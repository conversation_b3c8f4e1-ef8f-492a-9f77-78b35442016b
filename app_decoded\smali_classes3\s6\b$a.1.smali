.class public final Ls6/b$a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Ls6/b;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Ls6/b$a$a;
    }
.end annotation


# static fields
.field public static final synthetic a:Ls6/b$a;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, Ls6/b$a;

    .line 2
    .line 3
    invoke-direct {v0}, Ls6/b$a;-><init>()V

    .line 4
    .line 5
    .line 6
    sput-object v0, Ls6/b$a;->a:Ls6/b$a;

    .line 7
    .line 8
    return-void
.end method

.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
