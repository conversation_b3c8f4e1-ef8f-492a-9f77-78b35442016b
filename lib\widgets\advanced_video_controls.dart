import 'package:flutter/material.dart';
import 'package:flutter_vlc_player/flutter_vlc_player.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import '../models/video_models.dart';
import '../services/video_settings_manager.dart';
import '../services/playlist_manager.dart';

class AdvancedVideoControls extends StatefulWidget {
  final VlcPlayerController controller;
  final bool isVisible;
  final Function()? onToggleVisibility;
  final List<Chapter>? chapters;
  final List<VideoQuality>? qualities;
  final List<AudioTrack>? audioTracks;
  final List<SubtitleTrack>? subtitleTracks;

  const AdvancedVideoControls({
    super.key,
    required this.controller,
    required this.isVisible,
    this.onToggleVisibility,
    this.chapters,
    this.qualities,
    this.audioTracks,
    this.subtitleTracks,
  });

  @override
  State<AdvancedVideoControls> createState() => _AdvancedVideoControlsState();
}

class _AdvancedVideoControlsState extends State<AdvancedVideoControls> {
  final VideoSettingsManager _settings = VideoSettingsManager();
  final PlaylistManager _playlist = PlaylistManager();
  bool _showSpeedMenu = false;
  bool _showQualityMenu = false;
  bool _showAudioMenu = false;
  bool _showSubtitleMenu = false;
  bool _showChaptersMenu = false;

  @override
  Widget build(BuildContext context) {
    if (!widget.isVisible) return const SizedBox.shrink();

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.transparent,
            Colors.black.withOpacity(0.7),
          ],
        ),
      ),
      child: Column(
        children: [
          _buildTopControls(),
          const Spacer(),
          _buildBottomControls(),
        ],
      ),
    );
  }

  Widget _buildTopControls() {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            IconButton(
              onPressed: () => Navigator.pop(context),
              icon: const Icon(Icons.arrow_back, color: Colors.white),
            ),
            const Spacer(),
            _buildMenuButton(
              icon: FontAwesomeIcons.gauge,
              isActive: _showSpeedMenu,
              onPressed: () => setState(() => _showSpeedMenu = !_showSpeedMenu),
            ),
            const SizedBox(width: 8),
            _buildMenuButton(
              icon: FontAwesomeIcons.gear,
              isActive: _showQualityMenu,
              onPressed: () =>
                  setState(() => _showQualityMenu = !_showQualityMenu),
            ),
            const SizedBox(width: 8),
            _buildMenuButton(
              icon: FontAwesomeIcons.volumeHigh,
              isActive: _showAudioMenu,
              onPressed: () => setState(() => _showAudioMenu = !_showAudioMenu),
            ),
            const SizedBox(width: 8),
            _buildMenuButton(
              icon: FontAwesomeIcons.closedCaptioning,
              isActive: _showSubtitleMenu,
              onPressed: () =>
                  setState(() => _showSubtitleMenu = !_showSubtitleMenu),
            ),
            if (widget.chapters?.isNotEmpty == true) ...[
              const SizedBox(width: 8),
              _buildMenuButton(
                icon: FontAwesomeIcons.list,
                isActive: _showChaptersMenu,
                onPressed: () =>
                    setState(() => _showChaptersMenu = !_showChaptersMenu),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildBottomControls() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          if (_showSpeedMenu) _buildSpeedMenu(),
          if (_showQualityMenu) _buildQualityMenu(),
          if (_showAudioMenu) _buildAudioMenu(),
          if (_showSubtitleMenu) _buildSubtitleMenu(),
          if (_showChaptersMenu) _buildChaptersMenu(),
          const SizedBox(height: 16),
          _buildMainControls(),
          const SizedBox(height: 16),
          _buildProgressBar(),
        ],
      ),
    );
  }

  Widget _buildMenuButton({
    required IconData icon,
    required bool isActive,
    required VoidCallback onPressed,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: isActive ? Colors.blue : Colors.black54,
        borderRadius: BorderRadius.circular(8),
      ),
      child: IconButton(
        onPressed: onPressed,
        icon: Icon(icon, color: Colors.white, size: 20),
      ),
    );
  }

  Widget _buildSpeedMenu() {
    final speeds = [0.25, 0.5, 0.75, 1.0, 1.25, 1.5, 1.75, 2.0];

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.black87,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'سرعة التشغيل',
            style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            children: speeds.map((speed) {
              final isSelected = _settings.playbackSpeed == speed;
              return GestureDetector(
                onTap: () {
                  widget.controller.setPlaybackSpeed(speed);
                  _settings.setPlaybackSpeed(speed);
                  setState(() => _showSpeedMenu = false);
                },
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: isSelected ? Colors.blue : Colors.grey[700],
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    '${speed}x',
                    style: const TextStyle(color: Colors.white),
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildQualityMenu() {
    if (widget.qualities?.isEmpty != false) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.black87,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'جودة الفيديو',
            style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          ...widget.qualities!.map((quality) {
            final isSelected = _settings.videoQuality == quality.label;
            return ListTile(
              dense: true,
              title: Text(quality.label,
                  style: const TextStyle(color: Colors.white)),
              leading: Radio<String>(
                value: quality.label,
                groupValue: _settings.videoQuality,
                onChanged: (value) {
                  if (value != null) {
                    _settings.setVideoQuality(value);
                    // تغيير URL الفيديو هنا
                    setState(() => _showQualityMenu = false);
                  }
                },
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildAudioMenu() {
    if (widget.audioTracks?.isEmpty != false) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.black87,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'المسار الصوتي',
            style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          ...widget.audioTracks!.map((track) {
            return ListTile(
              dense: true,
              title: Text('${track.language} - ${track.name}',
                  style: const TextStyle(color: Colors.white)),
              onTap: () {
                widget.controller.setAudioTrack(track.id);
                setState(() => _showAudioMenu = false);
              },
            );
          }),
        ],
      ),
    );
  }

  Widget _buildSubtitleMenu() {
    if (widget.subtitleTracks?.isEmpty != false) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.black87,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'الترجمة',
            style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          ListTile(
            dense: true,
            title:
                const Text('بدون ترجمة', style: TextStyle(color: Colors.white)),
            onTap: () {
              widget.controller.setSpuTrack(-1);
              setState(() => _showSubtitleMenu = false);
            },
          ),
          ...widget.subtitleTracks!.map((track) {
            return ListTile(
              dense: true,
              title: Text('${track.language} - ${track.name}',
                  style: const TextStyle(color: Colors.white)),
              onTap: () {
                widget.controller.setSpuTrack(track.id);
                setState(() => _showSubtitleMenu = false);
              },
            );
          }),
        ],
      ),
    );
  }

  Widget _buildChaptersMenu() {
    if (widget.chapters?.isEmpty != false) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.black87,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'الفصول',
            style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          ...widget.chapters!.map((chapter) {
            return ListTile(
              dense: true,
              title: Text(chapter.title,
                  style: const TextStyle(color: Colors.white)),
              subtitle: Text(
                _formatDuration(chapter.time),
                style: const TextStyle(color: Colors.grey),
              ),
              onTap: () {
                widget.controller.seekTo(chapter.time);
                setState(() => _showChaptersMenu = false);
              },
            );
          }),
        ],
      ),
    );
  }

  Widget _buildMainControls() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // Previous
        IconButton(
          onPressed: _playlist.hasPrevious
              ? () {
                  _playlist.playPrevious();
                  // تشغيل الفيديو السابق
                }
              : null,
          icon: Icon(
            FontAwesomeIcons.backward,
            color: _playlist.hasPrevious ? Colors.white : Colors.grey,
            size: 20.sp,
          ),
        ),

        // Skip Backward
        IconButton(
          onPressed: () => _skipBackward(),
          icon: Icon(FontAwesomeIcons.rotateLeft,
              color: Colors.white, size: 20.sp),
        ),

        // Play/Pause
        IconButton(
          onPressed: () {
            if (widget.controller.value.isPlaying) {
              widget.controller.pause();
            } else {
              widget.controller.play();
            }
            setState(() {});
          },
          icon: Icon(
            widget.controller.value.isPlaying
                ? FontAwesomeIcons.pause
                : FontAwesomeIcons.play,
            color: Colors.white,
            size: 24.sp,
          ),
        ),

        // Skip Forward
        IconButton(
          onPressed: () => _skipForward(),
          icon: Icon(FontAwesomeIcons.rotateRight,
              color: Colors.white, size: 20.sp),
        ),

        // Next
        IconButton(
          onPressed: _playlist.hasNext
              ? () {
                  _playlist.playNext();
                  // تشغيل الفيديو التالي
                }
              : null,
          icon: Icon(
            FontAwesomeIcons.forward,
            color: _playlist.hasNext ? Colors.white : Colors.grey,
            size: 20.sp,
          ),
        ),
      ],
    );
  }

  Widget _buildProgressBar() {
    return StreamBuilder<Duration>(
      stream: widget.controller.onPositionChanged,
      builder: (context, snapshot) {
        final position = snapshot.data ?? Duration.zero;
        final duration = widget.controller.value.duration;

        return Column(
          children: [
            Slider(
              value: duration.inMilliseconds > 0
                  ? position.inMilliseconds / duration.inMilliseconds
                  : 0.0,
              onChanged: (value) {
                final newPosition = Duration(
                  milliseconds: (value * duration.inMilliseconds).round(),
                );
                widget.controller.seekTo(newPosition);
              },
              activeColor: Colors.blue,
              inactiveColor: Colors.white30,
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    _formatDuration(position),
                    style: const TextStyle(color: Colors.white, fontSize: 12),
                  ),
                  Text(
                    _formatDuration(duration),
                    style: const TextStyle(color: Colors.white, fontSize: 12),
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  void _skipForward() {
    final currentPosition = widget.controller.value.position;
    final newPosition =
        currentPosition + Duration(seconds: _settings.skipDuration);
    widget.controller.seekTo(newPosition);
  }

  void _skipBackward() {
    final currentPosition = widget.controller.value.position;
    final newPosition =
        currentPosition - Duration(seconds: _settings.skipDuration);
    widget.controller
        .seekTo(newPosition.isNegative ? Duration.zero : newPosition);
  }

  String _formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    }
  }
}
