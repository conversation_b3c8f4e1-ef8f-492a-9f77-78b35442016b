.class public final LX5/U0;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LD5/g$b;
.implements LD5/g$c;


# static fields
.field public static final a:LX5/U0;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, LX5/U0;

    .line 2
    .line 3
    invoke-direct {v0}, LX5/U0;-><init>()V

    .line 4
    .line 5
    .line 6
    sput-object v0, LX5/U0;->a:LX5/U0;

    .line 7
    .line 8
    return-void
.end method

.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public D0(LD5/g;)LD5/g;
    .locals 0

    .line 1
    invoke-static {p0, p1}, LD5/g$b$a;->d(LD5/g$b;LD5/g;)LD5/g;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    return-object p1
.end method

.method public P(Ljava/lang/Object;LM5/o;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, LD5/g$b$a;->a(LD5/g$b;Ljava/lang/Object;LM5/o;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    return-object p1
.end method

.method public b(LD5/g$c;)LD5/g$b;
    .locals 0

    .line 1
    invoke-static {p0, p1}, LD5/g$b$a;->b(LD5/g$b;LD5/g$c;)LD5/g$b;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    return-object p1
.end method

.method public getKey()LD5/g$c;
    .locals 0

    .line 1
    return-object p0
.end method

.method public i0(LD5/g$c;)LD5/g;
    .locals 0

    .line 1
    invoke-static {p0, p1}, LD5/g$b$a;->c(LD5/g$b;LD5/g$c;)LD5/g;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    return-object p1
.end method
