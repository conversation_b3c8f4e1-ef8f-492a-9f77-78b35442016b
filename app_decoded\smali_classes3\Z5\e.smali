.class public abstract LZ5/e;
.super LX5/a;
.source "SourceFile"

# interfaces
.implements LZ5/d;


# instance fields
.field public final d:LZ5/d;


# direct methods
.method public constructor <init>(LD5/g;LZ5/d;ZZ)V
    .locals 0

    .line 1
    invoke-direct {p0, p1, p3, p4}, LX5/a;-><init>(LD5/g;ZZ)V

    .line 2
    .line 3
    .line 4
    iput-object p2, p0, LZ5/e;->d:LZ5/d;

    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public L(Ljava/lang/Throwable;)V
    .locals 2

    .line 1
    const/4 v0, 0x0

    .line 2
    const/4 v1, 0x1

    .line 3
    invoke-static {p0, p1, v0, v1, v0}, LX5/A0;->M0(LX5/A0;Ljava/lang/Throwable;Ljava/lang/String;ILjava/lang/Object;)Ljava/util/concurrent/CancellationException;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    iget-object v0, p0, LZ5/e;->d:LZ5/d;

    .line 8
    .line 9
    invoke-interface {v0, p1}, LZ5/t;->c(Ljava/util/concurrent/CancellationException;)V

    .line 10
    .line 11
    .line 12
    invoke-virtual {p0, p1}, LX5/A0;->I(Ljava/lang/Throwable;)Z

    .line 13
    .line 14
    .line 15
    return-void
.end method

.method public final X0()LZ5/d;
    .locals 0

    .line 1
    return-object p0
.end method

.method public final Y0()LZ5/d;
    .locals 1

    .line 1
    iget-object v0, p0, LZ5/e;->d:LZ5/d;

    .line 2
    .line 3
    return-object v0
.end method

.method public final c(Ljava/util/concurrent/CancellationException;)V
    .locals 2

    .line 1
    invoke-virtual {p0}, LX5/A0;->isCancelled()Z

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    if-eqz v0, :cond_0

    .line 6
    .line 7
    return-void

    .line 8
    :cond_0
    if-nez p1, :cond_1

    .line 9
    .line 10
    new-instance p1, LX5/t0;

    .line 11
    .line 12
    invoke-static {p0}, LX5/A0;->z(LX5/A0;)Ljava/lang/String;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    const/4 v1, 0x0

    .line 17
    invoke-direct {p1, v0, v1, p0}, LX5/t0;-><init>(Ljava/lang/String;Ljava/lang/Throwable;LX5/s0;)V

    .line 18
    .line 19
    .line 20
    :cond_1
    invoke-virtual {p0, p1}, LZ5/e;->L(Ljava/lang/Throwable;)V

    .line 21
    .line 22
    .line 23
    return-void
.end method

.method public g()Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, LZ5/e;->d:LZ5/d;

    .line 2
    .line 3
    invoke-interface {v0}, LZ5/t;->g()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public i(LD5/d;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, LZ5/e;->d:LZ5/d;

    .line 2
    .line 3
    invoke-interface {v0, p1}, LZ5/t;->i(LD5/d;)Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    return-object p1
.end method

.method public iterator()LZ5/f;
    .locals 1

    .line 1
    iget-object v0, p0, LZ5/e;->d:LZ5/d;

    .line 2
    .line 3
    invoke-interface {v0}, LZ5/t;->iterator()LZ5/f;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    return-object v0
.end method

.method public n(Ljava/lang/Object;LD5/d;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, LZ5/e;->d:LZ5/d;

    .line 2
    .line 3
    invoke-interface {v0, p1, p2}, LZ5/u;->n(Ljava/lang/Object;LD5/d;)Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    return-object p1
.end method

.method public o(Ljava/lang/Throwable;)Z
    .locals 1

    .line 1
    iget-object v0, p0, LZ5/e;->d:LZ5/d;

    .line 2
    .line 3
    invoke-interface {v0, p1}, LZ5/u;->o(Ljava/lang/Throwable;)Z

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    return p1
.end method

.method public q(LM5/k;)V
    .locals 1

    .line 1
    iget-object v0, p0, LZ5/e;->d:LZ5/d;

    .line 2
    .line 3
    invoke-interface {v0, p1}, LZ5/u;->q(LM5/k;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public u(LD5/d;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, LZ5/e;->d:LZ5/d;

    .line 2
    .line 3
    invoke-interface {v0, p1}, LZ5/t;->u(LD5/d;)Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    invoke-static {}, LE5/b;->e()Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    return-object p1
.end method

.method public w(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object v0, p0, LZ5/e;->d:LZ5/d;

    .line 2
    .line 3
    invoke-interface {v0, p1}, LZ5/u;->w(Ljava/lang/Object;)Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    return-object p1
.end method

.method public x()Z
    .locals 1

    .line 1
    iget-object v0, p0, LZ5/e;->d:LZ5/d;

    .line 2
    .line 3
    invoke-interface {v0}, LZ5/u;->x()Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    return v0
.end method
