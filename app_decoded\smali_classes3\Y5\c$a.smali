.class public final LY5/c$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Runnable;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = LY5/c;->z(JLX5/m;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation


# instance fields
.field public final synthetic a:LX5/m;

.field public final synthetic b:LY5/c;


# direct methods
.method public constructor <init>(LX5/m;LY5/c;)V
    .locals 0

    .line 1
    iput-object p1, p0, LY5/c$a;->a:LX5/m;

    .line 2
    .line 3
    iput-object p2, p0, LY5/c$a;->b:LY5/c;

    .line 4
    .line 5
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 6
    .line 7
    .line 8
    return-void
.end method


# virtual methods
.method public final run()V
    .locals 3

    .line 1
    iget-object v0, p0, LY5/c$a;->a:LX5/m;

    .line 2
    .line 3
    iget-object v1, p0, LY5/c$a;->b:LY5/c;

    .line 4
    .line 5
    sget-object v2, Ly5/I;->a:Ly5/I;

    .line 6
    .line 7
    invoke-interface {v0, v1, v2}, LX5/m;->d(LX5/G;Ljava/lang/Object;)V

    .line 8
    .line 9
    .line 10
    return-void
.end method
