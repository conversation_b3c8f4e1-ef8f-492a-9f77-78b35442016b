.class public final Lw6/c;
.super Lw6/a;
.source "SourceFile"


# instance fields
.field public final synthetic e:Ljava/lang/String;

.field public final synthetic f:Z

.field public final synthetic g:L<PERSON><PERSON>/jvm/functions/Function0;


# direct methods
.method public constructor <init>(Lja<PERSON>/lang/String;ZLkotlin/jvm/functions/Function0;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lw6/c;->e:Ljava/lang/String;

    .line 2
    .line 3
    iput-boolean p2, p0, Lw6/c;->f:Z

    .line 4
    .line 5
    iput-object p3, p0, Lw6/c;->g:<PERSON><PERSON><PERSON>/jvm/functions/Function0;

    .line 6
    .line 7
    invoke-direct {p0, p1, p2}, Lw6/a;-><init>(Ljava/lang/String;Z)V

    .line 8
    .line 9
    .line 10
    return-void
.end method


# virtual methods
.method public f()J
    .locals 2

    .line 1
    iget-object v0, p0, Lw6/c;->g:<PERSON><PERSON><PERSON>/jvm/functions/Function0;

    .line 2
    .line 3
    invoke-interface {v0}, L<PERSON>lin/jvm/functions/Function0;->invoke()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    const-wide/16 v0, -0x1

    .line 7
    .line 8
    return-wide v0
.end method
