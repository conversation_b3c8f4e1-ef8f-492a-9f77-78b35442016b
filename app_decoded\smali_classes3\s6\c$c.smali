.class public final Ls6/c$c;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Ls6/c;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "c"
.end annotation

.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Ls6/c$c$a;
    }
.end annotation


# static fields
.field public static final k:Ls6/c$c$a;

.field public static final l:Ljava/lang/String;

.field public static final m:Ljava/lang/String;


# instance fields
.field public final a:Ls6/u;

.field public final b:Ls6/t;

.field public final c:Ljava/lang/String;

.field public final d:Ls6/y;

.field public final e:I

.field public final f:Ljava/lang/String;

.field public final g:Ls6/t;

.field public final h:Ls6/s;

.field public final i:J

.field public final j:J


# direct methods
.method static constructor <clinit>()V
    .locals 3

    .line 1
    new-instance v0, Ls6/c$c$a;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, v1}, Ls6/c$c$a;-><init>(Lkotlin/jvm/internal/j;)V

    .line 5
    .line 6
    .line 7
    sput-object v0, Ls6/c$c;->k:Ls6/c$c$a;

    .line 8
    .line 9
    sget-object v0, LC6/j;->a:LC6/j$a;

    .line 10
    .line 11
    invoke-virtual {v0}, LC6/j$a;->g()LC6/j;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    invoke-virtual {v1}, LC6/j;->g()Ljava/lang/String;

    .line 16
    .line 17
    .line 18
    move-result-object v1

    .line 19
    const-string v2, "-Sent-Millis"

    .line 20
    .line 21
    invoke-static {v1, v2}, Lkotlin/jvm/internal/r;->n(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/String;

    .line 22
    .line 23
    .line 24
    move-result-object v1

    .line 25
    sput-object v1, Ls6/c$c;->l:Ljava/lang/String;

    .line 26
    .line 27
    invoke-virtual {v0}, LC6/j$a;->g()LC6/j;

    .line 28
    .line 29
    .line 30
    move-result-object v0

    .line 31
    invoke-virtual {v0}, LC6/j;->g()Ljava/lang/String;

    .line 32
    .line 33
    .line 34
    move-result-object v0

    .line 35
    const-string v1, "-Received-Millis"

    .line 36
    .line 37
    invoke-static {v0, v1}, Lkotlin/jvm/internal/r;->n(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/String;

    .line 38
    .line 39
    .line 40
    move-result-object v0

    .line 41
    sput-object v0, Ls6/c$c;->m:Ljava/lang/String;

    .line 42
    .line 43
    return-void
.end method

.method public constructor <init>(LG6/Z;)V
    .locals 8

    .line 1
    const-string v0, "rawSource"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/r;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    :try_start_0
    invoke-static {p1}, LG6/L;->d(LG6/Z;)LG6/g;

    move-result-object v0

    .line 4
    invoke-interface {v0}, LG6/g;->l0()Ljava/lang/String;

    move-result-object v1

    .line 5
    sget-object v2, Ls6/u;->k:Ls6/u$b;

    invoke-virtual {v2, v1}, Ls6/u$b;->f(Ljava/lang/String;)Ls6/u;

    move-result-object v2

    if-eqz v2, :cond_7

    iput-object v2, p0, Ls6/c$c;->a:Ls6/u;

    .line 6
    invoke-interface {v0}, LG6/g;->l0()Ljava/lang/String;

    move-result-object v1

    iput-object v1, p0, Ls6/c$c;->c:Ljava/lang/String;

    .line 7
    new-instance v1, Ls6/t$a;

    invoke-direct {v1}, Ls6/t$a;-><init>()V

    .line 8
    sget-object v2, Ls6/c;->g:Ls6/c$b;

    invoke-virtual {v2, v0}, Ls6/c$b;->c(LG6/g;)I

    move-result v2

    const/4 v3, 0x0

    move v4, v3

    :goto_0
    if-ge v4, v2, :cond_0

    add-int/lit8 v4, v4, 0x1

    .line 9
    invoke-interface {v0}, LG6/g;->l0()Ljava/lang/String;

    move-result-object v5

    invoke-virtual {v1, v5}, Ls6/t$a;->b(Ljava/lang/String;)Ls6/t$a;

    goto :goto_0

    :catchall_0
    move-exception v0

    goto/16 :goto_6

    .line 10
    :cond_0
    invoke-virtual {v1}, Ls6/t$a;->d()Ls6/t;

    move-result-object v1

    iput-object v1, p0, Ls6/c$c;->b:Ls6/t;

    .line 11
    sget-object v1, Ly6/k;->d:Ly6/k$a;

    invoke-interface {v0}, LG6/g;->l0()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Ly6/k$a;->a(Ljava/lang/String;)Ly6/k;

    move-result-object v1

    .line 12
    iget-object v2, v1, Ly6/k;->a:Ls6/y;

    iput-object v2, p0, Ls6/c$c;->d:Ls6/y;

    .line 13
    iget v2, v1, Ly6/k;->b:I

    iput v2, p0, Ls6/c$c;->e:I

    .line 14
    iget-object v1, v1, Ly6/k;->c:Ljava/lang/String;

    iput-object v1, p0, Ls6/c$c;->f:Ljava/lang/String;

    .line 15
    new-instance v1, Ls6/t$a;

    invoke-direct {v1}, Ls6/t$a;-><init>()V

    .line 16
    sget-object v2, Ls6/c;->g:Ls6/c$b;

    invoke-virtual {v2, v0}, Ls6/c$b;->c(LG6/g;)I

    move-result v2

    :goto_1
    if-ge v3, v2, :cond_1

    add-int/lit8 v3, v3, 0x1

    .line 17
    invoke-interface {v0}, LG6/g;->l0()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v1, v4}, Ls6/t$a;->b(Ljava/lang/String;)Ls6/t$a;

    goto :goto_1

    .line 18
    :cond_1
    sget-object v2, Ls6/c$c;->l:Ljava/lang/String;

    invoke-virtual {v1, v2}, Ls6/t$a;->e(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v3

    .line 19
    sget-object v4, Ls6/c$c;->m:Ljava/lang/String;

    invoke-virtual {v1, v4}, Ls6/t$a;->e(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v5

    .line 20
    invoke-virtual {v1, v2}, Ls6/t$a;->g(Ljava/lang/String;)Ls6/t$a;

    .line 21
    invoke-virtual {v1, v4}, Ls6/t$a;->g(Ljava/lang/String;)Ls6/t$a;

    const-wide/16 v6, 0x0

    if-nez v3, :cond_2

    move-wide v2, v6

    goto :goto_2

    .line 22
    :cond_2
    invoke-static {v3}, Ljava/lang/Long;->parseLong(Ljava/lang/String;)J

    move-result-wide v2

    :goto_2
    iput-wide v2, p0, Ls6/c$c;->i:J

    if-nez v5, :cond_3

    goto :goto_3

    .line 23
    :cond_3
    invoke-static {v5}, Ljava/lang/Long;->parseLong(Ljava/lang/String;)J

    move-result-wide v6

    :goto_3
    iput-wide v6, p0, Ls6/c$c;->j:J

    .line 24
    invoke-virtual {v1}, Ls6/t$a;->d()Ls6/t;

    move-result-object v1

    iput-object v1, p0, Ls6/c$c;->g:Ls6/t;

    .line 25
    invoke-virtual {p0}, Ls6/c$c;->a()Z

    move-result v1

    const/4 v2, 0x0

    if-eqz v1, :cond_6

    .line 26
    invoke-interface {v0}, LG6/g;->l0()Ljava/lang/String;

    move-result-object v1

    .line 27
    invoke-interface {v1}, Ljava/lang/CharSequence;->length()I

    move-result v3

    if-gtz v3, :cond_5

    .line 28
    invoke-interface {v0}, LG6/g;->l0()Ljava/lang/String;

    move-result-object v1

    .line 29
    sget-object v3, Ls6/i;->b:Ls6/i$b;

    invoke-virtual {v3, v1}, Ls6/i$b;->b(Ljava/lang/String;)Ls6/i;

    move-result-object v1

    .line 30
    invoke-virtual {p0, v0}, Ls6/c$c;->c(LG6/g;)Ljava/util/List;

    move-result-object v3

    .line 31
    invoke-virtual {p0, v0}, Ls6/c$c;->c(LG6/g;)Ljava/util/List;

    move-result-object v4

    .line 32
    invoke-interface {v0}, LG6/g;->E()Z

    move-result v5

    if-nez v5, :cond_4

    .line 33
    sget-object v5, Ls6/E;->b:Ls6/E$a;

    invoke-interface {v0}, LG6/g;->l0()Ljava/lang/String;

    move-result-object v0

    invoke-virtual {v5, v0}, Ls6/E$a;->a(Ljava/lang/String;)Ls6/E;

    move-result-object v0

    goto :goto_4

    .line 34
    :cond_4
    sget-object v0, Ls6/E;->g:Ls6/E;

    .line 35
    :goto_4
    sget-object v5, Ls6/s;->e:Ls6/s$a;

    invoke-virtual {v5, v0, v1, v3, v4}, Ls6/s$a;->b(Ls6/E;Ls6/i;Ljava/util/List;Ljava/util/List;)Ls6/s;

    move-result-object v0

    iput-object v0, p0, Ls6/c$c;->h:Ls6/s;

    goto :goto_5

    .line 36
    :cond_5
    new-instance v0, Ljava/io/IOException;

    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    const-string v3, "expected \"\" but was \""

    invoke-virtual {v2, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/16 v1, 0x22

    invoke-virtual {v2, v1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-direct {v0, v1}, Ljava/io/IOException;-><init>(Ljava/lang/String;)V

    throw v0

    .line 37
    :cond_6
    iput-object v2, p0, Ls6/c$c;->h:Ls6/s;

    .line 38
    :goto_5
    sget-object v0, Ly5/I;->a:Ly5/I;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 39
    invoke-static {p1, v2}, LK5/b;->a(Ljava/io/Closeable;Ljava/lang/Throwable;)V

    return-void

    .line 40
    :cond_7
    :try_start_1
    new-instance v0, Ljava/io/IOException;

    const-string v2, "Cache corruption for "

    invoke-static {v2, v1}, Lkotlin/jvm/internal/r;->n(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/String;

    move-result-object v1

    invoke-direct {v0, v1}, Ljava/io/IOException;-><init>(Ljava/lang/String;)V

    .line 41
    sget-object v1, LC6/j;->a:LC6/j$a;

    invoke-virtual {v1}, LC6/j$a;->g()LC6/j;

    move-result-object v1

    const-string v2, "cache corruption"

    const/4 v3, 0x5

    invoke-virtual {v1, v2, v3, v0}, LC6/j;->k(Ljava/lang/String;ILjava/lang/Throwable;)V

    .line 42
    throw v0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 43
    :goto_6
    :try_start_2
    throw v0
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    :catchall_1
    move-exception v1

    invoke-static {p1, v0}, LK5/b;->a(Ljava/io/Closeable;Ljava/lang/Throwable;)V

    throw v1
.end method

.method public constructor <init>(Ls6/B;)V
    .locals 2

    .line 44
    const-string v0, "response"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/r;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 45
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 46
    invoke-virtual {p1}, Ls6/B;->h0()Ls6/z;

    move-result-object v0

    invoke-virtual {v0}, Ls6/z;->j()Ls6/u;

    move-result-object v0

    iput-object v0, p0, Ls6/c$c;->a:Ls6/u;

    .line 47
    sget-object v0, Ls6/c;->g:Ls6/c$b;

    invoke-virtual {v0, p1}, Ls6/c$b;->f(Ls6/B;)Ls6/t;

    move-result-object v0

    iput-object v0, p0, Ls6/c$c;->b:Ls6/t;

    .line 48
    invoke-virtual {p1}, Ls6/B;->h0()Ls6/z;

    move-result-object v0

    invoke-virtual {v0}, Ls6/z;->h()Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Ls6/c$c;->c:Ljava/lang/String;

    .line 49
    invoke-virtual {p1}, Ls6/B;->Q()Ls6/y;

    move-result-object v0

    iput-object v0, p0, Ls6/c$c;->d:Ls6/y;

    .line 50
    invoke-virtual {p1}, Ls6/B;->h()I

    move-result v0

    iput v0, p0, Ls6/c$c;->e:I

    .line 51
    invoke-virtual {p1}, Ls6/B;->A()Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Ls6/c$c;->f:Ljava/lang/String;

    .line 52
    invoke-virtual {p1}, Ls6/B;->v()Ls6/t;

    move-result-object v0

    iput-object v0, p0, Ls6/c$c;->g:Ls6/t;

    .line 53
    invoke-virtual {p1}, Ls6/B;->l()Ls6/s;

    move-result-object v0

    iput-object v0, p0, Ls6/c$c;->h:Ls6/s;

    .line 54
    invoke-virtual {p1}, Ls6/B;->i0()J

    move-result-wide v0

    iput-wide v0, p0, Ls6/c$c;->i:J

    .line 55
    invoke-virtual {p1}, Ls6/B;->X()J

    move-result-wide v0

    iput-wide v0, p0, Ls6/c$c;->j:J

    return-void
.end method


# virtual methods
.method public final a()Z
    .locals 2

    .line 1
    iget-object v0, p0, Ls6/c$c;->a:Ls6/u;

    .line 2
    .line 3
    invoke-virtual {v0}, Ls6/u;->q()Ljava/lang/String;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    const-string v1, "https"

    .line 8
    .line 9
    invoke-static {v0, v1}, Lkotlin/jvm/internal/r;->b(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    return v0
.end method

.method public final b(Ls6/z;Ls6/B;)Z
    .locals 2

    .line 1
    const-string v0, "request"

    .line 2
    .line 3
    invoke-static {p1, v0}, Lkotlin/jvm/internal/r;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    const-string v0, "response"

    .line 7
    .line 8
    invoke-static {p2, v0}, Lkotlin/jvm/internal/r;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 9
    .line 10
    .line 11
    iget-object v0, p0, Ls6/c$c;->a:Ls6/u;

    .line 12
    .line 13
    invoke-virtual {p1}, Ls6/z;->j()Ls6/u;

    .line 14
    .line 15
    .line 16
    move-result-object v1

    .line 17
    invoke-static {v0, v1}, Lkotlin/jvm/internal/r;->b(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 18
    .line 19
    .line 20
    move-result v0

    .line 21
    if-eqz v0, :cond_0

    .line 22
    .line 23
    iget-object v0, p0, Ls6/c$c;->c:Ljava/lang/String;

    .line 24
    .line 25
    invoke-virtual {p1}, Ls6/z;->h()Ljava/lang/String;

    .line 26
    .line 27
    .line 28
    move-result-object v1

    .line 29
    invoke-static {v0, v1}, Lkotlin/jvm/internal/r;->b(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 30
    .line 31
    .line 32
    move-result v0

    .line 33
    if-eqz v0, :cond_0

    .line 34
    .line 35
    sget-object v0, Ls6/c;->g:Ls6/c$b;

    .line 36
    .line 37
    iget-object v1, p0, Ls6/c$c;->b:Ls6/t;

    .line 38
    .line 39
    invoke-virtual {v0, p2, v1, p1}, Ls6/c$b;->g(Ls6/B;Ls6/t;Ls6/z;)Z

    .line 40
    .line 41
    .line 42
    move-result p1

    .line 43
    if-eqz p1, :cond_0

    .line 44
    .line 45
    const/4 p1, 0x1

    .line 46
    goto :goto_0

    .line 47
    :cond_0
    const/4 p1, 0x0

    .line 48
    :goto_0
    return p1
.end method

.method public final c(LG6/g;)Ljava/util/List;
    .locals 7

    .line 1
    sget-object v0, Ls6/c;->g:Ls6/c$b;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, Ls6/c$b;->c(LG6/g;)I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    const/4 v1, -0x1

    .line 8
    if-ne v0, v1, :cond_0

    .line 9
    .line 10
    invoke-static {}, Lz5/o;->k()Ljava/util/List;

    .line 11
    .line 12
    .line 13
    move-result-object p1

    .line 14
    return-object p1

    .line 15
    :cond_0
    :try_start_0
    const-string v1, "X.509"

    .line 16
    .line 17
    invoke-static {v1}, Ljava/security/cert/CertificateFactory;->getInstance(Ljava/lang/String;)Ljava/security/cert/CertificateFactory;

    .line 18
    .line 19
    .line 20
    move-result-object v1

    .line 21
    new-instance v2, Ljava/util/ArrayList;

    .line 22
    .line 23
    invoke-direct {v2, v0}, Ljava/util/ArrayList;-><init>(I)V

    .line 24
    .line 25
    .line 26
    const/4 v3, 0x0

    .line 27
    :goto_0
    if-ge v3, v0, :cond_1

    .line 28
    .line 29
    add-int/lit8 v3, v3, 0x1

    .line 30
    .line 31
    invoke-interface {p1}, LG6/g;->l0()Ljava/lang/String;

    .line 32
    .line 33
    .line 34
    move-result-object v4

    .line 35
    new-instance v5, LG6/e;

    .line 36
    .line 37
    invoke-direct {v5}, LG6/e;-><init>()V

    .line 38
    .line 39
    .line 40
    sget-object v6, LG6/h;->d:LG6/h$a;

    .line 41
    .line 42
    invoke-virtual {v6, v4}, LG6/h$a;->a(Ljava/lang/String;)LG6/h;

    .line 43
    .line 44
    .line 45
    move-result-object v4

    .line 46
    invoke-static {v4}, Lkotlin/jvm/internal/r;->c(Ljava/lang/Object;)V

    .line 47
    .line 48
    .line 49
    invoke-virtual {v5, v4}, LG6/e;->D0(LG6/h;)LG6/e;

    .line 50
    .line 51
    .line 52
    invoke-virtual {v5}, LG6/e;->N0()Ljava/io/InputStream;

    .line 53
    .line 54
    .line 55
    move-result-object v4

    .line 56
    invoke-virtual {v1, v4}, Ljava/security/cert/CertificateFactory;->generateCertificate(Ljava/io/InputStream;)Ljava/security/cert/Certificate;

    .line 57
    .line 58
    .line 59
    move-result-object v4

    .line 60
    invoke-virtual {v2, v4}, Ljava/util/ArrayList;->add(Ljava/lang/Object;)Z
    :try_end_0
    .catch Ljava/security/cert/CertificateException; {:try_start_0 .. :try_end_0} :catch_0

    .line 61
    .line 62
    .line 63
    goto :goto_0

    .line 64
    :catch_0
    move-exception p1

    .line 65
    goto :goto_1

    .line 66
    :cond_1
    return-object v2

    .line 67
    :goto_1
    new-instance v0, Ljava/io/IOException;

    .line 68
    .line 69
    invoke-virtual {p1}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    .line 70
    .line 71
    .line 72
    move-result-object p1

    .line 73
    invoke-direct {v0, p1}, Ljava/io/IOException;-><init>(Ljava/lang/String;)V

    .line 74
    .line 75
    .line 76
    throw v0
.end method

.method public final d(Lv6/d$d;)Ls6/B;
    .locals 5

    .line 1
    const-string v0, "snapshot"

    .line 2
    .line 3
    invoke-static {p1, v0}, Lkotlin/jvm/internal/r;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Ls6/c$c;->g:Ls6/t;

    .line 7
    .line 8
    const-string v1, "Content-Type"

    .line 9
    .line 10
    invoke-virtual {v0, v1}, Ls6/t;->a(Ljava/lang/String;)Ljava/lang/String;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    iget-object v1, p0, Ls6/c$c;->g:Ls6/t;

    .line 15
    .line 16
    const-string v2, "Content-Length"

    .line 17
    .line 18
    invoke-virtual {v1, v2}, Ls6/t;->a(Ljava/lang/String;)Ljava/lang/String;

    .line 19
    .line 20
    .line 21
    move-result-object v1

    .line 22
    new-instance v2, Ls6/z$a;

    .line 23
    .line 24
    invoke-direct {v2}, Ls6/z$a;-><init>()V

    .line 25
    .line 26
    .line 27
    iget-object v3, p0, Ls6/c$c;->a:Ls6/u;

    .line 28
    .line 29
    invoke-virtual {v2, v3}, Ls6/z$a;->o(Ls6/u;)Ls6/z$a;

    .line 30
    .line 31
    .line 32
    move-result-object v2

    .line 33
    iget-object v3, p0, Ls6/c$c;->c:Ljava/lang/String;

    .line 34
    .line 35
    const/4 v4, 0x0

    .line 36
    invoke-virtual {v2, v3, v4}, Ls6/z$a;->g(Ljava/lang/String;Ls6/A;)Ls6/z$a;

    .line 37
    .line 38
    .line 39
    move-result-object v2

    .line 40
    iget-object v3, p0, Ls6/c$c;->b:Ls6/t;

    .line 41
    .line 42
    invoke-virtual {v2, v3}, Ls6/z$a;->f(Ls6/t;)Ls6/z$a;

    .line 43
    .line 44
    .line 45
    move-result-object v2

    .line 46
    invoke-virtual {v2}, Ls6/z$a;->b()Ls6/z;

    .line 47
    .line 48
    .line 49
    move-result-object v2

    .line 50
    new-instance v3, Ls6/B$a;

    .line 51
    .line 52
    invoke-direct {v3}, Ls6/B$a;-><init>()V

    .line 53
    .line 54
    .line 55
    invoke-virtual {v3, v2}, Ls6/B$a;->s(Ls6/z;)Ls6/B$a;

    .line 56
    .line 57
    .line 58
    move-result-object v2

    .line 59
    iget-object v3, p0, Ls6/c$c;->d:Ls6/y;

    .line 60
    .line 61
    invoke-virtual {v2, v3}, Ls6/B$a;->q(Ls6/y;)Ls6/B$a;

    .line 62
    .line 63
    .line 64
    move-result-object v2

    .line 65
    iget v3, p0, Ls6/c$c;->e:I

    .line 66
    .line 67
    invoke-virtual {v2, v3}, Ls6/B$a;->g(I)Ls6/B$a;

    .line 68
    .line 69
    .line 70
    move-result-object v2

    .line 71
    iget-object v3, p0, Ls6/c$c;->f:Ljava/lang/String;

    .line 72
    .line 73
    invoke-virtual {v2, v3}, Ls6/B$a;->n(Ljava/lang/String;)Ls6/B$a;

    .line 74
    .line 75
    .line 76
    move-result-object v2

    .line 77
    iget-object v3, p0, Ls6/c$c;->g:Ls6/t;

    .line 78
    .line 79
    invoke-virtual {v2, v3}, Ls6/B$a;->l(Ls6/t;)Ls6/B$a;

    .line 80
    .line 81
    .line 82
    move-result-object v2

    .line 83
    new-instance v3, Ls6/c$a;

    .line 84
    .line 85
    invoke-direct {v3, p1, v0, v1}, Ls6/c$a;-><init>(Lv6/d$d;Ljava/lang/String;Ljava/lang/String;)V

    .line 86
    .line 87
    .line 88
    invoke-virtual {v2, v3}, Ls6/B$a;->b(Ls6/C;)Ls6/B$a;

    .line 89
    .line 90
    .line 91
    move-result-object p1

    .line 92
    iget-object v0, p0, Ls6/c$c;->h:Ls6/s;

    .line 93
    .line 94
    invoke-virtual {p1, v0}, Ls6/B$a;->j(Ls6/s;)Ls6/B$a;

    .line 95
    .line 96
    .line 97
    move-result-object p1

    .line 98
    iget-wide v0, p0, Ls6/c$c;->i:J

    .line 99
    .line 100
    invoke-virtual {p1, v0, v1}, Ls6/B$a;->t(J)Ls6/B$a;

    .line 101
    .line 102
    .line 103
    move-result-object p1

    .line 104
    iget-wide v0, p0, Ls6/c$c;->j:J

    .line 105
    .line 106
    invoke-virtual {p1, v0, v1}, Ls6/B$a;->r(J)Ls6/B$a;

    .line 107
    .line 108
    .line 109
    move-result-object p1

    .line 110
    invoke-virtual {p1}, Ls6/B$a;->c()Ls6/B;

    .line 111
    .line 112
    .line 113
    move-result-object p1

    .line 114
    return-object p1
.end method

.method public final e(LG6/f;Ljava/util/List;)V
    .locals 8

    .line 1
    :try_start_0
    invoke-interface {p2}, Ljava/util/List;->size()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    int-to-long v0, v0

    .line 6
    invoke-interface {p1, v0, v1}, LG6/f;->J0(J)LG6/f;

    .line 7
    .line 8
    .line 9
    move-result-object v0

    .line 10
    const/16 v1, 0xa

    .line 11
    .line 12
    invoke-interface {v0, v1}, LG6/f;->F(I)LG6/f;

    .line 13
    .line 14
    .line 15
    invoke-interface {p2}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    .line 16
    .line 17
    .line 18
    move-result-object p2

    .line 19
    :goto_0
    invoke-interface {p2}, Ljava/util/Iterator;->hasNext()Z

    .line 20
    .line 21
    .line 22
    move-result v0

    .line 23
    if-eqz v0, :cond_0

    .line 24
    .line 25
    invoke-interface {p2}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 26
    .line 27
    .line 28
    move-result-object v0

    .line 29
    check-cast v0, Ljava/security/cert/Certificate;

    .line 30
    .line 31
    invoke-virtual {v0}, Ljava/security/cert/Certificate;->getEncoded()[B

    .line 32
    .line 33
    .line 34
    move-result-object v3

    .line 35
    sget-object v2, LG6/h;->d:LG6/h$a;

    .line 36
    .line 37
    const-string v0, "bytes"

    .line 38
    .line 39
    invoke-static {v3, v0}, Lkotlin/jvm/internal/r;->e(Ljava/lang/Object;Ljava/lang/String;)V

    .line 40
    .line 41
    .line 42
    const/4 v6, 0x3

    .line 43
    const/4 v7, 0x0

    .line 44
    const/4 v4, 0x0

    .line 45
    const/4 v5, 0x0

    .line 46
    invoke-static/range {v2 .. v7}, LG6/h$a;->f(LG6/h$a;[BIIILjava/lang/Object;)LG6/h;

    .line 47
    .line 48
    .line 49
    move-result-object v0

    .line 50
    invoke-virtual {v0}, LG6/h;->a()Ljava/lang/String;

    .line 51
    .line 52
    .line 53
    move-result-object v0

    .line 54
    invoke-interface {p1, v0}, LG6/f;->W(Ljava/lang/String;)LG6/f;

    .line 55
    .line 56
    .line 57
    move-result-object v0

    .line 58
    invoke-interface {v0, v1}, LG6/f;->F(I)LG6/f;
    :try_end_0
    .catch Ljava/security/cert/CertificateEncodingException; {:try_start_0 .. :try_end_0} :catch_0

    .line 59
    .line 60
    .line 61
    goto :goto_0

    .line 62
    :catch_0
    move-exception p1

    .line 63
    goto :goto_1

    .line 64
    :cond_0
    return-void

    .line 65
    :goto_1
    new-instance p2, Ljava/io/IOException;

    .line 66
    .line 67
    invoke-virtual {p1}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    .line 68
    .line 69
    .line 70
    move-result-object p1

    .line 71
    invoke-direct {p2, p1}, Ljava/io/IOException;-><init>(Ljava/lang/String;)V

    .line 72
    .line 73
    .line 74
    throw p2
.end method

.method public final f(Lv6/d$b;)V
    .locals 7

    .line 1
    const-string v0, "editor"

    .line 2
    .line 3
    invoke-static {p1, v0}, Lkotlin/jvm/internal/r;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    const/4 v0, 0x0

    .line 7
    invoke-virtual {p1, v0}, Lv6/d$b;->f(I)LG6/X;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    invoke-static {p1}, LG6/L;->c(LG6/X;)LG6/f;

    .line 12
    .line 13
    .line 14
    move-result-object p1

    .line 15
    :try_start_0
    iget-object v1, p0, Ls6/c$c;->a:Ls6/u;

    .line 16
    .line 17
    invoke-virtual {v1}, Ls6/u;->toString()Ljava/lang/String;

    .line 18
    .line 19
    .line 20
    move-result-object v1

    .line 21
    invoke-interface {p1, v1}, LG6/f;->W(Ljava/lang/String;)LG6/f;

    .line 22
    .line 23
    .line 24
    move-result-object v1

    .line 25
    const/16 v2, 0xa

    .line 26
    .line 27
    invoke-interface {v1, v2}, LG6/f;->F(I)LG6/f;

    .line 28
    .line 29
    .line 30
    iget-object v1, p0, Ls6/c$c;->c:Ljava/lang/String;

    .line 31
    .line 32
    invoke-interface {p1, v1}, LG6/f;->W(Ljava/lang/String;)LG6/f;

    .line 33
    .line 34
    .line 35
    move-result-object v1

    .line 36
    invoke-interface {v1, v2}, LG6/f;->F(I)LG6/f;

    .line 37
    .line 38
    .line 39
    iget-object v1, p0, Ls6/c$c;->b:Ls6/t;

    .line 40
    .line 41
    invoke-virtual {v1}, Ls6/t;->size()I

    .line 42
    .line 43
    .line 44
    move-result v1

    .line 45
    int-to-long v3, v1

    .line 46
    invoke-interface {p1, v3, v4}, LG6/f;->J0(J)LG6/f;

    .line 47
    .line 48
    .line 49
    move-result-object v1

    .line 50
    invoke-interface {v1, v2}, LG6/f;->F(I)LG6/f;

    .line 51
    .line 52
    .line 53
    iget-object v1, p0, Ls6/c$c;->b:Ls6/t;

    .line 54
    .line 55
    invoke-virtual {v1}, Ls6/t;->size()I

    .line 56
    .line 57
    .line 58
    move-result v1
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 59
    move v3, v0

    .line 60
    :goto_0
    const-string v4, ": "

    .line 61
    .line 62
    if-ge v3, v1, :cond_0

    .line 63
    .line 64
    add-int/lit8 v5, v3, 0x1

    .line 65
    .line 66
    :try_start_1
    iget-object v6, p0, Ls6/c$c;->b:Ls6/t;

    .line 67
    .line 68
    invoke-virtual {v6, v3}, Ls6/t;->d(I)Ljava/lang/String;

    .line 69
    .line 70
    .line 71
    move-result-object v6

    .line 72
    invoke-interface {p1, v6}, LG6/f;->W(Ljava/lang/String;)LG6/f;

    .line 73
    .line 74
    .line 75
    move-result-object v6

    .line 76
    invoke-interface {v6, v4}, LG6/f;->W(Ljava/lang/String;)LG6/f;

    .line 77
    .line 78
    .line 79
    move-result-object v4

    .line 80
    iget-object v6, p0, Ls6/c$c;->b:Ls6/t;

    .line 81
    .line 82
    invoke-virtual {v6, v3}, Ls6/t;->g(I)Ljava/lang/String;

    .line 83
    .line 84
    .line 85
    move-result-object v3

    .line 86
    invoke-interface {v4, v3}, LG6/f;->W(Ljava/lang/String;)LG6/f;

    .line 87
    .line 88
    .line 89
    move-result-object v3

    .line 90
    invoke-interface {v3, v2}, LG6/f;->F(I)LG6/f;

    .line 91
    .line 92
    .line 93
    move v3, v5

    .line 94
    goto :goto_0

    .line 95
    :catchall_0
    move-exception v0

    .line 96
    goto/16 :goto_2

    .line 97
    .line 98
    :cond_0
    new-instance v1, Ly6/k;

    .line 99
    .line 100
    iget-object v3, p0, Ls6/c$c;->d:Ls6/y;

    .line 101
    .line 102
    iget v5, p0, Ls6/c$c;->e:I

    .line 103
    .line 104
    iget-object v6, p0, Ls6/c$c;->f:Ljava/lang/String;

    .line 105
    .line 106
    invoke-direct {v1, v3, v5, v6}, Ly6/k;-><init>(Ls6/y;ILjava/lang/String;)V

    .line 107
    .line 108
    .line 109
    invoke-virtual {v1}, Ly6/k;->toString()Ljava/lang/String;

    .line 110
    .line 111
    .line 112
    move-result-object v1

    .line 113
    invoke-interface {p1, v1}, LG6/f;->W(Ljava/lang/String;)LG6/f;

    .line 114
    .line 115
    .line 116
    move-result-object v1

    .line 117
    invoke-interface {v1, v2}, LG6/f;->F(I)LG6/f;

    .line 118
    .line 119
    .line 120
    iget-object v1, p0, Ls6/c$c;->g:Ls6/t;

    .line 121
    .line 122
    invoke-virtual {v1}, Ls6/t;->size()I

    .line 123
    .line 124
    .line 125
    move-result v1

    .line 126
    add-int/lit8 v1, v1, 0x2

    .line 127
    .line 128
    int-to-long v5, v1

    .line 129
    invoke-interface {p1, v5, v6}, LG6/f;->J0(J)LG6/f;

    .line 130
    .line 131
    .line 132
    move-result-object v1

    .line 133
    invoke-interface {v1, v2}, LG6/f;->F(I)LG6/f;

    .line 134
    .line 135
    .line 136
    iget-object v1, p0, Ls6/c$c;->g:Ls6/t;

    .line 137
    .line 138
    invoke-virtual {v1}, Ls6/t;->size()I

    .line 139
    .line 140
    .line 141
    move-result v1

    .line 142
    :goto_1
    if-ge v0, v1, :cond_1

    .line 143
    .line 144
    add-int/lit8 v3, v0, 0x1

    .line 145
    .line 146
    iget-object v5, p0, Ls6/c$c;->g:Ls6/t;

    .line 147
    .line 148
    invoke-virtual {v5, v0}, Ls6/t;->d(I)Ljava/lang/String;

    .line 149
    .line 150
    .line 151
    move-result-object v5

    .line 152
    invoke-interface {p1, v5}, LG6/f;->W(Ljava/lang/String;)LG6/f;

    .line 153
    .line 154
    .line 155
    move-result-object v5

    .line 156
    invoke-interface {v5, v4}, LG6/f;->W(Ljava/lang/String;)LG6/f;

    .line 157
    .line 158
    .line 159
    move-result-object v5

    .line 160
    iget-object v6, p0, Ls6/c$c;->g:Ls6/t;

    .line 161
    .line 162
    invoke-virtual {v6, v0}, Ls6/t;->g(I)Ljava/lang/String;

    .line 163
    .line 164
    .line 165
    move-result-object v0

    .line 166
    invoke-interface {v5, v0}, LG6/f;->W(Ljava/lang/String;)LG6/f;

    .line 167
    .line 168
    .line 169
    move-result-object v0

    .line 170
    invoke-interface {v0, v2}, LG6/f;->F(I)LG6/f;

    .line 171
    .line 172
    .line 173
    move v0, v3

    .line 174
    goto :goto_1

    .line 175
    :cond_1
    sget-object v0, Ls6/c$c;->l:Ljava/lang/String;

    .line 176
    .line 177
    invoke-interface {p1, v0}, LG6/f;->W(Ljava/lang/String;)LG6/f;

    .line 178
    .line 179
    .line 180
    move-result-object v0

    .line 181
    invoke-interface {v0, v4}, LG6/f;->W(Ljava/lang/String;)LG6/f;

    .line 182
    .line 183
    .line 184
    move-result-object v0

    .line 185
    iget-wide v5, p0, Ls6/c$c;->i:J

    .line 186
    .line 187
    invoke-interface {v0, v5, v6}, LG6/f;->J0(J)LG6/f;

    .line 188
    .line 189
    .line 190
    move-result-object v0

    .line 191
    invoke-interface {v0, v2}, LG6/f;->F(I)LG6/f;

    .line 192
    .line 193
    .line 194
    sget-object v0, Ls6/c$c;->m:Ljava/lang/String;

    .line 195
    .line 196
    invoke-interface {p1, v0}, LG6/f;->W(Ljava/lang/String;)LG6/f;

    .line 197
    .line 198
    .line 199
    move-result-object v0

    .line 200
    invoke-interface {v0, v4}, LG6/f;->W(Ljava/lang/String;)LG6/f;

    .line 201
    .line 202
    .line 203
    move-result-object v0

    .line 204
    iget-wide v3, p0, Ls6/c$c;->j:J

    .line 205
    .line 206
    invoke-interface {v0, v3, v4}, LG6/f;->J0(J)LG6/f;

    .line 207
    .line 208
    .line 209
    move-result-object v0

    .line 210
    invoke-interface {v0, v2}, LG6/f;->F(I)LG6/f;

    .line 211
    .line 212
    .line 213
    invoke-virtual {p0}, Ls6/c$c;->a()Z

    .line 214
    .line 215
    .line 216
    move-result v0

    .line 217
    if-eqz v0, :cond_2

    .line 218
    .line 219
    invoke-interface {p1, v2}, LG6/f;->F(I)LG6/f;

    .line 220
    .line 221
    .line 222
    iget-object v0, p0, Ls6/c$c;->h:Ls6/s;

    .line 223
    .line 224
    invoke-static {v0}, Lkotlin/jvm/internal/r;->c(Ljava/lang/Object;)V

    .line 225
    .line 226
    .line 227
    invoke-virtual {v0}, Ls6/s;->a()Ls6/i;

    .line 228
    .line 229
    .line 230
    move-result-object v0

    .line 231
    invoke-virtual {v0}, Ls6/i;->c()Ljava/lang/String;

    .line 232
    .line 233
    .line 234
    move-result-object v0

    .line 235
    invoke-interface {p1, v0}, LG6/f;->W(Ljava/lang/String;)LG6/f;

    .line 236
    .line 237
    .line 238
    move-result-object v0

    .line 239
    invoke-interface {v0, v2}, LG6/f;->F(I)LG6/f;

    .line 240
    .line 241
    .line 242
    iget-object v0, p0, Ls6/c$c;->h:Ls6/s;

    .line 243
    .line 244
    invoke-virtual {v0}, Ls6/s;->d()Ljava/util/List;

    .line 245
    .line 246
    .line 247
    move-result-object v0

    .line 248
    invoke-virtual {p0, p1, v0}, Ls6/c$c;->e(LG6/f;Ljava/util/List;)V

    .line 249
    .line 250
    .line 251
    iget-object v0, p0, Ls6/c$c;->h:Ls6/s;

    .line 252
    .line 253
    invoke-virtual {v0}, Ls6/s;->c()Ljava/util/List;

    .line 254
    .line 255
    .line 256
    move-result-object v0

    .line 257
    invoke-virtual {p0, p1, v0}, Ls6/c$c;->e(LG6/f;Ljava/util/List;)V

    .line 258
    .line 259
    .line 260
    iget-object v0, p0, Ls6/c$c;->h:Ls6/s;

    .line 261
    .line 262
    invoke-virtual {v0}, Ls6/s;->e()Ls6/E;

    .line 263
    .line 264
    .line 265
    move-result-object v0

    .line 266
    invoke-virtual {v0}, Ls6/E;->b()Ljava/lang/String;

    .line 267
    .line 268
    .line 269
    move-result-object v0

    .line 270
    invoke-interface {p1, v0}, LG6/f;->W(Ljava/lang/String;)LG6/f;

    .line 271
    .line 272
    .line 273
    move-result-object v0

    .line 274
    invoke-interface {v0, v2}, LG6/f;->F(I)LG6/f;

    .line 275
    .line 276
    .line 277
    :cond_2
    sget-object v0, Ly5/I;->a:Ly5/I;
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 278
    .line 279
    const/4 v0, 0x0

    .line 280
    invoke-static {p1, v0}, LK5/b;->a(Ljava/io/Closeable;Ljava/lang/Throwable;)V

    .line 281
    .line 282
    .line 283
    return-void

    .line 284
    :goto_2
    :try_start_2
    throw v0
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    .line 285
    :catchall_1
    move-exception v1

    .line 286
    invoke-static {p1, v0}, LK5/b;->a(Ljava/io/Closeable;Ljava/lang/Throwable;)V

    .line 287
    .line 288
    .line 289
    throw v1
.end method
