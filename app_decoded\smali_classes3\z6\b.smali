.class public final Lz6/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ly6/d;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lz6/b$f;,
        Lz6/b$b;,
        Lz6/b$a;,
        Lz6/b$e;,
        Lz6/b$c;,
        Lz6/b$g;,
        Lz6/b$d;
    }
.end annotation


# static fields
.field public static final h:Lz6/b$d;


# instance fields
.field public final a:Ls6/x;

.field public final b:Lx6/f;

.field public final c:LG6/g;

.field public final d:LG6/f;

.field public e:I

.field public final f:Lz6/a;

.field public g:Ls6/t;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 1
    new-instance v0, Lz6/b$d;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, v1}, Lz6/b$d;-><init>(Lkotlin/jvm/internal/j;)V

    .line 5
    .line 6
    .line 7
    sput-object v0, Lz6/b;->h:Lz6/b$d;

    .line 8
    .line 9
    return-void
.end method

.method public constructor <init>(Ls6/x;Lx6/f;LG6/g;LG6/f;)V
    .locals 1

    .line 1
    const-string v0, "connection"

    .line 2
    .line 3
    invoke-static {p2, v0}, Lkotlin/jvm/internal/r;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    const-string v0, "source"

    .line 7
    .line 8
    invoke-static {p3, v0}, Lkotlin/jvm/internal/r;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 9
    .line 10
    .line 11
    const-string v0, "sink"

    .line 12
    .line 13
    invoke-static {p4, v0}, Lkotlin/jvm/internal/r;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 14
    .line 15
    .line 16
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 17
    .line 18
    .line 19
    iput-object p1, p0, Lz6/b;->a:Ls6/x;

    .line 20
    .line 21
    iput-object p2, p0, Lz6/b;->b:Lx6/f;

    .line 22
    .line 23
    iput-object p3, p0, Lz6/b;->c:LG6/g;

    .line 24
    .line 25
    iput-object p4, p0, Lz6/b;->d:LG6/f;

    .line 26
    .line 27
    new-instance p1, Lz6/a;

    .line 28
    .line 29
    invoke-direct {p1, p3}, Lz6/a;-><init>(LG6/g;)V

    .line 30
    .line 31
    .line 32
    iput-object p1, p0, Lz6/b;->f:Lz6/a;

    .line 33
    .line 34
    return-void
.end method

.method public static final synthetic i(Lz6/b;LG6/o;)V
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Lz6/b;->r(LG6/o;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final synthetic j(Lz6/b;)Ls6/x;
    .locals 0

    .line 1
    iget-object p0, p0, Lz6/b;->a:Ls6/x;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic k(Lz6/b;)Lz6/a;
    .locals 0

    .line 1
    iget-object p0, p0, Lz6/b;->f:Lz6/a;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic l(Lz6/b;)LG6/f;
    .locals 0

    .line 1
    iget-object p0, p0, Lz6/b;->d:LG6/f;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic m(Lz6/b;)LG6/g;
    .locals 0

    .line 1
    iget-object p0, p0, Lz6/b;->c:LG6/g;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic n(Lz6/b;)I
    .locals 0

    .line 1
    iget p0, p0, Lz6/b;->e:I

    .line 2
    .line 3
    return p0
.end method

.method public static final synthetic o(Lz6/b;)Ls6/t;
    .locals 0

    .line 1
    iget-object p0, p0, Lz6/b;->g:Ls6/t;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic p(Lz6/b;I)V
    .locals 0

    .line 1
    iput p1, p0, Lz6/b;->e:I

    .line 2
    .line 3
    return-void
.end method

.method public static final synthetic q(Lz6/b;Ls6/t;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lz6/b;->g:Ls6/t;

    .line 2
    .line 3
    return-void
.end method


# virtual methods
.method public final A(Ls6/t;Ljava/lang/String;)V
    .locals 5

    .line 1
    const-string v0, "headers"

    .line 2
    .line 3
    invoke-static {p1, v0}, Lkotlin/jvm/internal/r;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    const-string v0, "requestLine"

    .line 7
    .line 8
    invoke-static {p2, v0}, Lkotlin/jvm/internal/r;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 9
    .line 10
    .line 11
    iget v0, p0, Lz6/b;->e:I

    .line 12
    .line 13
    if-nez v0, :cond_1

    .line 14
    .line 15
    iget-object v0, p0, Lz6/b;->d:LG6/f;

    .line 16
    .line 17
    invoke-interface {v0, p2}, LG6/f;->W(Ljava/lang/String;)LG6/f;

    .line 18
    .line 19
    .line 20
    move-result-object p2

    .line 21
    const-string v0, "\r\n"

    .line 22
    .line 23
    invoke-interface {p2, v0}, LG6/f;->W(Ljava/lang/String;)LG6/f;

    .line 24
    .line 25
    .line 26
    invoke-virtual {p1}, Ls6/t;->size()I

    .line 27
    .line 28
    .line 29
    move-result p2

    .line 30
    const/4 v1, 0x0

    .line 31
    :goto_0
    if-ge v1, p2, :cond_0

    .line 32
    .line 33
    add-int/lit8 v2, v1, 0x1

    .line 34
    .line 35
    iget-object v3, p0, Lz6/b;->d:LG6/f;

    .line 36
    .line 37
    invoke-virtual {p1, v1}, Ls6/t;->d(I)Ljava/lang/String;

    .line 38
    .line 39
    .line 40
    move-result-object v4

    .line 41
    invoke-interface {v3, v4}, LG6/f;->W(Ljava/lang/String;)LG6/f;

    .line 42
    .line 43
    .line 44
    move-result-object v3

    .line 45
    const-string v4, ": "

    .line 46
    .line 47
    invoke-interface {v3, v4}, LG6/f;->W(Ljava/lang/String;)LG6/f;

    .line 48
    .line 49
    .line 50
    move-result-object v3

    .line 51
    invoke-virtual {p1, v1}, Ls6/t;->g(I)Ljava/lang/String;

    .line 52
    .line 53
    .line 54
    move-result-object v1

    .line 55
    invoke-interface {v3, v1}, LG6/f;->W(Ljava/lang/String;)LG6/f;

    .line 56
    .line 57
    .line 58
    move-result-object v1

    .line 59
    invoke-interface {v1, v0}, LG6/f;->W(Ljava/lang/String;)LG6/f;

    .line 60
    .line 61
    .line 62
    move v1, v2

    .line 63
    goto :goto_0

    .line 64
    :cond_0
    iget-object p1, p0, Lz6/b;->d:LG6/f;

    .line 65
    .line 66
    invoke-interface {p1, v0}, LG6/f;->W(Ljava/lang/String;)LG6/f;

    .line 67
    .line 68
    .line 69
    const/4 p1, 0x1

    .line 70
    iput p1, p0, Lz6/b;->e:I

    .line 71
    .line 72
    return-void

    .line 73
    :cond_1
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 74
    .line 75
    .line 76
    move-result-object p1

    .line 77
    const-string p2, "state: "

    .line 78
    .line 79
    invoke-static {p2, p1}, Lkotlin/jvm/internal/r;->n(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/String;

    .line 80
    .line 81
    .line 82
    move-result-object p1

    .line 83
    new-instance p2, Ljava/lang/IllegalStateException;

    .line 84
    .line 85
    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 86
    .line 87
    .line 88
    move-result-object p1

    .line 89
    invoke-direct {p2, p1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 90
    .line 91
    .line 92
    throw p2
.end method

.method public a()V
    .locals 1

    .line 1
    iget-object v0, p0, Lz6/b;->d:LG6/f;

    .line 2
    .line 3
    invoke-interface {v0}, LG6/f;->flush()V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public b(Z)Ls6/B$a;
    .locals 4

    .line 1
    iget v0, p0, Lz6/b;->e:I

    .line 2
    .line 3
    const/4 v1, 0x1

    .line 4
    const/4 v2, 0x3

    .line 5
    if-eq v0, v1, :cond_1

    .line 6
    .line 7
    if-ne v0, v2, :cond_0

    .line 8
    .line 9
    goto :goto_0

    .line 10
    :cond_0
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 11
    .line 12
    .line 13
    move-result-object p1

    .line 14
    const-string v0, "state: "

    .line 15
    .line 16
    invoke-static {v0, p1}, Lkotlin/jvm/internal/r;->n(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/String;

    .line 17
    .line 18
    .line 19
    move-result-object p1

    .line 20
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 21
    .line 22
    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 23
    .line 24
    .line 25
    move-result-object p1

    .line 26
    invoke-direct {v0, p1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 27
    .line 28
    .line 29
    throw v0

    .line 30
    :cond_1
    :goto_0
    :try_start_0
    sget-object v0, Ly6/k;->d:Ly6/k$a;

    .line 31
    .line 32
    iget-object v1, p0, Lz6/b;->f:Lz6/a;

    .line 33
    .line 34
    invoke-virtual {v1}, Lz6/a;->b()Ljava/lang/String;

    .line 35
    .line 36
    .line 37
    move-result-object v1

    .line 38
    invoke-virtual {v0, v1}, Ly6/k$a;->a(Ljava/lang/String;)Ly6/k;

    .line 39
    .line 40
    .line 41
    move-result-object v0

    .line 42
    new-instance v1, Ls6/B$a;

    .line 43
    .line 44
    invoke-direct {v1}, Ls6/B$a;-><init>()V

    .line 45
    .line 46
    .line 47
    iget-object v3, v0, Ly6/k;->a:Ls6/y;

    .line 48
    .line 49
    invoke-virtual {v1, v3}, Ls6/B$a;->q(Ls6/y;)Ls6/B$a;

    .line 50
    .line 51
    .line 52
    move-result-object v1

    .line 53
    iget v3, v0, Ly6/k;->b:I

    .line 54
    .line 55
    invoke-virtual {v1, v3}, Ls6/B$a;->g(I)Ls6/B$a;

    .line 56
    .line 57
    .line 58
    move-result-object v1

    .line 59
    iget-object v3, v0, Ly6/k;->c:Ljava/lang/String;

    .line 60
    .line 61
    invoke-virtual {v1, v3}, Ls6/B$a;->n(Ljava/lang/String;)Ls6/B$a;

    .line 62
    .line 63
    .line 64
    move-result-object v1

    .line 65
    iget-object v3, p0, Lz6/b;->f:Lz6/a;

    .line 66
    .line 67
    invoke-virtual {v3}, Lz6/a;->a()Ls6/t;

    .line 68
    .line 69
    .line 70
    move-result-object v3

    .line 71
    invoke-virtual {v1, v3}, Ls6/B$a;->l(Ls6/t;)Ls6/B$a;

    .line 72
    .line 73
    .line 74
    move-result-object v1

    .line 75
    const/16 v3, 0x64

    .line 76
    .line 77
    if-eqz p1, :cond_2

    .line 78
    .line 79
    iget p1, v0, Ly6/k;->b:I

    .line 80
    .line 81
    if-ne p1, v3, :cond_2

    .line 82
    .line 83
    const/4 v1, 0x0

    .line 84
    goto :goto_1

    .line 85
    :catch_0
    move-exception p1

    .line 86
    goto :goto_2

    .line 87
    :cond_2
    iget p1, v0, Ly6/k;->b:I

    .line 88
    .line 89
    if-ne p1, v3, :cond_3

    .line 90
    .line 91
    iput v2, p0, Lz6/b;->e:I

    .line 92
    .line 93
    goto :goto_1

    .line 94
    :cond_3
    const/4 p1, 0x4

    .line 95
    iput p1, p0, Lz6/b;->e:I
    :try_end_0
    .catch Ljava/io/EOFException; {:try_start_0 .. :try_end_0} :catch_0

    .line 96
    .line 97
    :goto_1
    return-object v1

    .line 98
    :goto_2
    invoke-virtual {p0}, Lz6/b;->c()Lx6/f;

    .line 99
    .line 100
    .line 101
    move-result-object v0

    .line 102
    invoke-virtual {v0}, Lx6/f;->z()Ls6/D;

    .line 103
    .line 104
    .line 105
    move-result-object v0

    .line 106
    invoke-virtual {v0}, Ls6/D;->a()Ls6/a;

    .line 107
    .line 108
    .line 109
    move-result-object v0

    .line 110
    invoke-virtual {v0}, Ls6/a;->l()Ls6/u;

    .line 111
    .line 112
    .line 113
    move-result-object v0

    .line 114
    invoke-virtual {v0}, Ls6/u;->o()Ljava/lang/String;

    .line 115
    .line 116
    .line 117
    move-result-object v0

    .line 118
    new-instance v1, Ljava/io/IOException;

    .line 119
    .line 120
    const-string v2, "unexpected end of stream on "

    .line 121
    .line 122
    invoke-static {v2, v0}, Lkotlin/jvm/internal/r;->n(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/String;

    .line 123
    .line 124
    .line 125
    move-result-object v0

    .line 126
    invoke-direct {v1, v0, p1}, Ljava/io/IOException;-><init>(Ljava/lang/String;Ljava/lang/Throwable;)V

    .line 127
    .line 128
    .line 129
    throw v1
.end method

.method public c()Lx6/f;
    .locals 1

    .line 1
    iget-object v0, p0, Lz6/b;->b:Lx6/f;

    .line 2
    .line 3
    return-object v0
.end method

.method public cancel()V
    .locals 1

    .line 1
    invoke-virtual {p0}, Lz6/b;->c()Lx6/f;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0}, Lx6/f;->d()V

    .line 6
    .line 7
    .line 8
    return-void
.end method

.method public d(Ls6/B;)J
    .locals 2

    .line 1
    const-string v0, "response"

    .line 2
    .line 3
    invoke-static {p1, v0}, Lkotlin/jvm/internal/r;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    invoke-static {p1}, Ly6/e;->b(Ls6/B;)Z

    .line 7
    .line 8
    .line 9
    move-result v0

    .line 10
    if-nez v0, :cond_0

    .line 11
    .line 12
    const-wide/16 v0, 0x0

    .line 13
    .line 14
    goto :goto_0

    .line 15
    :cond_0
    invoke-virtual {p0, p1}, Lz6/b;->t(Ls6/B;)Z

    .line 16
    .line 17
    .line 18
    move-result v0

    .line 19
    if-eqz v0, :cond_1

    .line 20
    .line 21
    const-wide/16 v0, -0x1

    .line 22
    .line 23
    goto :goto_0

    .line 24
    :cond_1
    invoke-static {p1}, Lt6/d;->v(Ls6/B;)J

    .line 25
    .line 26
    .line 27
    move-result-wide v0

    .line 28
    :goto_0
    return-wide v0
.end method

.method public e()V
    .locals 1

    .line 1
    iget-object v0, p0, Lz6/b;->d:LG6/f;

    .line 2
    .line 3
    invoke-interface {v0}, LG6/f;->flush()V

    .line 4
    .line 5
    .line 6
    return-void
.end method

.method public f(Ls6/B;)LG6/Z;
    .locals 4

    .line 1
    const-string v0, "response"

    .line 2
    .line 3
    invoke-static {p1, v0}, Lkotlin/jvm/internal/r;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    invoke-static {p1}, Ly6/e;->b(Ls6/B;)Z

    .line 7
    .line 8
    .line 9
    move-result v0

    .line 10
    if-nez v0, :cond_0

    .line 11
    .line 12
    const-wide/16 v0, 0x0

    .line 13
    .line 14
    invoke-virtual {p0, v0, v1}, Lz6/b;->w(J)LG6/Z;

    .line 15
    .line 16
    .line 17
    move-result-object p1

    .line 18
    goto :goto_0

    .line 19
    :cond_0
    invoke-virtual {p0, p1}, Lz6/b;->t(Ls6/B;)Z

    .line 20
    .line 21
    .line 22
    move-result v0

    .line 23
    if-eqz v0, :cond_1

    .line 24
    .line 25
    invoke-virtual {p1}, Ls6/B;->h0()Ls6/z;

    .line 26
    .line 27
    .line 28
    move-result-object p1

    .line 29
    invoke-virtual {p1}, Ls6/z;->j()Ls6/u;

    .line 30
    .line 31
    .line 32
    move-result-object p1

    .line 33
    invoke-virtual {p0, p1}, Lz6/b;->v(Ls6/u;)LG6/Z;

    .line 34
    .line 35
    .line 36
    move-result-object p1

    .line 37
    goto :goto_0

    .line 38
    :cond_1
    invoke-static {p1}, Lt6/d;->v(Ls6/B;)J

    .line 39
    .line 40
    .line 41
    move-result-wide v0

    .line 42
    const-wide/16 v2, -0x1

    .line 43
    .line 44
    cmp-long p1, v0, v2

    .line 45
    .line 46
    if-eqz p1, :cond_2

    .line 47
    .line 48
    invoke-virtual {p0, v0, v1}, Lz6/b;->w(J)LG6/Z;

    .line 49
    .line 50
    .line 51
    move-result-object p1

    .line 52
    goto :goto_0

    .line 53
    :cond_2
    invoke-virtual {p0}, Lz6/b;->y()LG6/Z;

    .line 54
    .line 55
    .line 56
    move-result-object p1

    .line 57
    :goto_0
    return-object p1
.end method

.method public g(Ls6/z;)V
    .locals 3

    .line 1
    const-string v0, "request"

    .line 2
    .line 3
    invoke-static {p1, v0}, Lkotlin/jvm/internal/r;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    sget-object v0, Ly6/i;->a:Ly6/i;

    .line 7
    .line 8
    invoke-virtual {p0}, Lz6/b;->c()Lx6/f;

    .line 9
    .line 10
    .line 11
    move-result-object v1

    .line 12
    invoke-virtual {v1}, Lx6/f;->z()Ls6/D;

    .line 13
    .line 14
    .line 15
    move-result-object v1

    .line 16
    invoke-virtual {v1}, Ls6/D;->b()Ljava/net/Proxy;

    .line 17
    .line 18
    .line 19
    move-result-object v1

    .line 20
    invoke-virtual {v1}, Ljava/net/Proxy;->type()Ljava/net/Proxy$Type;

    .line 21
    .line 22
    .line 23
    move-result-object v1

    .line 24
    const-string v2, "connection.route().proxy.type()"

    .line 25
    .line 26
    invoke-static {v1, v2}, Lkotlin/jvm/internal/r;->e(Ljava/lang/Object;Ljava/lang/String;)V

    .line 27
    .line 28
    .line 29
    invoke-virtual {v0, p1, v1}, Ly6/i;->a(Ls6/z;Ljava/net/Proxy$Type;)Ljava/lang/String;

    .line 30
    .line 31
    .line 32
    move-result-object v0

    .line 33
    invoke-virtual {p1}, Ls6/z;->f()Ls6/t;

    .line 34
    .line 35
    .line 36
    move-result-object p1

    .line 37
    invoke-virtual {p0, p1, v0}, Lz6/b;->A(Ls6/t;Ljava/lang/String;)V

    .line 38
    .line 39
    .line 40
    return-void
.end method

.method public h(Ls6/z;J)LG6/X;
    .locals 2

    .line 1
    const-string v0, "request"

    .line 2
    .line 3
    invoke-static {p1, v0}, Lkotlin/jvm/internal/r;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p1}, Ls6/z;->a()Ls6/A;

    .line 7
    .line 8
    .line 9
    move-result-object v0

    .line 10
    if-eqz v0, :cond_1

    .line 11
    .line 12
    invoke-virtual {p1}, Ls6/z;->a()Ls6/A;

    .line 13
    .line 14
    .line 15
    move-result-object v0

    .line 16
    invoke-virtual {v0}, Ls6/A;->isDuplex()Z

    .line 17
    .line 18
    .line 19
    move-result v0

    .line 20
    if-nez v0, :cond_0

    .line 21
    .line 22
    goto :goto_0

    .line 23
    :cond_0
    new-instance p1, Ljava/net/ProtocolException;

    .line 24
    .line 25
    const-string p2, "Duplex connections are not supported for HTTP/1"

    .line 26
    .line 27
    invoke-direct {p1, p2}, Ljava/net/ProtocolException;-><init>(Ljava/lang/String;)V

    .line 28
    .line 29
    .line 30
    throw p1

    .line 31
    :cond_1
    :goto_0
    invoke-virtual {p0, p1}, Lz6/b;->s(Ls6/z;)Z

    .line 32
    .line 33
    .line 34
    move-result p1

    .line 35
    if-eqz p1, :cond_2

    .line 36
    .line 37
    invoke-virtual {p0}, Lz6/b;->u()LG6/X;

    .line 38
    .line 39
    .line 40
    move-result-object p1

    .line 41
    goto :goto_1

    .line 42
    :cond_2
    const-wide/16 v0, -0x1

    .line 43
    .line 44
    cmp-long p1, p2, v0

    .line 45
    .line 46
    if-eqz p1, :cond_3

    .line 47
    .line 48
    invoke-virtual {p0}, Lz6/b;->x()LG6/X;

    .line 49
    .line 50
    .line 51
    move-result-object p1

    .line 52
    :goto_1
    return-object p1

    .line 53
    :cond_3
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 54
    .line 55
    const-string p2, "Cannot stream a request body without chunked encoding or a known content length!"

    .line 56
    .line 57
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 58
    .line 59
    .line 60
    throw p1
.end method

.method public final r(LG6/o;)V
    .locals 2

    .line 1
    invoke-virtual {p1}, LG6/o;->i()LG6/a0;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    sget-object v1, LG6/a0;->e:LG6/a0;

    .line 6
    .line 7
    invoke-virtual {p1, v1}, LG6/o;->j(LG6/a0;)LG6/o;

    .line 8
    .line 9
    .line 10
    invoke-virtual {v0}, LG6/a0;->a()LG6/a0;

    .line 11
    .line 12
    .line 13
    invoke-virtual {v0}, LG6/a0;->b()LG6/a0;

    .line 14
    .line 15
    .line 16
    return-void
.end method

.method public final s(Ls6/z;)Z
    .locals 2

    .line 1
    const-string v0, "Transfer-Encoding"

    .line 2
    .line 3
    invoke-virtual {p1, v0}, Ls6/z;->d(Ljava/lang/String;)Ljava/lang/String;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    const/4 v0, 0x1

    .line 8
    const-string v1, "chunked"

    .line 9
    .line 10
    invoke-static {v1, p1, v0}, LV5/n;->u(Ljava/lang/String;Ljava/lang/String;Z)Z

    .line 11
    .line 12
    .line 13
    move-result p1

    .line 14
    return p1
.end method

.method public final t(Ls6/B;)Z
    .locals 3

    .line 1
    const/4 v0, 0x0

    .line 2
    const/4 v1, 0x2

    .line 3
    const-string v2, "Transfer-Encoding"

    .line 4
    .line 5
    invoke-static {p1, v2, v0, v1, v0}, Ls6/B;->t(Ls6/B;Ljava/lang/String;Ljava/lang/String;ILjava/lang/Object;)Ljava/lang/String;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    const/4 v0, 0x1

    .line 10
    const-string v1, "chunked"

    .line 11
    .line 12
    invoke-static {v1, p1, v0}, LV5/n;->u(Ljava/lang/String;Ljava/lang/String;Z)Z

    .line 13
    .line 14
    .line 15
    move-result p1

    .line 16
    return p1
.end method

.method public final u()LG6/X;
    .locals 2

    .line 1
    iget v0, p0, Lz6/b;->e:I

    .line 2
    .line 3
    const/4 v1, 0x1

    .line 4
    if-ne v0, v1, :cond_0

    .line 5
    .line 6
    const/4 v0, 0x2

    .line 7
    iput v0, p0, Lz6/b;->e:I

    .line 8
    .line 9
    new-instance v0, Lz6/b$b;

    .line 10
    .line 11
    invoke-direct {v0, p0}, Lz6/b$b;-><init>(Lz6/b;)V

    .line 12
    .line 13
    .line 14
    return-object v0

    .line 15
    :cond_0
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    const-string v1, "state: "

    .line 20
    .line 21
    invoke-static {v1, v0}, Lkotlin/jvm/internal/r;->n(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/String;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    new-instance v1, Ljava/lang/IllegalStateException;

    .line 26
    .line 27
    invoke-virtual {v0}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 28
    .line 29
    .line 30
    move-result-object v0

    .line 31
    invoke-direct {v1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 32
    .line 33
    .line 34
    throw v1
.end method

.method public final v(Ls6/u;)LG6/Z;
    .locals 2

    .line 1
    iget v0, p0, Lz6/b;->e:I

    .line 2
    .line 3
    const/4 v1, 0x4

    .line 4
    if-ne v0, v1, :cond_0

    .line 5
    .line 6
    const/4 v0, 0x5

    .line 7
    iput v0, p0, Lz6/b;->e:I

    .line 8
    .line 9
    new-instance v0, Lz6/b$c;

    .line 10
    .line 11
    invoke-direct {v0, p0, p1}, Lz6/b$c;-><init>(Lz6/b;Ls6/u;)V

    .line 12
    .line 13
    .line 14
    return-object v0

    .line 15
    :cond_0
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 16
    .line 17
    .line 18
    move-result-object p1

    .line 19
    const-string v0, "state: "

    .line 20
    .line 21
    invoke-static {v0, p1}, Lkotlin/jvm/internal/r;->n(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/String;

    .line 22
    .line 23
    .line 24
    move-result-object p1

    .line 25
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 26
    .line 27
    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 28
    .line 29
    .line 30
    move-result-object p1

    .line 31
    invoke-direct {v0, p1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 32
    .line 33
    .line 34
    throw v0
.end method

.method public final w(J)LG6/Z;
    .locals 2

    .line 1
    iget v0, p0, Lz6/b;->e:I

    .line 2
    .line 3
    const/4 v1, 0x4

    .line 4
    if-ne v0, v1, :cond_0

    .line 5
    .line 6
    const/4 v0, 0x5

    .line 7
    iput v0, p0, Lz6/b;->e:I

    .line 8
    .line 9
    new-instance v0, Lz6/b$e;

    .line 10
    .line 11
    invoke-direct {v0, p0, p1, p2}, Lz6/b$e;-><init>(Lz6/b;J)V

    .line 12
    .line 13
    .line 14
    return-object v0

    .line 15
    :cond_0
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 16
    .line 17
    .line 18
    move-result-object p1

    .line 19
    const-string p2, "state: "

    .line 20
    .line 21
    invoke-static {p2, p1}, Lkotlin/jvm/internal/r;->n(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/String;

    .line 22
    .line 23
    .line 24
    move-result-object p1

    .line 25
    new-instance p2, Ljava/lang/IllegalStateException;

    .line 26
    .line 27
    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 28
    .line 29
    .line 30
    move-result-object p1

    .line 31
    invoke-direct {p2, p1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 32
    .line 33
    .line 34
    throw p2
.end method

.method public final x()LG6/X;
    .locals 2

    .line 1
    iget v0, p0, Lz6/b;->e:I

    .line 2
    .line 3
    const/4 v1, 0x1

    .line 4
    if-ne v0, v1, :cond_0

    .line 5
    .line 6
    const/4 v0, 0x2

    .line 7
    iput v0, p0, Lz6/b;->e:I

    .line 8
    .line 9
    new-instance v0, Lz6/b$f;

    .line 10
    .line 11
    invoke-direct {v0, p0}, Lz6/b$f;-><init>(Lz6/b;)V

    .line 12
    .line 13
    .line 14
    return-object v0

    .line 15
    :cond_0
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    const-string v1, "state: "

    .line 20
    .line 21
    invoke-static {v1, v0}, Lkotlin/jvm/internal/r;->n(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/String;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    new-instance v1, Ljava/lang/IllegalStateException;

    .line 26
    .line 27
    invoke-virtual {v0}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 28
    .line 29
    .line 30
    move-result-object v0

    .line 31
    invoke-direct {v1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 32
    .line 33
    .line 34
    throw v1
.end method

.method public final y()LG6/Z;
    .locals 2

    .line 1
    iget v0, p0, Lz6/b;->e:I

    .line 2
    .line 3
    const/4 v1, 0x4

    .line 4
    if-ne v0, v1, :cond_0

    .line 5
    .line 6
    const/4 v0, 0x5

    .line 7
    iput v0, p0, Lz6/b;->e:I

    .line 8
    .line 9
    invoke-virtual {p0}, Lz6/b;->c()Lx6/f;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    invoke-virtual {v0}, Lx6/f;->y()V

    .line 14
    .line 15
    .line 16
    new-instance v0, Lz6/b$g;

    .line 17
    .line 18
    invoke-direct {v0, p0}, Lz6/b$g;-><init>(Lz6/b;)V

    .line 19
    .line 20
    .line 21
    return-object v0

    .line 22
    :cond_0
    invoke-static {v0}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 23
    .line 24
    .line 25
    move-result-object v0

    .line 26
    const-string v1, "state: "

    .line 27
    .line 28
    invoke-static {v1, v0}, Lkotlin/jvm/internal/r;->n(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/String;

    .line 29
    .line 30
    .line 31
    move-result-object v0

    .line 32
    new-instance v1, Ljava/lang/IllegalStateException;

    .line 33
    .line 34
    invoke-virtual {v0}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 35
    .line 36
    .line 37
    move-result-object v0

    .line 38
    invoke-direct {v1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 39
    .line 40
    .line 41
    throw v1
.end method

.method public final z(Ls6/B;)V
    .locals 4

    .line 1
    const-string v0, "response"

    .line 2
    .line 3
    invoke-static {p1, v0}, Lkotlin/jvm/internal/r;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    invoke-static {p1}, Lt6/d;->v(Ls6/B;)J

    .line 7
    .line 8
    .line 9
    move-result-wide v0

    .line 10
    const-wide/16 v2, -0x1

    .line 11
    .line 12
    cmp-long p1, v0, v2

    .line 13
    .line 14
    if-nez p1, :cond_0

    .line 15
    .line 16
    return-void

    .line 17
    :cond_0
    invoke-virtual {p0, v0, v1}, Lz6/b;->w(J)LG6/Z;

    .line 18
    .line 19
    .line 20
    move-result-object p1

    .line 21
    const v0, 0x7fffffff

    .line 22
    .line 23
    .line 24
    sget-object v1, Ljava/util/concurrent/TimeUnit;->MILLISECONDS:Ljava/util/concurrent/TimeUnit;

    .line 25
    .line 26
    invoke-static {p1, v0, v1}, Lt6/d;->M(LG6/Z;ILjava/util/concurrent/TimeUnit;)Z

    .line 27
    .line 28
    .line 29
    invoke-interface {p1}, LG6/Z;->close()V

    .line 30
    .line 31
    .line 32
    return-void
.end method
