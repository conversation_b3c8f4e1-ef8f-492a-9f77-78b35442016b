.class public final LZ5/s;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LX5/V0;


# instance fields
.field public final a:LX5/n;


# direct methods
.method public constructor <init>(LX5/n;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LZ5/s;->a:LX5/n;

    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public b(Lc6/C;I)V
    .locals 1

    .line 1
    iget-object v0, p0, LZ5/s;->a:LX5/n;

    .line 2
    .line 3
    invoke-virtual {v0, p1, p2}, LX5/n;->b(Lc6/C;I)V

    .line 4
    .line 5
    .line 6
    return-void
.end method
