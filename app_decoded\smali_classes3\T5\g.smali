.class public interface abstract LT5/g;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LT5/b;
.implements Ly5/g;


# virtual methods
.method public abstract isExternal()Z
.end method

.method public abstract isInfix()Z
.end method

.method public abstract isInline()Z
.end method

.method public abstract isOperator()Z
.end method

.method public abstract isSuspend()Z
.end method
