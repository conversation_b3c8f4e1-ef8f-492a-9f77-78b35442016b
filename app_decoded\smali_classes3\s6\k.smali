.class public final Ls6/k;
.super Ljava/lang/Object;
.source "SourceFile"


# instance fields
.field public final a:Lx6/g;


# direct methods
.method public constructor <init>()V
    .locals 4

    .line 1
    const-wide/16 v0, 0x5

    .line 2
    sget-object v2, Ljava/util/concurrent/TimeUnit;->MINUTES:Ljava/util/concurrent/TimeUnit;

    const/4 v3, 0x5

    invoke-direct {p0, v3, v0, v1, v2}, Ls6/k;-><init>(IJLjava/util/concurrent/TimeUnit;)V

    return-void
.end method

.method public constructor <init>(IJLjava/util/concurrent/TimeUnit;)V
    .locals 7

    .line 3
    const-string v0, "timeUnit"

    invoke-static {p4, v0}, Lkotlin/jvm/internal/r;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 4
    new-instance v0, Lx6/g;

    .line 5
    sget-object v2, Lw6/e;->i:Lw6/e;

    move-object v1, v0

    move v3, p1

    move-wide v4, p2

    move-object v6, p4

    .line 6
    invoke-direct/range {v1 .. v6}, Lx6/g;-><init>(Lw6/e;IJLjava/util/concurrent/TimeUnit;)V

    invoke-direct {p0, v0}, Ls6/k;-><init>(Lx6/g;)V

    return-void
.end method

.method public constructor <init>(Lx6/g;)V
    .locals 1

    .line 7
    const-string v0, "delegate"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/r;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 8
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 9
    iput-object p1, p0, Ls6/k;->a:Lx6/g;

    return-void
.end method


# virtual methods
.method public final a()Lx6/g;
    .locals 1

    .line 1
    iget-object v0, p0, Ls6/k;->a:Lx6/g;

    .line 2
    .line 3
    return-object v0
.end method
