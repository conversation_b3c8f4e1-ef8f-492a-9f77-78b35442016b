.class public final LX5/H$a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LD5/g$c;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LX5/H;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation


# static fields
.field public static final synthetic a:LX5/H$a;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, LX5/H$a;

    .line 2
    .line 3
    invoke-direct {v0}, LX5/H$a;-><init>()V

    .line 4
    .line 5
    .line 6
    sput-object v0, LX5/H$a;->a:LX5/H$a;

    .line 7
    .line 8
    return-void
.end method

.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
