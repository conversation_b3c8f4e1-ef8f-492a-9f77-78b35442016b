.class public abstract Ls6/A;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Ls6/A$a;
    }
.end annotation


# static fields
.field public static final Companion:Ls6/A$a;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 1
    new-instance v0, Ls6/A$a;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, v1}, Ls6/A$a;-><init>(Lkotlin/jvm/internal/j;)V

    .line 5
    .line 6
    .line 7
    sput-object v0, Ls6/A;->Companion:Ls6/A$a;

    .line 8
    .line 9
    return-void
.end method

.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static final create(LG6/h;Ls6/w;)Ls6/A;
    .locals 1

    .line 1
    sget-object v0, Ls6/A;->Companion:Ls6/A$a;

    invoke-virtual {v0, p0, p1}, Ls6/A$a;->a(LG6/h;Ls6/w;)Ls6/A;

    move-result-object p0

    return-object p0
.end method

.method public static final create(Ljava/io/File;Ls6/w;)Ls6/A;
    .locals 1

    .line 2
    sget-object v0, Ls6/A;->Companion:Ls6/A$a;

    invoke-virtual {v0, p0, p1}, Ls6/A$a;->b(Ljava/io/File;Ls6/w;)Ls6/A;

    move-result-object p0

    return-object p0
.end method

.method public static final create(Ljava/lang/String;Ls6/w;)Ls6/A;
    .locals 1

    .line 3
    sget-object v0, Ls6/A;->Companion:Ls6/A$a;

    invoke-virtual {v0, p0, p1}, Ls6/A$a;->c(Ljava/lang/String;Ls6/w;)Ls6/A;

    move-result-object p0

    return-object p0
.end method

.method public static final create(Ls6/w;LG6/h;)Ls6/A;
    .locals 1

    .line 4
    sget-object v0, Ls6/A;->Companion:Ls6/A$a;

    invoke-virtual {v0, p0, p1}, Ls6/A$a;->d(Ls6/w;LG6/h;)Ls6/A;

    move-result-object p0

    return-object p0
.end method

.method public static final create(Ls6/w;Ljava/io/File;)Ls6/A;
    .locals 1

    .line 5
    sget-object v0, Ls6/A;->Companion:Ls6/A$a;

    invoke-virtual {v0, p0, p1}, Ls6/A$a;->e(Ls6/w;Ljava/io/File;)Ls6/A;

    move-result-object p0

    return-object p0
.end method

.method public static final create(Ls6/w;Ljava/lang/String;)Ls6/A;
    .locals 1

    .line 6
    sget-object v0, Ls6/A;->Companion:Ls6/A$a;

    invoke-virtual {v0, p0, p1}, Ls6/A$a;->f(Ls6/w;Ljava/lang/String;)Ls6/A;

    move-result-object p0

    return-object p0
.end method

.method public static final create(Ls6/w;[B)Ls6/A;
    .locals 1

    .line 7
    sget-object v0, Ls6/A;->Companion:Ls6/A$a;

    invoke-virtual {v0, p0, p1}, Ls6/A$a;->g(Ls6/w;[B)Ls6/A;

    move-result-object p0

    return-object p0
.end method

.method public static final create(Ls6/w;[BI)Ls6/A;
    .locals 1

    .line 8
    sget-object v0, Ls6/A;->Companion:Ls6/A$a;

    invoke-virtual {v0, p0, p1, p2}, Ls6/A$a;->h(Ls6/w;[BI)Ls6/A;

    move-result-object p0

    return-object p0
.end method

.method public static final create(Ls6/w;[BII)Ls6/A;
    .locals 1

    .line 9
    sget-object v0, Ls6/A;->Companion:Ls6/A$a;

    invoke-virtual {v0, p0, p1, p2, p3}, Ls6/A$a;->i(Ls6/w;[BII)Ls6/A;

    move-result-object p0

    return-object p0
.end method

.method public static final create([B)Ls6/A;
    .locals 1

    .line 10
    sget-object v0, Ls6/A;->Companion:Ls6/A$a;

    invoke-virtual {v0, p0}, Ls6/A$a;->j([B)Ls6/A;

    move-result-object p0

    return-object p0
.end method

.method public static final create([BLs6/w;)Ls6/A;
    .locals 1

    .line 11
    sget-object v0, Ls6/A;->Companion:Ls6/A$a;

    invoke-virtual {v0, p0, p1}, Ls6/A$a;->k([BLs6/w;)Ls6/A;

    move-result-object p0

    return-object p0
.end method

.method public static final create([BLs6/w;I)Ls6/A;
    .locals 1

    .line 12
    sget-object v0, Ls6/A;->Companion:Ls6/A$a;

    invoke-virtual {v0, p0, p1, p2}, Ls6/A$a;->l([BLs6/w;I)Ls6/A;

    move-result-object p0

    return-object p0
.end method

.method public static final create([BLs6/w;II)Ls6/A;
    .locals 1

    .line 13
    sget-object v0, Ls6/A;->Companion:Ls6/A$a;

    invoke-virtual {v0, p0, p1, p2, p3}, Ls6/A$a;->m([BLs6/w;II)Ls6/A;

    move-result-object p0

    return-object p0
.end method


# virtual methods
.method public abstract contentLength()J
.end method

.method public abstract contentType()Ls6/w;
.end method

.method public isDuplex()Z
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    return v0
.end method

.method public isOneShot()Z
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    return v0
.end method

.method public abstract writeTo(LG6/f;)V
.end method
