.class public interface abstract Ls6/e;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Cloneable;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Ls6/e$a;
    }
.end annotation


# virtual methods
.method public abstract a()Ls6/z;
.end method

.method public abstract c(Ls6/f;)V
.end method

.method public abstract cancel()V
.end method

.method public abstract execute()Ls6/B;
.end method

.method public abstract isCanceled()Z
.end method
