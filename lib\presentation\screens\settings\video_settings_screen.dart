import 'package:flutter/material.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import '../../../services/video_settings_manager.dart';

class VideoSettingsScreen extends StatefulWidget {
  const VideoSettingsScreen({super.key});

  @override
  State<VideoSettingsScreen> createState() => _VideoSettingsScreenState();
}

class _VideoSettingsScreenState extends State<VideoSettingsScreen> {
  final VideoSettingsManager _settings = VideoSettingsManager();

  @override
  void initState() {
    super.initState();
    _settings.init();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إعدادات الفيديو'),
        backgroundColor: Colors.black,
      ),
      backgroundColor: Colors.black,
      body: AnimatedBuilder(
        animation: _settings,
        builder: (context, child) {
          return ListView(
            padding: const EdgeInsets.all(16),
            children: [
              _buildSectionTitle('إعدادات التشغيل'),
              _buildPlaybackSpeedSetting(),
              _buildSkipDurationSetting(),
              _buildSwitchSetting(
                'تشغيل تلقائي',
                _settings.autoPlay,
                (value) => _settings.setAutoPlay(value),
              ),
              _buildSwitchSetting(
                'حفظ موضع التشغيل',
                _settings.rememberPosition,
                (value) => _settings.setRememberPosition(value),
              ),
              
              const SizedBox(height: 20),
              _buildSectionTitle('إعدادات العرض'),
              _buildAspectRatioSetting(),
              _buildVideoQualitySetting(),
              _buildSwitchSetting(
                'تسارع الأجهزة',
                _settings.hardwareAcceleration,
                (value) => _settings.setHardwareAcceleration(value),
              ),
              
              const SizedBox(height: 20),
              _buildSectionTitle('إعدادات الشبكة'),
              _buildNetworkCachingSetting(),
              _buildLiveCachingSetting(),
              _buildSwitchSetting(
                'إعادة الاتصال التلقائي',
                _settings.autoReconnect,
                (value) => _settings.setAutoReconnect(value),
              ),
              
              const SizedBox(height: 30),
              _buildResetButton(),
            ],
          );
        },
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Text(
        title,
        style: TextStyle(
          color: Colors.blue,
          fontSize: 18.sp,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildPlaybackSpeedSetting() {
    final speeds = [0.25, 0.5, 0.75, 1.0, 1.25, 1.5, 1.75, 2.0];
    
    return Card(
      color: Colors.grey[900],
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'سرعة التشغيل: ${_settings.playbackSpeed}x',
              style: const TextStyle(color: Colors.white, fontSize: 16),
            ),
            const SizedBox(height: 10),
            Wrap(
              spacing: 8,
              children: speeds.map((speed) {
                final isSelected = _settings.playbackSpeed == speed;
                return GestureDetector(
                  onTap: () => _settings.setPlaybackSpeed(speed),
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: isSelected ? Colors.blue : Colors.grey[700],
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      '${speed}x',
                      style: const TextStyle(color: Colors.white),
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSkipDurationSetting() {
    final durations = [5, 10, 15, 30, 60];
    
    return Card(
      color: Colors.grey[900],
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'مدة القفز: ${_settings.skipDuration} ثانية',
              style: const TextStyle(color: Colors.white, fontSize: 16),
            ),
            const SizedBox(height: 10),
            Wrap(
              spacing: 8,
              children: durations.map((duration) {
                final isSelected = _settings.skipDuration == duration;
                return GestureDetector(
                  onTap: () => _settings.setSkipDuration(duration),
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: isSelected ? Colors.blue : Colors.grey[700],
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      '${duration}s',
                      style: const TextStyle(color: Colors.white),
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAspectRatioSetting() {
    final ratios = ["16:9", "4:3", "21:9", "Auto"];
    
    return Card(
      color: Colors.grey[900],
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'نسبة العرض: ${_settings.aspectRatio}',
              style: const TextStyle(color: Colors.white, fontSize: 16),
            ),
            const SizedBox(height: 10),
            Wrap(
              spacing: 8,
              children: ratios.map((ratio) {
                final isSelected = _settings.aspectRatio == ratio;
                return GestureDetector(
                  onTap: () => _settings.setAspectRatio(ratio),
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: isSelected ? Colors.blue : Colors.grey[700],
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      ratio,
                      style: const TextStyle(color: Colors.white),
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVideoQualitySetting() {
    final qualities = ["Auto", "1080p", "720p", "480p", "360p"];
    
    return Card(
      color: Colors.grey[900],
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'جودة الفيديو: ${_settings.videoQuality}',
              style: const TextStyle(color: Colors.white, fontSize: 16),
            ),
            const SizedBox(height: 10),
            Wrap(
              spacing: 8,
              children: qualities.map((quality) {
                final isSelected = _settings.videoQuality == quality;
                return GestureDetector(
                  onTap: () => _settings.setVideoQuality(quality),
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: isSelected ? Colors.blue : Colors.grey[700],
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      quality,
                      style: const TextStyle(color: Colors.white),
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNetworkCachingSetting() {
    return Card(
      color: Colors.grey[900],
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تخزين الشبكة المؤقت: ${_settings.networkCaching}ms',
              style: const TextStyle(color: Colors.white, fontSize: 16),
            ),
            Slider(
              value: _settings.networkCaching.toDouble(),
              min: 1000,
              max: 10000,
              divisions: 18,
              onChanged: (value) => _settings.setNetworkCaching(value.round()),
              activeColor: Colors.blue,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLiveCachingSetting() {
    return Card(
      color: Colors.grey[900],
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تخزين البث المباشر المؤقت: ${_settings.liveCaching}ms',
              style: const TextStyle(color: Colors.white, fontSize: 16),
            ),
            Slider(
              value: _settings.liveCaching.toDouble(),
              min: 1000,
              max: 10000,
              divisions: 18,
              onChanged: (value) => _settings.setLiveCaching(value.round()),
              activeColor: Colors.blue,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSwitchSetting(String title, bool value, Function(bool) onChanged) {
    return Card(
      color: Colors.grey[900],
      child: SwitchListTile(
        title: Text(title, style: const TextStyle(color: Colors.white)),
        value: value,
        onChanged: onChanged,
        activeColor: Colors.blue,
      ),
    );
  }

  Widget _buildResetButton() {
    return ElevatedButton(
      onPressed: () {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            backgroundColor: Colors.grey[900],
            title: const Text('إعادة تعيين', style: TextStyle(color: Colors.white)),
            content: const Text(
              'هل تريد إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟',
              style: TextStyle(color: Colors.white),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () {
                  _settings.resetToDefaults();
                  Navigator.pop(context);
                },
                child: const Text('إعادة تعيين'),
              ),
            ],
          ),
        );
      },
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.red,
        minimumSize: const Size(double.infinity, 50),
      ),
      child: const Text(
        'إعادة تعيين جميع الإعدادات',
        style: TextStyle(color: Colors.white),
      ),
    );
  }
}
