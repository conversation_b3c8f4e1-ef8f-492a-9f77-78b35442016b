.class public Ls6/B$a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Ls6/B;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "a"
.end annotation


# instance fields
.field public a:Ls6/z;

.field public b:Ls6/y;

.field public c:I

.field public d:Ljava/lang/String;

.field public e:Ls6/s;

.field public f:Ls6/t$a;

.field public g:Ls6/C;

.field public h:Ls6/B;

.field public i:Ls6/B;

.field public j:Ls6/B;

.field public k:J

.field public l:J

.field public m:Lx6/c;


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 17
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, -0x1

    .line 18
    iput v0, p0, Ls6/B$a;->c:I

    .line 19
    new-instance v0, Ls6/t$a;

    invoke-direct {v0}, Ls6/t$a;-><init>()V

    iput-object v0, p0, Ls6/B$a;->f:Ls6/t$a;

    return-void
.end method

.method public constructor <init>(Ls6/B;)V
    .locals 2

    .line 1
    const-string v0, "response"

    invoke-static {p1, v0}, Lkotlin/jvm/internal/r;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    const/4 v0, -0x1

    .line 3
    iput v0, p0, Ls6/B$a;->c:I

    .line 4
    invoke-virtual {p1}, Ls6/B;->h0()Ls6/z;

    move-result-object v0

    iput-object v0, p0, Ls6/B$a;->a:Ls6/z;

    .line 5
    invoke-virtual {p1}, Ls6/B;->Q()Ls6/y;

    move-result-object v0

    iput-object v0, p0, Ls6/B$a;->b:Ls6/y;

    .line 6
    invoke-virtual {p1}, Ls6/B;->h()I

    move-result v0

    iput v0, p0, Ls6/B$a;->c:I

    .line 7
    invoke-virtual {p1}, Ls6/B;->A()Ljava/lang/String;

    move-result-object v0

    iput-object v0, p0, Ls6/B$a;->d:Ljava/lang/String;

    .line 8
    invoke-virtual {p1}, Ls6/B;->l()Ls6/s;

    move-result-object v0

    iput-object v0, p0, Ls6/B$a;->e:Ls6/s;

    .line 9
    invoke-virtual {p1}, Ls6/B;->v()Ls6/t;

    move-result-object v0

    invoke-virtual {v0}, Ls6/t;->f()Ls6/t$a;

    move-result-object v0

    iput-object v0, p0, Ls6/B$a;->f:Ls6/t$a;

    .line 10
    invoke-virtual {p1}, Ls6/B;->a()Ls6/C;

    move-result-object v0

    iput-object v0, p0, Ls6/B$a;->g:Ls6/C;

    .line 11
    invoke-virtual {p1}, Ls6/B;->B()Ls6/B;

    move-result-object v0

    iput-object v0, p0, Ls6/B$a;->h:Ls6/B;

    .line 12
    invoke-virtual {p1}, Ls6/B;->c()Ls6/B;

    move-result-object v0

    iput-object v0, p0, Ls6/B$a;->i:Ls6/B;

    .line 13
    invoke-virtual {p1}, Ls6/B;->P()Ls6/B;

    move-result-object v0

    iput-object v0, p0, Ls6/B$a;->j:Ls6/B;

    .line 14
    invoke-virtual {p1}, Ls6/B;->i0()J

    move-result-wide v0

    iput-wide v0, p0, Ls6/B$a;->k:J

    .line 15
    invoke-virtual {p1}, Ls6/B;->X()J

    move-result-wide v0

    iput-wide v0, p0, Ls6/B$a;->l:J

    .line 16
    invoke-virtual {p1}, Ls6/B;->j()Lx6/c;

    move-result-object p1

    iput-object p1, p0, Ls6/B$a;->m:Lx6/c;

    return-void
.end method


# virtual methods
.method public final A(Ls6/B;)V
    .locals 0

    .line 1
    iput-object p1, p0, Ls6/B$a;->h:Ls6/B;

    .line 2
    .line 3
    return-void
.end method

.method public final B(Ls6/B;)V
    .locals 0

    .line 1
    iput-object p1, p0, Ls6/B$a;->j:Ls6/B;

    .line 2
    .line 3
    return-void
.end method

.method public final C(Ls6/y;)V
    .locals 0

    .line 1
    iput-object p1, p0, Ls6/B$a;->b:Ls6/y;

    .line 2
    .line 3
    return-void
.end method

.method public final D(J)V
    .locals 0

    .line 1
    iput-wide p1, p0, Ls6/B$a;->l:J

    .line 2
    .line 3
    return-void
.end method

.method public final E(Ls6/z;)V
    .locals 0

    .line 1
    iput-object p1, p0, Ls6/B$a;->a:Ls6/z;

    .line 2
    .line 3
    return-void
.end method

.method public final F(J)V
    .locals 0

    .line 1
    iput-wide p1, p0, Ls6/B$a;->k:J

    .line 2
    .line 3
    return-void
.end method

.method public a(Ljava/lang/String;Ljava/lang/String;)Ls6/B$a;
    .locals 1

    .line 1
    const-string v0, "name"

    .line 2
    .line 3
    invoke-static {p1, v0}, Lkotlin/jvm/internal/r;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    const-string v0, "value"

    .line 7
    .line 8
    invoke-static {p2, v0}, Lkotlin/jvm/internal/r;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 9
    .line 10
    .line 11
    invoke-virtual {p0}, Ls6/B$a;->i()Ls6/t$a;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    invoke-virtual {v0, p1, p2}, Ls6/t$a;->a(Ljava/lang/String;Ljava/lang/String;)Ls6/t$a;

    .line 16
    .line 17
    .line 18
    return-object p0
.end method

.method public b(Ls6/C;)Ls6/B$a;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Ls6/B$a;->u(Ls6/C;)V

    .line 2
    .line 3
    .line 4
    return-object p0
.end method

.method public c()Ls6/B;
    .locals 18

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    iget v5, v0, Ls6/B$a;->c:I

    .line 4
    .line 5
    if-ltz v5, :cond_3

    .line 6
    .line 7
    iget-object v2, v0, Ls6/B$a;->a:Ls6/z;

    .line 8
    .line 9
    if-eqz v2, :cond_2

    .line 10
    .line 11
    iget-object v3, v0, Ls6/B$a;->b:Ls6/y;

    .line 12
    .line 13
    if-eqz v3, :cond_1

    .line 14
    .line 15
    iget-object v4, v0, Ls6/B$a;->d:Ljava/lang/String;

    .line 16
    .line 17
    if-eqz v4, :cond_0

    .line 18
    .line 19
    iget-object v6, v0, Ls6/B$a;->e:Ls6/s;

    .line 20
    .line 21
    iget-object v1, v0, Ls6/B$a;->f:Ls6/t$a;

    .line 22
    .line 23
    invoke-virtual {v1}, Ls6/t$a;->d()Ls6/t;

    .line 24
    .line 25
    .line 26
    move-result-object v7

    .line 27
    iget-object v8, v0, Ls6/B$a;->g:Ls6/C;

    .line 28
    .line 29
    iget-object v9, v0, Ls6/B$a;->h:Ls6/B;

    .line 30
    .line 31
    iget-object v10, v0, Ls6/B$a;->i:Ls6/B;

    .line 32
    .line 33
    iget-object v11, v0, Ls6/B$a;->j:Ls6/B;

    .line 34
    .line 35
    iget-wide v12, v0, Ls6/B$a;->k:J

    .line 36
    .line 37
    iget-wide v14, v0, Ls6/B$a;->l:J

    .line 38
    .line 39
    iget-object v1, v0, Ls6/B$a;->m:Lx6/c;

    .line 40
    .line 41
    new-instance v17, Ls6/B;

    .line 42
    .line 43
    move-object/from16 v16, v1

    .line 44
    .line 45
    move-object/from16 v1, v17

    .line 46
    .line 47
    invoke-direct/range {v1 .. v16}, Ls6/B;-><init>(Ls6/z;Ls6/y;Ljava/lang/String;ILs6/s;Ls6/t;Ls6/C;Ls6/B;Ls6/B;Ls6/B;JJLx6/c;)V

    .line 48
    .line 49
    .line 50
    return-object v17

    .line 51
    :cond_0
    new-instance v1, Ljava/lang/IllegalStateException;

    .line 52
    .line 53
    const-string v2, "message == null"

    .line 54
    .line 55
    invoke-virtual {v2}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 56
    .line 57
    .line 58
    move-result-object v2

    .line 59
    invoke-direct {v1, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 60
    .line 61
    .line 62
    throw v1

    .line 63
    :cond_1
    new-instance v1, Ljava/lang/IllegalStateException;

    .line 64
    .line 65
    const-string v2, "protocol == null"

    .line 66
    .line 67
    invoke-virtual {v2}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 68
    .line 69
    .line 70
    move-result-object v2

    .line 71
    invoke-direct {v1, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 72
    .line 73
    .line 74
    throw v1

    .line 75
    :cond_2
    new-instance v1, Ljava/lang/IllegalStateException;

    .line 76
    .line 77
    const-string v2, "request == null"

    .line 78
    .line 79
    invoke-virtual {v2}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 80
    .line 81
    .line 82
    move-result-object v2

    .line 83
    invoke-direct {v1, v2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 84
    .line 85
    .line 86
    throw v1

    .line 87
    :cond_3
    invoke-virtual/range {p0 .. p0}, Ls6/B$a;->h()I

    .line 88
    .line 89
    .line 90
    move-result v1

    .line 91
    invoke-static {v1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 92
    .line 93
    .line 94
    move-result-object v1

    .line 95
    const-string v2, "code < 0: "

    .line 96
    .line 97
    invoke-static {v2, v1}, Lkotlin/jvm/internal/r;->n(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/String;

    .line 98
    .line 99
    .line 100
    move-result-object v1

    .line 101
    new-instance v2, Ljava/lang/IllegalStateException;

    .line 102
    .line 103
    invoke-virtual {v1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 104
    .line 105
    .line 106
    move-result-object v1

    .line 107
    invoke-direct {v2, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 108
    .line 109
    .line 110
    throw v2
.end method

.method public d(Ls6/B;)Ls6/B$a;
    .locals 1

    .line 1
    const-string v0, "cacheResponse"

    .line 2
    .line 3
    invoke-virtual {p0, v0, p1}, Ls6/B$a;->f(Ljava/lang/String;Ls6/B;)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0, p1}, Ls6/B$a;->v(Ls6/B;)V

    .line 7
    .line 8
    .line 9
    return-object p0
.end method

.method public final e(Ls6/B;)V
    .locals 1

    .line 1
    if-nez p1, :cond_0

    .line 2
    .line 3
    goto :goto_0

    .line 4
    :cond_0
    invoke-virtual {p1}, Ls6/B;->a()Ls6/C;

    .line 5
    .line 6
    .line 7
    move-result-object p1

    .line 8
    if-nez p1, :cond_1

    .line 9
    .line 10
    :goto_0
    return-void

    .line 11
    :cond_1
    new-instance p1, Ljava/lang/IllegalArgumentException;

    .line 12
    .line 13
    const-string v0, "priorResponse.body != null"

    .line 14
    .line 15
    invoke-virtual {v0}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    invoke-direct {p1, v0}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    .line 20
    .line 21
    .line 22
    throw p1
.end method

.method public final f(Ljava/lang/String;Ls6/B;)V
    .locals 1

    .line 1
    if-nez p2, :cond_0

    .line 2
    .line 3
    goto :goto_0

    .line 4
    :cond_0
    invoke-virtual {p2}, Ls6/B;->a()Ls6/C;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    if-nez v0, :cond_4

    .line 9
    .line 10
    invoke-virtual {p2}, Ls6/B;->B()Ls6/B;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    if-nez v0, :cond_3

    .line 15
    .line 16
    invoke-virtual {p2}, Ls6/B;->c()Ls6/B;

    .line 17
    .line 18
    .line 19
    move-result-object v0

    .line 20
    if-nez v0, :cond_2

    .line 21
    .line 22
    invoke-virtual {p2}, Ls6/B;->P()Ls6/B;

    .line 23
    .line 24
    .line 25
    move-result-object p2

    .line 26
    if-nez p2, :cond_1

    .line 27
    .line 28
    :goto_0
    return-void

    .line 29
    :cond_1
    const-string p2, ".priorResponse != null"

    .line 30
    .line 31
    invoke-static {p1, p2}, Lkotlin/jvm/internal/r;->n(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/String;

    .line 32
    .line 33
    .line 34
    move-result-object p1

    .line 35
    new-instance p2, Ljava/lang/IllegalArgumentException;

    .line 36
    .line 37
    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 38
    .line 39
    .line 40
    move-result-object p1

    .line 41
    invoke-direct {p2, p1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    .line 42
    .line 43
    .line 44
    throw p2

    .line 45
    :cond_2
    const-string p2, ".cacheResponse != null"

    .line 46
    .line 47
    invoke-static {p1, p2}, Lkotlin/jvm/internal/r;->n(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/String;

    .line 48
    .line 49
    .line 50
    move-result-object p1

    .line 51
    new-instance p2, Ljava/lang/IllegalArgumentException;

    .line 52
    .line 53
    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 54
    .line 55
    .line 56
    move-result-object p1

    .line 57
    invoke-direct {p2, p1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    .line 58
    .line 59
    .line 60
    throw p2

    .line 61
    :cond_3
    const-string p2, ".networkResponse != null"

    .line 62
    .line 63
    invoke-static {p1, p2}, Lkotlin/jvm/internal/r;->n(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/String;

    .line 64
    .line 65
    .line 66
    move-result-object p1

    .line 67
    new-instance p2, Ljava/lang/IllegalArgumentException;

    .line 68
    .line 69
    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 70
    .line 71
    .line 72
    move-result-object p1

    .line 73
    invoke-direct {p2, p1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    .line 74
    .line 75
    .line 76
    throw p2

    .line 77
    :cond_4
    const-string p2, ".body != null"

    .line 78
    .line 79
    invoke-static {p1, p2}, Lkotlin/jvm/internal/r;->n(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/String;

    .line 80
    .line 81
    .line 82
    move-result-object p1

    .line 83
    new-instance p2, Ljava/lang/IllegalArgumentException;

    .line 84
    .line 85
    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 86
    .line 87
    .line 88
    move-result-object p1

    .line 89
    invoke-direct {p2, p1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    .line 90
    .line 91
    .line 92
    throw p2
.end method

.method public g(I)Ls6/B$a;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Ls6/B$a;->w(I)V

    .line 2
    .line 3
    .line 4
    return-object p0
.end method

.method public final h()I
    .locals 1

    .line 1
    iget v0, p0, Ls6/B$a;->c:I

    .line 2
    .line 3
    return v0
.end method

.method public final i()Ls6/t$a;
    .locals 1

    .line 1
    iget-object v0, p0, Ls6/B$a;->f:Ls6/t$a;

    .line 2
    .line 3
    return-object v0
.end method

.method public j(Ls6/s;)Ls6/B$a;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Ls6/B$a;->x(Ls6/s;)V

    .line 2
    .line 3
    .line 4
    return-object p0
.end method

.method public k(Ljava/lang/String;Ljava/lang/String;)Ls6/B$a;
    .locals 1

    .line 1
    const-string v0, "name"

    .line 2
    .line 3
    invoke-static {p1, v0}, Lkotlin/jvm/internal/r;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    const-string v0, "value"

    .line 7
    .line 8
    invoke-static {p2, v0}, Lkotlin/jvm/internal/r;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 9
    .line 10
    .line 11
    invoke-virtual {p0}, Ls6/B$a;->i()Ls6/t$a;

    .line 12
    .line 13
    .line 14
    move-result-object v0

    .line 15
    invoke-virtual {v0, p1, p2}, Ls6/t$a;->h(Ljava/lang/String;Ljava/lang/String;)Ls6/t$a;

    .line 16
    .line 17
    .line 18
    return-object p0
.end method

.method public l(Ls6/t;)Ls6/B$a;
    .locals 1

    .line 1
    const-string v0, "headers"

    .line 2
    .line 3
    invoke-static {p1, v0}, Lkotlin/jvm/internal/r;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p1}, Ls6/t;->f()Ls6/t$a;

    .line 7
    .line 8
    .line 9
    move-result-object p1

    .line 10
    invoke-virtual {p0, p1}, Ls6/B$a;->y(Ls6/t$a;)V

    .line 11
    .line 12
    .line 13
    return-object p0
.end method

.method public final m(Lx6/c;)V
    .locals 1

    .line 1
    const-string v0, "deferredTrailers"

    .line 2
    .line 3
    invoke-static {p1, v0}, Lkotlin/jvm/internal/r;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    iput-object p1, p0, Ls6/B$a;->m:Lx6/c;

    .line 7
    .line 8
    return-void
.end method

.method public n(Ljava/lang/String;)Ls6/B$a;
    .locals 1

    .line 1
    const-string v0, "message"

    .line 2
    .line 3
    invoke-static {p1, v0}, Lkotlin/jvm/internal/r;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0, p1}, Ls6/B$a;->z(Ljava/lang/String;)V

    .line 7
    .line 8
    .line 9
    return-object p0
.end method

.method public o(Ls6/B;)Ls6/B$a;
    .locals 1

    .line 1
    const-string v0, "networkResponse"

    .line 2
    .line 3
    invoke-virtual {p0, v0, p1}, Ls6/B$a;->f(Ljava/lang/String;Ls6/B;)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0, p1}, Ls6/B$a;->A(Ls6/B;)V

    .line 7
    .line 8
    .line 9
    return-object p0
.end method

.method public p(Ls6/B;)Ls6/B$a;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Ls6/B$a;->e(Ls6/B;)V

    .line 2
    .line 3
    .line 4
    invoke-virtual {p0, p1}, Ls6/B$a;->B(Ls6/B;)V

    .line 5
    .line 6
    .line 7
    return-object p0
.end method

.method public q(Ls6/y;)Ls6/B$a;
    .locals 1

    .line 1
    const-string v0, "protocol"

    .line 2
    .line 3
    invoke-static {p1, v0}, Lkotlin/jvm/internal/r;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0, p1}, Ls6/B$a;->C(Ls6/y;)V

    .line 7
    .line 8
    .line 9
    return-object p0
.end method

.method public r(J)Ls6/B$a;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Ls6/B$a;->D(J)V

    .line 2
    .line 3
    .line 4
    return-object p0
.end method

.method public s(Ls6/z;)Ls6/B$a;
    .locals 1

    .line 1
    const-string v0, "request"

    .line 2
    .line 3
    invoke-static {p1, v0}, Lkotlin/jvm/internal/r;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0, p1}, Ls6/B$a;->E(Ls6/z;)V

    .line 7
    .line 8
    .line 9
    return-object p0
.end method

.method public t(J)Ls6/B$a;
    .locals 0

    .line 1
    invoke-virtual {p0, p1, p2}, Ls6/B$a;->F(J)V

    .line 2
    .line 3
    .line 4
    return-object p0
.end method

.method public final u(Ls6/C;)V
    .locals 0

    .line 1
    iput-object p1, p0, Ls6/B$a;->g:Ls6/C;

    .line 2
    .line 3
    return-void
.end method

.method public final v(Ls6/B;)V
    .locals 0

    .line 1
    iput-object p1, p0, Ls6/B$a;->i:Ls6/B;

    .line 2
    .line 3
    return-void
.end method

.method public final w(I)V
    .locals 0

    .line 1
    iput p1, p0, Ls6/B$a;->c:I

    .line 2
    .line 3
    return-void
.end method

.method public final x(Ls6/s;)V
    .locals 0

    .line 1
    iput-object p1, p0, Ls6/B$a;->e:Ls6/s;

    .line 2
    .line 3
    return-void
.end method

.method public final y(Ls6/t$a;)V
    .locals 1

    .line 1
    const-string v0, "<set-?>"

    .line 2
    .line 3
    invoke-static {p1, v0}, Lkotlin/jvm/internal/r;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    iput-object p1, p0, Ls6/B$a;->f:Ls6/t$a;

    .line 7
    .line 8
    return-void
.end method

.method public final z(Ljava/lang/String;)V
    .locals 0

    .line 1
    iput-object p1, p0, Ls6/B$a;->d:Ljava/lang/String;

    .line 2
    .line 3
    return-void
.end method
