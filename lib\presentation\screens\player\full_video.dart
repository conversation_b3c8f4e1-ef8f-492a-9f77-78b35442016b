part of '../screens.dart';

class FullVideoScreen extends StatefulWidget {
  const FullVideoScreen({
    super.key,
    required this.link,
    required this.title,
    this.isLive = false,
    this.videoId,
    this.chapters,
    this.qualities,
    this.audioTracks,
    this.subtitleTracks,
  });
  final String link;
  final String title;
  final bool isLive;
  final String? videoId;
  final List<Chapter>? chapters;
  final List<VideoQuality>? qualities;
  final List<AudioTrack>? audioTracks;
  final List<SubtitleTrack>? subtitleTracks;

  @override
  State<FullVideoScreen> createState() => _FullVideoScreenState();
}

class _FullVideoScreenState extends State<FullVideoScreen> {
  late VlcPlayerController _videoPlayerController;
  final VideoSettingsManager _settings = VideoSettingsManager();
  final PlaylistManager _playlist = PlaylistManager();

  bool isPlayed = true;
  bool progress = true;
  bool showControllersVideo = true;
  String position = '';
  String duration = '';
  double sliderValue = 0.0;
  bool validPosition = false;
  double _currentVolume = 0.0;
  double _currentBright = 0.0;
  late Timer timer;

  // ميزات جديدة
  final bool _isInitialized = false;
  final int _reconnectAttempts = 0;
  final int _maxReconnectAttempts = 3;
  Timer? _positionSaveTimer;

  // final ScreenBrightnessUtil _screenBrightnessUtil = ScreenBrightnessUtil();

  _settingPage() async {
    try {
      // double brightness = await _screenBrightnessUtil.getBrightness();
      // if (brightness == -1) {
      //   debugPrint("Oops... something wrong!");
      // } else {
      //   _currentBright = brightness;
      // }

      ///default volume is half
      VolumeController().listener((volume) {
        setState(() => _currentVolume = volume);
      });
      VolumeController().getVolume().then((volume) => _currentVolume = volume);

      setState(() {});
    } catch (e) {
      debugPrint("Error: setting: $e");
    }
  }

  @override
  void initState() {
    super.initState();
    _initializeSettings();
    _initializeVideoPlayer();
    _settingPage();
    _startControllerTimer();
    _startPositionSaveTimer();
  }

  Future<void> _initializeSettings() async {
    await _settings.init();
    WakelockPlus.enable();
  }

  Future<void> _initializeVideoPlayer() async {
    // استعادة موضع التشغيل المحفوظ
    int? savedPosition;
    if (widget.videoId != null) {
      savedPosition = await _settings.getVideoPosition(widget.videoId!);
    }

    _videoPlayerController = VlcPlayerController.network(
      widget.link,
      hwAcc: _settings.hardwareAcceleration ? HwAcc.full : HwAcc.disabled,
      autoPlay: _settings.autoPlay,
      autoInitialize: true,
      options: VlcPlayerOptions(
        advanced: VlcAdvancedOptions([
          VlcAdvancedOptions.networkCaching(_settings.networkCaching),
          VlcAdvancedOptions.liveCaching(_settings.liveCaching),
        ]),
        http: VlcHttpOptions([
          VlcHttpOptions.httpReconnect(_settings.autoReconnect),
        ]),
        rtp: VlcRtpOptions([
          VlcRtpOptions.rtpOverRtsp(true),
        ]),
      ),
    );

    _videoPlayerController.addListener(listener);

    // الانتقال إلى الموضع المحفوظ
    if (savedPosition != null && savedPosition > 0) {
      Future.delayed(const Duration(seconds: 2), () {
        _videoPlayerController.seekTo(Duration(seconds: savedPosition!));
      });
    }

    // تطبيق سرعة التشغيل المحفوظة
    Future.delayed(const Duration(seconds: 1), () {
      _videoPlayerController.setPlaybackSpeed(_settings.playbackSpeed);
    });
  }

  void _startControllerTimer() {
    timer = Timer.periodic(const Duration(seconds: 5), (timer) {
      if (showControllersVideo) {
        setState(() {
          showControllersVideo = false;
        });
      }
    });
  }

  void _startPositionSaveTimer() {
    if (widget.videoId != null && !widget.isLive) {
      _positionSaveTimer = Timer.periodic(const Duration(seconds: 10), (timer) {
        final position = _videoPlayerController.value.position.inSeconds;
        if (position > 0) {
          _settings.saveVideoPosition(widget.videoId!, position);
        }
      });
    }
  }

  void listener() async {
    if (!mounted) return;

    if (progress) {
      if (_videoPlayerController.value.isPlaying) {
        setState(() {
          progress = false;
        });
      }
    }

    if (_videoPlayerController.value.isInitialized) {
      var oPosition = _videoPlayerController.value.position;
      var oDuration = _videoPlayerController.value.duration;

      if (oDuration.inHours == 0) {
        var strPosition = oPosition.toString().split('.')[0];
        var strDuration = oDuration.toString().split('.')[0];
        position = "${strPosition.split(':')[1]}:${strPosition.split(':')[2]}";
        duration = "${strDuration.split(':')[1]}:${strDuration.split(':')[2]}";
      } else {
        position = oPosition.toString().split('.')[0];
        duration = oDuration.toString().split('.')[0];
      }
      validPosition = oDuration.compareTo(oPosition) >= 0;
      sliderValue = validPosition ? oPosition.inSeconds.toDouble() : 0;
      setState(() {});
    }
  }

  @override
  void dispose() async {
    super.dispose();

    // حفظ موضع التشغيل النهائي
    if (widget.videoId != null && !widget.isLive) {
      final position = _videoPlayerController.value.position.inSeconds;
      if (position > 0) {
        await _settings.saveVideoPosition(widget.videoId!, position);
      }
    }

    // إيقاف المؤقتات
    timer.cancel();
    _positionSaveTimer?.cancel();

    // تنظيف المشغل
    await _videoPlayerController.stopRendererScanning();
    await _videoPlayerController.dispose();
    VolumeController().removeListener();
    WakelockPlus.disable();
  }

  @override
  Widget build(BuildContext context) {
    debugPrint("SIZE: ${MediaQuery.of(context).size.width}");
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        alignment: Alignment.bottomCenter,
        children: [
          Container(
            width: getSize(context).width,
            height: getSize(context).height,
            color: Colors.black,
            child: VlcPlayer(
              controller: _videoPlayerController,
              aspectRatio: 16 / 9,
              virtualDisplay: true,
              placeholder: const SizedBox(),
            ),
          ),

          if (progress)
            const Center(
                child: CircularProgressIndicator(
              color: kColorPrimary,
            )),

          ///Controllers with Advanced Gestures
          VideoGestureDetector(
            controller: _videoPlayerController,
            onTap: () {
              setState(() {
                showControllersVideo = !showControllersVideo;
              });
            },
            onVolumeChanged: (volume) {
              setState(() {
                _currentVolume = volume;
              });
            },
            onBrightnessChanged: (brightness) {
              setState(() {
                _currentBright = brightness;
              });
            },
            onSeek: (duration) {
              // يمكن إضافة منطق إضافي هنا
            },
            child: Container(
              width: getSize(context).width,
              height: getSize(context).height,
              color: Colors.transparent,
              child: AnimatedSize(
                duration: const Duration(milliseconds: 200),
                child: !showControllersVideo
                    ? const SizedBox()
                    : SafeArea(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            ///Back & Title
                            Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                IconButton(
                                  focusColor: kColorFocus,
                                  onPressed: () async {
                                    await Future.delayed(
                                            const Duration(milliseconds: 1000))
                                        .then((value) {
                                      Get.back(
                                          result: progress
                                              ? null
                                              : [
                                                  sliderValue,
                                                  _videoPlayerController
                                                      .value.duration.inSeconds
                                                      .toDouble()
                                                ]);
                                    });
                                  },
                                  icon: Icon(
                                    FontAwesomeIcons.chevronLeft,
                                    size: 19.sp,
                                  ),
                                ),
                                const SizedBox(width: 5),
                                Expanded(
                                  child: Text(
                                    widget.title,
                                    maxLines: 1,
                                    style: Get.textTheme.labelLarge!.copyWith(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 18.sp,
                                    ),
                                  ),
                                ),
                              ],
                            ),

                            ///Advanced Controls
                            if (!progress)
                              AdvancedVideoControls(
                                controller: _videoPlayerController,
                                isVisible: showControllersVideo,
                                chapters: widget.chapters,
                                qualities: widget.qualities,
                                audioTracks: widget.audioTracks,
                                subtitleTracks: widget.subtitleTracks,
                              ),
                          ],
                        ),
                      ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
