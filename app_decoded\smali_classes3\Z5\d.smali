.class public interface abstract LZ5/d;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LZ5/u;
.implements LZ5/t;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LZ5/d$a;
    }
.end annotation


# static fields
.field public static final R7:LZ5/d$a;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    sget-object v0, LZ5/d$a;->a:LZ5/d$a;

    .line 2
    .line 3
    sput-object v0, LZ5/d;->R7:LZ5/d$a;

    .line 4
    .line 5
    return-void
.end method
