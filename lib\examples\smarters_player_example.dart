import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../presentation/screens/player/smarters_video_player.dart';

class SmartersPlayerExample extends StatelessWidget {
  const SmartersPlayerExample({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Smarters Video Player Example'),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
      ),
      backgroundColor: Colors.black,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text(
              'اختبار مشغل الفيديو المتقدم',
              style: TextStyle(
                color: Colors.white,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 40),
            
            // Live Stream Example
            ElevatedButton.icon(
              onPressed: () {
                Get.to(() => const SmartersVideoPlayer(
                  videoUrl: 'https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8',
                  title: 'مثال على البث المباشر',
                  isLive: true,
                ));
              },
              icon: const Icon(Icons.live_tv),
              label: const Text('تشغيل بث مباشر'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
              ),
            ),
            
            const SizedBox(height: 20),
            
            // VOD Example
            ElevatedButton.icon(
              onPressed: () {
                Get.to(() => const SmartersVideoPlayer(
                  videoUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
                  title: 'فيديو تجريبي - Big Buck Bunny',
                  isLive: false,
                ));
              },
              icon: const Icon(Icons.movie),
              label: const Text('تشغيل فيديو'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
              ),
            ),
            
            const SizedBox(height: 20),
            
            // HLS Stream Example
            ElevatedButton.icon(
              onPressed: () {
                Get.to(() => const SmartersVideoPlayer(
                  videoUrl: 'https://demo.unified-streaming.com/k8s/features/stable/video/tears-of-steel/tears-of-steel.ism/.m3u8',
                  title: 'HLS Stream - Tears of Steel',
                  isLive: false,
                ));
              },
              icon: const Icon(Icons.stream),
              label: const Text('تشغيل HLS'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
              ),
            ),
            
            const SizedBox(height: 40),
            
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: Text(
                'الميزات المتاحة:\n'
                '• التحكم بالإيماءات (Volume, Brightness, Seek)\n'
                '• أوضاع العرض المتعددة (Fill, Fit Width, Fit Height)\n'
                '• أدوات تحكم متقدمة\n'
                '• دعم البث المباشر والفيديوهات\n'
                '• تصميم مشابه لـ IPTV Smarters Pro',
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
