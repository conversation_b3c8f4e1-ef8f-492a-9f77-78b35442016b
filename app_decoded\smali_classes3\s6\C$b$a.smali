.class public final Ls6/C$b$a;
.super Ls6/C;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Ls6/C$b;->a(LG6/g;Ls6/w;J)Ls6/C;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation


# instance fields
.field public final synthetic a:Ls6/w;

.field public final synthetic b:J

.field public final synthetic c:LG6/g;


# direct methods
.method public constructor <init>(Ls6/w;JLG6/g;)V
    .locals 0

    .line 1
    iput-object p1, p0, Ls6/C$b$a;->a:Ls6/w;

    .line 2
    .line 3
    iput-wide p2, p0, Ls6/C$b$a;->b:J

    .line 4
    .line 5
    iput-object p4, p0, Ls6/C$b$a;->c:LG6/g;

    .line 6
    .line 7
    invoke-direct {p0}, Ls6/C;-><init>()V

    .line 8
    .line 9
    .line 10
    return-void
.end method


# virtual methods
.method public contentLength()J
    .locals 2

    .line 1
    iget-wide v0, p0, Ls6/C$b$a;->b:J

    .line 2
    .line 3
    return-wide v0
.end method

.method public contentType()Ls6/w;
    .locals 1

    .line 1
    iget-object v0, p0, Ls6/C$b$a;->a:Ls6/w;

    .line 2
    .line 3
    return-object v0
.end method

.method public source()LG6/g;
    .locals 1

    .line 1
    iget-object v0, p0, Ls6/C$b$a;->c:LG6/g;

    .line 2
    .line 3
    return-object v0
.end method
