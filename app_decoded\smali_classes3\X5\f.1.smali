.class public final LX5/f;
.super LX5/d0;
.source "SourceFile"


# instance fields
.field public final i:Ljava/lang/Thread;


# direct methods
.method public constructor <init>(Ljava/lang/Thread;)V
    .locals 0

    .line 1
    invoke-direct {p0}, LX5/d0;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LX5/f;->i:Ljava/lang/Thread;

    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public e1()Ljava/lang/Thread;
    .locals 1

    .line 1
    iget-object v0, p0, LX5/f;->i:Ljava/lang/Thread;

    .line 2
    .line 3
    return-object v0
.end method
