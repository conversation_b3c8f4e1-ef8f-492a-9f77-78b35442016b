import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';

class VideoSettingsManager extends ChangeNotifier {
  static final VideoSettingsManager _instance = VideoSettingsManager._internal();
  factory VideoSettingsManager() => _instance;
  VideoSettingsManager._internal();

  SharedPreferences? _prefs;

  // إعدادات التشغيل
  double _playbackSpeed = 1.0;
  double _volume = 0.5;
  double _brightness = 0.5;
  bool _autoPlay = true;
  bool _rememberPosition = true;
  int _skipDuration = 10; // ثوانٍ
  
  // إعدادات العرض
  String _aspectRatio = "16:9";
  String _videoQuality = "Auto";
  bool _hardwareAcceleration = true;
  
  // إعدادات الشبكة
  int _networkCaching = 2000;
  int _liveCaching = 2000;
  bool _autoReconnect = true;

  // Getters
  double get playbackSpeed => _playbackSpeed;
  double get volume => _volume;
  double get brightness => _brightness;
  bool get autoPlay => _autoPlay;
  bool get rememberPosition => _rememberPosition;
  int get skipDuration => _skipDuration;
  String get aspectRatio => _aspectRatio;
  String get videoQuality => _videoQuality;
  bool get hardwareAcceleration => _hardwareAcceleration;
  int get networkCaching => _networkCaching;
  int get liveCaching => _liveCaching;
  bool get autoReconnect => _autoReconnect;

  Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
    await _loadSettings();
  }

  Future<void> _loadSettings() async {
    if (_prefs == null) return;

    _playbackSpeed = _prefs!.getDouble('playback_speed') ?? 1.0;
    _volume = _prefs!.getDouble('volume') ?? 0.5;
    _brightness = _prefs!.getDouble('brightness') ?? 0.5;
    _autoPlay = _prefs!.getBool('auto_play') ?? true;
    _rememberPosition = _prefs!.getBool('remember_position') ?? true;
    _skipDuration = _prefs!.getInt('skip_duration') ?? 10;
    _aspectRatio = _prefs!.getString('aspect_ratio') ?? "16:9";
    _videoQuality = _prefs!.getString('video_quality') ?? "Auto";
    _hardwareAcceleration = _prefs!.getBool('hardware_acceleration') ?? true;
    _networkCaching = _prefs!.getInt('network_caching') ?? 2000;
    _liveCaching = _prefs!.getInt('live_caching') ?? 2000;
    _autoReconnect = _prefs!.getBool('auto_reconnect') ?? true;

    notifyListeners();
  }

  Future<void> setPlaybackSpeed(double speed) async {
    _playbackSpeed = speed;
    await _prefs?.setDouble('playback_speed', speed);
    notifyListeners();
  }

  Future<void> setVolume(double volume) async {
    _volume = volume;
    await _prefs?.setDouble('volume', volume);
    notifyListeners();
  }

  Future<void> setBrightness(double brightness) async {
    _brightness = brightness;
    await _prefs?.setDouble('brightness', brightness);
    notifyListeners();
  }

  Future<void> setAutoPlay(bool autoPlay) async {
    _autoPlay = autoPlay;
    await _prefs?.setBool('auto_play', autoPlay);
    notifyListeners();
  }

  Future<void> setRememberPosition(bool remember) async {
    _rememberPosition = remember;
    await _prefs?.setBool('remember_position', remember);
    notifyListeners();
  }

  Future<void> setSkipDuration(int duration) async {
    _skipDuration = duration;
    await _prefs?.setInt('skip_duration', duration);
    notifyListeners();
  }

  Future<void> setAspectRatio(String ratio) async {
    _aspectRatio = ratio;
    await _prefs?.setString('aspect_ratio', ratio);
    notifyListeners();
  }

  Future<void> setVideoQuality(String quality) async {
    _videoQuality = quality;
    await _prefs?.setString('video_quality', quality);
    notifyListeners();
  }

  Future<void> setHardwareAcceleration(bool enabled) async {
    _hardwareAcceleration = enabled;
    await _prefs?.setBool('hardware_acceleration', enabled);
    notifyListeners();
  }

  Future<void> setNetworkCaching(int caching) async {
    _networkCaching = caching;
    await _prefs?.setInt('network_caching', caching);
    notifyListeners();
  }

  Future<void> setLiveCaching(int caching) async {
    _liveCaching = caching;
    await _prefs?.setInt('live_caching', caching);
    notifyListeners();
  }

  Future<void> setAutoReconnect(bool enabled) async {
    _autoReconnect = enabled;
    await _prefs?.setBool('auto_reconnect', enabled);
    notifyListeners();
  }

  // حفظ واستعادة موضع التشغيل
  Future<void> saveVideoPosition(String videoId, int position) async {
    if (_rememberPosition) {
      await _prefs?.setInt('video_position_$videoId', position);
    }
  }

  Future<int?> getVideoPosition(String videoId) async {
    if (_rememberPosition) {
      return _prefs?.getInt('video_position_$videoId');
    }
    return null;
  }

  Future<void> clearVideoPosition(String videoId) async {
    await _prefs?.remove('video_position_$videoId');
  }

  // إعادة تعيين جميع الإعدادات
  Future<void> resetToDefaults() async {
    await _prefs?.clear();
    await _loadSettings();
  }
}
