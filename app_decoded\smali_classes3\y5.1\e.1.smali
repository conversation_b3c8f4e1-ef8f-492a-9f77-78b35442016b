.class public abstract Ly5/e;
.super Ly5/f;
.source "SourceFile"


# direct methods
.method public static bridge synthetic a(<PERSON>java/lang/Throwable;Ljava/lang/Throwable;)V
    .locals 0

    .line 1
    invoke-static {p0, p1}, Ly5/f;->a(<PERSON><PERSON><PERSON>/lang/Throwable;<PERSON><PERSON><PERSON>/lang/Throwable;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static bridge synthetic b(Ljava/lang/Throwable;)Ljava/lang/String;
    .locals 0

    .line 1
    invoke-static {p0}, Ly5/f;->b(Ljava/lang/Throwable;)Ljava/lang/String;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method
