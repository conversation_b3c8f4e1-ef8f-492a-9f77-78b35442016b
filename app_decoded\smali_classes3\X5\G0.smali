.class public final LX5/G0;
.super LD5/a;
.source "SourceFile"

# interfaces
.implements LX5/s0;


# static fields
.field public static final b:LX5/G0;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, LX5/G0;

    .line 2
    .line 3
    invoke-direct {v0}, LX5/G0;-><init>()V

    .line 4
    .line 5
    .line 6
    sput-object v0, LX5/G0;->b:LX5/G0;

    .line 7
    .line 8
    return-void
.end method

.method public constructor <init>()V
    .locals 1

    .line 1
    sget-object v0, LX5/s0;->Q7:LX5/s0$b;

    .line 2
    .line 3
    invoke-direct {p0, v0}, LD5/a;-><init>(LD5/g$c;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public B(LM5/k;)LX5/Z;
    .locals 0

    .line 1
    sget-object p1, LX5/H0;->a:LX5/H0;

    .line 2
    .line 3
    return-object p1
.end method

.method public I0(ZZLM5/k;)LX5/Z;
    .locals 0

    .line 1
    sget-object p1, LX5/H0;->a:LX5/H0;

    .line 2
    .line 3
    return-object p1
.end method

.method public K()Ljava/util/concurrent/CancellationException;
    .locals 2

    .line 1
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 2
    .line 3
    const-string v1, "This job is always active"

    .line 4
    .line 5
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 6
    .line 7
    .line 8
    throw v0
.end method

.method public Q(LX5/u;)LX5/s;
    .locals 0

    .line 1
    sget-object p1, LX5/H0;->a:LX5/H0;

    .line 2
    .line 3
    return-object p1
.end method

.method public a()Z
    .locals 1

    .line 1
    const/4 v0, 0x1

    .line 2
    return v0
.end method

.method public c(Ljava/util/concurrent/CancellationException;)V
    .locals 0

    .line 1
    return-void
.end method

.method public getParent()LX5/s0;
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    return-object v0
.end method

.method public isCancelled()Z
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    return v0
.end method

.method public start()Z
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    return v0
.end method

.method public t(LD5/d;)Ljava/lang/Object;
    .locals 1

    .line 1
    new-instance p1, Ljava/lang/UnsupportedOperationException;

    .line 2
    .line 3
    const-string v0, "This job is always active"

    .line 4
    .line 5
    invoke-direct {p1, v0}, Ljava/lang/UnsupportedOperationException;-><init>(Ljava/lang/String;)V

    .line 6
    .line 7
    .line 8
    throw p1
.end method

.method public toString()Ljava/lang/String;
    .locals 1

    .line 1
    const-string v0, "NonCancellable"

    .line 2
    .line 3
    return-object v0
.end method
