.class public final Ls6/x$a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Ls6/x;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation


# instance fields
.field public A:I

.field public B:I

.field public C:J

.field public D:Lx6/h;

.field public a:Ls6/p;

.field public b:Ls6/k;

.field public final c:Ljava/util/List;

.field public final d:Ljava/util/List;

.field public e:Ls6/r$c;

.field public f:Z

.field public g:Ls6/b;

.field public h:Z

.field public i:Z

.field public j:Ls6/n;

.field public k:Ls6/c;

.field public l:Ls6/q;

.field public m:Ljava/net/Proxy;

.field public n:Ljava/net/ProxySelector;

.field public o:Ls6/b;

.field public p:Ljavax/net/SocketFactory;

.field public q:Ljavax/net/ssl/SSLSocketFactory;

.field public r:Ljavax/net/ssl/X509TrustManager;

.field public s:Ljava/util/List;

.field public t:Ljava/util/List;

.field public u:Ljavax/net/ssl/HostnameVerifier;

.field public v:Ls6/g;

.field public w:LF6/c;

.field public x:I

.field public y:I

.field public z:I


# direct methods
.method public constructor <init>()V
    .locals 2

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    new-instance v0, Ls6/p;

    .line 5
    .line 6
    invoke-direct {v0}, Ls6/p;-><init>()V

    .line 7
    .line 8
    .line 9
    iput-object v0, p0, Ls6/x$a;->a:Ls6/p;

    .line 10
    .line 11
    new-instance v0, Ls6/k;

    .line 12
    .line 13
    invoke-direct {v0}, Ls6/k;-><init>()V

    .line 14
    .line 15
    .line 16
    iput-object v0, p0, Ls6/x$a;->b:Ls6/k;

    .line 17
    .line 18
    new-instance v0, Ljava/util/ArrayList;

    .line 19
    .line 20
    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 21
    .line 22
    .line 23
    iput-object v0, p0, Ls6/x$a;->c:Ljava/util/List;

    .line 24
    .line 25
    new-instance v0, Ljava/util/ArrayList;

    .line 26
    .line 27
    invoke-direct {v0}, Ljava/util/ArrayList;-><init>()V

    .line 28
    .line 29
    .line 30
    iput-object v0, p0, Ls6/x$a;->d:Ljava/util/List;

    .line 31
    .line 32
    sget-object v0, Ls6/r;->b:Ls6/r;

    .line 33
    .line 34
    invoke-static {v0}, Lt6/d;->g(Ls6/r;)Ls6/r$c;

    .line 35
    .line 36
    .line 37
    move-result-object v0

    .line 38
    iput-object v0, p0, Ls6/x$a;->e:Ls6/r$c;

    .line 39
    .line 40
    const/4 v0, 0x1

    .line 41
    iput-boolean v0, p0, Ls6/x$a;->f:Z

    .line 42
    .line 43
    sget-object v1, Ls6/b;->b:Ls6/b;

    .line 44
    .line 45
    iput-object v1, p0, Ls6/x$a;->g:Ls6/b;

    .line 46
    .line 47
    iput-boolean v0, p0, Ls6/x$a;->h:Z

    .line 48
    .line 49
    iput-boolean v0, p0, Ls6/x$a;->i:Z

    .line 50
    .line 51
    sget-object v0, Ls6/n;->b:Ls6/n;

    .line 52
    .line 53
    iput-object v0, p0, Ls6/x$a;->j:Ls6/n;

    .line 54
    .line 55
    sget-object v0, Ls6/q;->b:Ls6/q;

    .line 56
    .line 57
    iput-object v0, p0, Ls6/x$a;->l:Ls6/q;

    .line 58
    .line 59
    iput-object v1, p0, Ls6/x$a;->o:Ls6/b;

    .line 60
    .line 61
    invoke-static {}, Ljavax/net/SocketFactory;->getDefault()Ljavax/net/SocketFactory;

    .line 62
    .line 63
    .line 64
    move-result-object v0

    .line 65
    const-string v1, "getDefault()"

    .line 66
    .line 67
    invoke-static {v0, v1}, Lkotlin/jvm/internal/r;->e(Ljava/lang/Object;Ljava/lang/String;)V

    .line 68
    .line 69
    .line 70
    iput-object v0, p0, Ls6/x$a;->p:Ljavax/net/SocketFactory;

    .line 71
    .line 72
    sget-object v0, Ls6/x;->E:Ls6/x$b;

    .line 73
    .line 74
    invoke-virtual {v0}, Ls6/x$b;->a()Ljava/util/List;

    .line 75
    .line 76
    .line 77
    move-result-object v1

    .line 78
    iput-object v1, p0, Ls6/x$a;->s:Ljava/util/List;

    .line 79
    .line 80
    invoke-virtual {v0}, Ls6/x$b;->b()Ljava/util/List;

    .line 81
    .line 82
    .line 83
    move-result-object v0

    .line 84
    iput-object v0, p0, Ls6/x$a;->t:Ljava/util/List;

    .line 85
    .line 86
    sget-object v0, LF6/d;->a:LF6/d;

    .line 87
    .line 88
    iput-object v0, p0, Ls6/x$a;->u:Ljavax/net/ssl/HostnameVerifier;

    .line 89
    .line 90
    sget-object v0, Ls6/g;->d:Ls6/g;

    .line 91
    .line 92
    iput-object v0, p0, Ls6/x$a;->v:Ls6/g;

    .line 93
    .line 94
    const/16 v0, 0x2710

    .line 95
    .line 96
    iput v0, p0, Ls6/x$a;->y:I

    .line 97
    .line 98
    iput v0, p0, Ls6/x$a;->z:I

    .line 99
    .line 100
    iput v0, p0, Ls6/x$a;->A:I

    .line 101
    .line 102
    const-wide/16 v0, 0x400

    .line 103
    .line 104
    iput-wide v0, p0, Ls6/x$a;->C:J

    .line 105
    .line 106
    return-void
.end method


# virtual methods
.method public final A()Ljava/net/Proxy;
    .locals 1

    .line 1
    iget-object v0, p0, Ls6/x$a;->m:Ljava/net/Proxy;

    .line 2
    .line 3
    return-object v0
.end method

.method public final B()Ls6/b;
    .locals 1

    .line 1
    iget-object v0, p0, Ls6/x$a;->o:Ls6/b;

    .line 2
    .line 3
    return-object v0
.end method

.method public final C()Ljava/net/ProxySelector;
    .locals 1

    .line 1
    iget-object v0, p0, Ls6/x$a;->n:Ljava/net/ProxySelector;

    .line 2
    .line 3
    return-object v0
.end method

.method public final D()I
    .locals 1

    .line 1
    iget v0, p0, Ls6/x$a;->z:I

    .line 2
    .line 3
    return v0
.end method

.method public final E()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Ls6/x$a;->f:Z

    .line 2
    .line 3
    return v0
.end method

.method public final F()Lx6/h;
    .locals 1

    .line 1
    iget-object v0, p0, Ls6/x$a;->D:Lx6/h;

    .line 2
    .line 3
    return-object v0
.end method

.method public final G()Ljavax/net/SocketFactory;
    .locals 1

    .line 1
    iget-object v0, p0, Ls6/x$a;->p:Ljavax/net/SocketFactory;

    .line 2
    .line 3
    return-object v0
.end method

.method public final H()Ljavax/net/ssl/SSLSocketFactory;
    .locals 1

    .line 1
    iget-object v0, p0, Ls6/x$a;->q:Ljavax/net/ssl/SSLSocketFactory;

    .line 2
    .line 3
    return-object v0
.end method

.method public final I()I
    .locals 1

    .line 1
    iget v0, p0, Ls6/x$a;->A:I

    .line 2
    .line 3
    return v0
.end method

.method public final J()Ljavax/net/ssl/X509TrustManager;
    .locals 1

    .line 1
    iget-object v0, p0, Ls6/x$a;->r:Ljavax/net/ssl/X509TrustManager;

    .line 2
    .line 3
    return-object v0
.end method

.method public final K(Ljava/net/ProxySelector;)Ls6/x$a;
    .locals 1

    .line 1
    const-string v0, "proxySelector"

    .line 2
    .line 3
    invoke-static {p1, v0}, Lkotlin/jvm/internal/r;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0}, Ls6/x$a;->C()Ljava/net/ProxySelector;

    .line 7
    .line 8
    .line 9
    move-result-object v0

    .line 10
    invoke-static {p1, v0}, Lkotlin/jvm/internal/r;->b(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 11
    .line 12
    .line 13
    move-result v0

    .line 14
    if-nez v0, :cond_0

    .line 15
    .line 16
    const/4 v0, 0x0

    .line 17
    invoke-virtual {p0, v0}, Ls6/x$a;->S(Lx6/h;)V

    .line 18
    .line 19
    .line 20
    :cond_0
    invoke-virtual {p0, p1}, Ls6/x$a;->Q(Ljava/net/ProxySelector;)V

    .line 21
    .line 22
    .line 23
    return-object p0
.end method

.method public final L(JLjava/util/concurrent/TimeUnit;)Ls6/x$a;
    .locals 1

    .line 1
    const-string v0, "unit"

    .line 2
    .line 3
    invoke-static {p3, v0}, Lkotlin/jvm/internal/r;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    const-string v0, "timeout"

    .line 7
    .line 8
    invoke-static {v0, p1, p2, p3}, Lt6/d;->k(Ljava/lang/String;JLjava/util/concurrent/TimeUnit;)I

    .line 9
    .line 10
    .line 11
    move-result p1

    .line 12
    invoke-virtual {p0, p1}, Ls6/x$a;->R(I)V

    .line 13
    .line 14
    .line 15
    return-object p0
.end method

.method public final M(Ls6/c;)V
    .locals 0

    .line 1
    iput-object p1, p0, Ls6/x$a;->k:Ls6/c;

    .line 2
    .line 3
    return-void
.end method

.method public final N(I)V
    .locals 0

    .line 1
    iput p1, p0, Ls6/x$a;->y:I

    .line 2
    .line 3
    return-void
.end method

.method public final O(Z)V
    .locals 0

    .line 1
    iput-boolean p1, p0, Ls6/x$a;->h:Z

    .line 2
    .line 3
    return-void
.end method

.method public final P(Z)V
    .locals 0

    .line 1
    iput-boolean p1, p0, Ls6/x$a;->i:Z

    .line 2
    .line 3
    return-void
.end method

.method public final Q(Ljava/net/ProxySelector;)V
    .locals 0

    .line 1
    iput-object p1, p0, Ls6/x$a;->n:Ljava/net/ProxySelector;

    .line 2
    .line 3
    return-void
.end method

.method public final R(I)V
    .locals 0

    .line 1
    iput p1, p0, Ls6/x$a;->z:I

    .line 2
    .line 3
    return-void
.end method

.method public final S(Lx6/h;)V
    .locals 0

    .line 1
    iput-object p1, p0, Ls6/x$a;->D:Lx6/h;

    .line 2
    .line 3
    return-void
.end method

.method public final a(Ls6/v;)Ls6/x$a;
    .locals 1

    .line 1
    const-string v0, "interceptor"

    .line 2
    .line 3
    invoke-static {p1, v0}, Lkotlin/jvm/internal/r;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    invoke-virtual {p0}, Ls6/x$a;->v()Ljava/util/List;

    .line 7
    .line 8
    .line 9
    move-result-object v0

    .line 10
    check-cast v0, Ljava/util/Collection;

    .line 11
    .line 12
    invoke-interface {v0, p1}, Ljava/util/Collection;->add(Ljava/lang/Object;)Z

    .line 13
    .line 14
    .line 15
    return-object p0
.end method

.method public final b()Ls6/x;
    .locals 1

    .line 1
    new-instance v0, Ls6/x;

    .line 2
    .line 3
    invoke-direct {v0, p0}, Ls6/x;-><init>(Ls6/x$a;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method

.method public final c(Ls6/c;)Ls6/x$a;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Ls6/x$a;->M(Ls6/c;)V

    .line 2
    .line 3
    .line 4
    return-object p0
.end method

.method public final d(JLjava/util/concurrent/TimeUnit;)Ls6/x$a;
    .locals 1

    .line 1
    const-string v0, "unit"

    .line 2
    .line 3
    invoke-static {p3, v0}, Lkotlin/jvm/internal/r;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    const-string v0, "timeout"

    .line 7
    .line 8
    invoke-static {v0, p1, p2, p3}, Lt6/d;->k(Ljava/lang/String;JLjava/util/concurrent/TimeUnit;)I

    .line 9
    .line 10
    .line 11
    move-result p1

    .line 12
    invoke-virtual {p0, p1}, Ls6/x$a;->N(I)V

    .line 13
    .line 14
    .line 15
    return-object p0
.end method

.method public final e(Z)Ls6/x$a;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Ls6/x$a;->O(Z)V

    .line 2
    .line 3
    .line 4
    return-object p0
.end method

.method public final f(Z)Ls6/x$a;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, Ls6/x$a;->P(Z)V

    .line 2
    .line 3
    .line 4
    return-object p0
.end method

.method public final g()Ls6/b;
    .locals 1

    .line 1
    iget-object v0, p0, Ls6/x$a;->g:Ls6/b;

    .line 2
    .line 3
    return-object v0
.end method

.method public final h()Ls6/c;
    .locals 1

    .line 1
    iget-object v0, p0, Ls6/x$a;->k:Ls6/c;

    .line 2
    .line 3
    return-object v0
.end method

.method public final i()I
    .locals 1

    .line 1
    iget v0, p0, Ls6/x$a;->x:I

    .line 2
    .line 3
    return v0
.end method

.method public final j()LF6/c;
    .locals 1

    .line 1
    iget-object v0, p0, Ls6/x$a;->w:LF6/c;

    .line 2
    .line 3
    return-object v0
.end method

.method public final k()Ls6/g;
    .locals 1

    .line 1
    iget-object v0, p0, Ls6/x$a;->v:Ls6/g;

    .line 2
    .line 3
    return-object v0
.end method

.method public final l()I
    .locals 1

    .line 1
    iget v0, p0, Ls6/x$a;->y:I

    .line 2
    .line 3
    return v0
.end method

.method public final m()Ls6/k;
    .locals 1

    .line 1
    iget-object v0, p0, Ls6/x$a;->b:Ls6/k;

    .line 2
    .line 3
    return-object v0
.end method

.method public final n()Ljava/util/List;
    .locals 1

    .line 1
    iget-object v0, p0, Ls6/x$a;->s:Ljava/util/List;

    .line 2
    .line 3
    return-object v0
.end method

.method public final o()Ls6/n;
    .locals 1

    .line 1
    iget-object v0, p0, Ls6/x$a;->j:Ls6/n;

    .line 2
    .line 3
    return-object v0
.end method

.method public final p()Ls6/p;
    .locals 1

    .line 1
    iget-object v0, p0, Ls6/x$a;->a:Ls6/p;

    .line 2
    .line 3
    return-object v0
.end method

.method public final q()Ls6/q;
    .locals 1

    .line 1
    iget-object v0, p0, Ls6/x$a;->l:Ls6/q;

    .line 2
    .line 3
    return-object v0
.end method

.method public final r()Ls6/r$c;
    .locals 1

    .line 1
    iget-object v0, p0, Ls6/x$a;->e:Ls6/r$c;

    .line 2
    .line 3
    return-object v0
.end method

.method public final s()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Ls6/x$a;->h:Z

    .line 2
    .line 3
    return v0
.end method

.method public final t()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Ls6/x$a;->i:Z

    .line 2
    .line 3
    return v0
.end method

.method public final u()Ljavax/net/ssl/HostnameVerifier;
    .locals 1

    .line 1
    iget-object v0, p0, Ls6/x$a;->u:Ljavax/net/ssl/HostnameVerifier;

    .line 2
    .line 3
    return-object v0
.end method

.method public final v()Ljava/util/List;
    .locals 1

    .line 1
    iget-object v0, p0, Ls6/x$a;->c:Ljava/util/List;

    .line 2
    .line 3
    return-object v0
.end method

.method public final w()J
    .locals 2

    .line 1
    iget-wide v0, p0, Ls6/x$a;->C:J

    .line 2
    .line 3
    return-wide v0
.end method

.method public final x()Ljava/util/List;
    .locals 1

    .line 1
    iget-object v0, p0, Ls6/x$a;->d:Ljava/util/List;

    .line 2
    .line 3
    return-object v0
.end method

.method public final y()I
    .locals 1

    .line 1
    iget v0, p0, Ls6/x$a;->B:I

    .line 2
    .line 3
    return v0
.end method

.method public final z()Ljava/util/List;
    .locals 1

    .line 1
    iget-object v0, p0, Ls6/x$a;->t:Ljava/util/List;

    .line 2
    .line 3
    return-object v0
.end method
