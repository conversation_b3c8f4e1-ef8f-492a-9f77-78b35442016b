.class public final Ly5/j;
.super Ljava/lang/Object;
.source "SourceFile"


# static fields
.field public static final a:Ly5/j;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, Ly5/j;

    .line 2
    .line 3
    invoke-direct {v0}, Ly5/j;-><init>()V

    .line 4
    .line 5
    .line 6
    sput-object v0, Ly5/j;->a:Ly5/j;

    .line 7
    .line 8
    return-void
.end method

.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static final a()Ly5/i;
    .locals 4

    .line 1
    new-instance v0, Ly5/i;

    .line 2
    .line 3
    const/16 v1, 0x9

    .line 4
    .line 5
    const/16 v2, 0x18

    .line 6
    .line 7
    const/4 v3, 0x1

    .line 8
    invoke-direct {v0, v3, v1, v2}, Ly5/i;-><init>(III)V

    .line 9
    .line 10
    .line 11
    return-object v0
.end method
