.class public interface abstract Ly6/d;
.super Ljava/lang/Object;
.source "SourceFile"


# virtual methods
.method public abstract a()V
.end method

.method public abstract b(Z)Ls6/B$a;
.end method

.method public abstract c()Lx6/f;
.end method

.method public abstract cancel()V
.end method

.method public abstract d(Ls6/B;)J
.end method

.method public abstract e()V
.end method

.method public abstract f(Ls6/B;)LG6/Z;
.end method

.method public abstract g(Ls6/z;)V
.end method

.method public abstract h(Ls6/z;J)LG6/X;
.end method
