.class public abstract LX5/w0;
.super Ljava/lang/Object;
.source "SourceFile"


# direct methods
.method public static final a(LX5/s0;)LX5/y;
    .locals 0

    .line 1
    invoke-static {p0}, LX5/y0;->a(LX5/s0;)LX5/y;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static synthetic b(LX5/s0;ILjava/lang/Object;)LX5/y;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, LX5/y0;->b(LX5/s0;ILjava/lang/Object;)LX5/y;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final c(LD5/g;Ljava/util/concurrent/CancellationException;)V
    .locals 0

    .line 1
    invoke-static {p0, p1}, LX5/y0;->c(LD5/g;Ljava/util/concurrent/CancellationException;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static synthetic d(LD5/g;Ljava/util/concurrent/CancellationException;ILjava/lang/Object;)V
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, LX5/y0;->d(LD5/g;Ljava/util/concurrent/CancellationException;ILjava/lang/Object;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final e(LX5/s0;LD5/d;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1}, LX5/y0;->e(LX5/s0;LD5/d;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final f(LX5/m;Ljava/util/concurrent/Future;)V
    .locals 0

    .line 1
    invoke-static {p0, p1}, LX5/x0;->a(LX5/m;Ljava/util/concurrent/Future;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final g(LD5/g;)V
    .locals 0

    .line 1
    invoke-static {p0}, LX5/y0;->f(LD5/g;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static final h(LX5/s0;)V
    .locals 0

    .line 1
    invoke-static {p0}, LX5/y0;->g(LX5/s0;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method
