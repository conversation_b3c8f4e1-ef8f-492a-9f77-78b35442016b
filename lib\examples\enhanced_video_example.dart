import 'package:flutter/material.dart';
import '../models/video_models.dart';
import '../presentation/screens/player/full_video.dart';

class EnhancedVideoExample extends StatelessWidget {
  const EnhancedVideoExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('مثال على المشغل المحسن'),
        backgroundColor: Colors.black,
      ),
      backgroundColor: Colors.black,
      body: Center(
        child: ElevatedButton(
          onPressed: () => _playEnhancedVideo(context),
          child: const Text('تشغيل فيديو بالميزات المحسنة'),
        ),
      ),
    );
  }

  void _playEnhancedVideo(BuildContext context) {
    // إنشاء فصول للفيديو
    final chapters = [
      Chapter(
        title: "المقدمة",
        time: Duration.zero,
        description: "مقدمة الفيديو",
      ),
      Chapter(
        title: "الجزء الأول",
        time: const Duration(minutes: 5),
        description: "بداية المحتوى الرئيسي",
      ),
      Chapter(
        title: "الجزء الثاني",
        time: const Duration(minutes: 15),
        description: "تطوير الأحداث",
      ),
      Chapter(
        title: "الخاتمة",
        time: const Duration(minutes: 25),
        description: "نهاية الفيديو",
      ),
    ];

    // إنشاء جودات مختلفة
    final qualities = [
      VideoQuality(
        label: "1080p",
        url: "https://example.com/video_1080p.m3u8",
        height: 1080,
      ),
      VideoQuality(
        label: "720p",
        url: "https://example.com/video_720p.m3u8",
        height: 720,
      ),
      VideoQuality(
        label: "480p",
        url: "https://example.com/video_480p.m3u8",
        height: 480,
      ),
    ];

    // إنشاء مسارات صوتية
    final audioTracks = [
      AudioTrack(
        id: 0,
        language: "ar",
        name: "العربية",
      ),
      AudioTrack(
        id: 1,
        language: "en",
        name: "English",
      ),
    ];

    // إنشاء مسارات ترجمة
    final subtitleTracks = [
      SubtitleTrack(
        id: 0,
        language: "ar",
        name: "العربية",
      ),
      SubtitleTrack(
        id: 1,
        language: "en",
        name: "English",
      ),
      SubtitleTrack(
        id: 2,
        language: "fr",
        name: "Français",
      ),
    ];

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => FullVideoScreen(
          link: "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4",
          title: "فيديو تجريبي بالميزات المحسنة",
          videoId: "enhanced_video_001",
          isLive: false,
          chapters: chapters,
          qualities: qualities,
          audioTracks: audioTracks,
          subtitleTracks: subtitleTracks,
        ),
      ),
    );
  }
}

// مثال على كيفية استخدام مدير قائمة التشغيل
class PlaylistExample extends StatefulWidget {
  const PlaylistExample({super.key});

  @override
  State<PlaylistExample> createState() => _PlaylistExampleState();
}

class _PlaylistExampleState extends State<PlaylistExample> {
  final List<PlaylistItem> _samplePlaylist = [
    PlaylistItem(
      id: "1",
      title: "فيديو 1",
      url: "https://example.com/video1.mp4",
      thumbnail: "https://example.com/thumb1.jpg",
      duration: const Duration(minutes: 30),
    ),
    PlaylistItem(
      id: "2",
      title: "فيديو 2",
      url: "https://example.com/video2.mp4",
      thumbnail: "https://example.com/thumb2.jpg",
      duration: const Duration(minutes: 45),
    ),
    PlaylistItem(
      id: "3",
      title: "فيديو 3",
      url: "https://example.com/video3.mp4",
      thumbnail: "https://example.com/thumb3.jpg",
      duration: const Duration(hours: 1, minutes: 20),
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('مثال قائمة التشغيل'),
        backgroundColor: Colors.black,
      ),
      backgroundColor: Colors.black,
      body: ListView.builder(
        itemCount: _samplePlaylist.length,
        itemBuilder: (context, index) {
          final item = _samplePlaylist[index];
          return ListTile(
            leading: item.thumbnail != null
                ? Image.network(
                    item.thumbnail!,
                    width: 80,
                    height: 60,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) =>
                        const Icon(Icons.video_library, color: Colors.white),
                  )
                : const Icon(Icons.video_library, color: Colors.white),
            title: Text(
              item.title,
              style: const TextStyle(color: Colors.white),
            ),
            subtitle: item.duration != null
                ? Text(
                    _formatDuration(item.duration!),
                    style: const TextStyle(color: Colors.grey),
                  )
                : null,
            onTap: () => _playFromPlaylist(context, index),
          );
        },
      ),
    );
  }

  void _playFromPlaylist(BuildContext context, int index) {
    // هنا يمكن تعيين قائمة التشغيل في PlaylistManager
    // وتشغيل الفيديو المحدد
    
    final item = _samplePlaylist[index];
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => FullVideoScreen(
          link: item.url,
          title: item.title,
          videoId: item.id,
          isLive: false,
        ),
      ),
    );
  }

  String _formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    
    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else {
      return '${minutes}m';
    }
  }
}
