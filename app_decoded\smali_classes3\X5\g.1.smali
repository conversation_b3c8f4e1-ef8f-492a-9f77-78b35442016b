.class public abstract LX5/g;
.super Ljava/lang/Object;
.source "SourceFile"


# direct methods
.method public static final a(LX5/J;LD5/g;LX5/L;LM5/o;)LX5/Q;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, LX5/i;->a(LX5/J;LD5/g;LX5/L;LM5/o;)LX5/Q;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static synthetic b(LX5/J;LD5/g;LX5/L;LM5/o;ILjava/lang/Object;)LX5/Q;
    .locals 0

    .line 1
    invoke-static/range {p0 .. p5}, LX5/i;->b(LX5/J;LD5/g;LX5/L;LM5/o;ILjava/lang/Object;)LX5/Q;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final c(LX5/J;LD5/g;LX5/L;LM5/o;)LX5/s0;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, LX5/i;->c(LX5/J;LD5/g;LX5/L;LM5/o;)LX5/s0;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static synthetic d(LX5/J;LD5/g;LX5/L;LM5/o;ILjava/lang/Object;)LX5/s0;
    .locals 0

    .line 1
    invoke-static/range {p0 .. p5}, LX5/i;->d(LX5/J;LD5/g;LX5/L;LM5/o;ILjava/lang/Object;)LX5/s0;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final e(LD5/g;LM5/o;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1}, LX5/h;->a(LD5/g;LM5/o;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static synthetic f(LD5/g;LM5/o;ILjava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2, p3}, LX5/h;->b(LD5/g;LM5/o;ILjava/lang/Object;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static final g(LD5/g;LM5/o;LD5/d;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, LX5/i;->e(LD5/g;LM5/o;LD5/d;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method
