import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import '../../../helpers/helpers.dart';

class AppSettingsScreen extends StatefulWidget {
  const AppSettingsScreen({Key? key}) : super(key: key);

  @override
  State<AppSettingsScreen> createState() => _AppSettingsScreenState();
}

class _AppSettingsScreenState extends State<AppSettingsScreen> {
  String _selectedPlayer = 'player1'; // Default player

  @override
  void initState() {
    super.initState();
    _loadPlayerPreference();
  }

  void _loadPlayerPreference() {
    final storage = GetStorage();
    setState(() {
      _selectedPlayer = storage.read('selected_player') ?? 'player1';
    });
  }

  void _savePlayerPreference(String player) async {
    final storage = GetStorage();
    await storage.write('selected_player', player);
    setState(() {
      _selectedPlayer = player;
    });

    // Show confirmation
    Get.snackbar(
      'تم الحفظ',
      'تم تغيير المشغل بنجاح',
      backgroundColor: kColorPrimary,
      colorText: Colors.white,
      duration: const Duration(seconds: 2),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        title: Text(
          'الإعدادات',
          style: TextStyle(
            color: Colors.white,
            fontSize: 20.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          onPressed: () => Get.back(),
          icon: const Icon(
            FontAwesomeIcons.chevronLeft,
            color: Colors.white,
          ),
        ),
        elevation: 0,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Player Selection Section
            _buildSectionTitle('اختيار المشغل'),
            const SizedBox(height: 16),

            // Player 1 Option
            _buildPlayerOption(
              title: 'مشغل 1',
              subtitle: 'المشغل الأساسي مع الميزات الأساسية',
              value: 'player1',
              icon: FontAwesomeIcons.play,
            ),

            const SizedBox(height: 12),

            // Player 2 Option
            _buildPlayerOption(
              title: 'مشغل 2',
              subtitle: 'المشغل المتقدم مع جميع الميزات',
              value: 'player2',
              icon: FontAwesomeIcons.gear,
            ),

            const SizedBox(height: 32),

            // Other Settings Section
            _buildSectionTitle('إعدادات أخرى'),
            const SizedBox(height: 16),

            // About App
            _buildSettingItem(
              title: 'حول التطبيق',
              subtitle: 'معلومات التطبيق والإصدار',
              icon: FontAwesomeIcons.info,
              onTap: () {
                _showAboutDialog();
              },
            ),

            const SizedBox(height: 12),

            // Clear Cache
            _buildSettingItem(
              title: 'مسح التخزين المؤقت',
              subtitle: 'حذف الملفات المؤقتة لتحسين الأداء',
              icon: FontAwesomeIcons.trash,
              onTap: () {
                _showClearCacheDialog();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: TextStyle(
        color: kColorPrimary,
        fontSize: 18.sp,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  Widget _buildPlayerOption({
    required String title,
    required String subtitle,
    required String value,
    required IconData icon,
  }) {
    final isSelected = _selectedPlayer == value;

    return GestureDetector(
      onTap: () => _savePlayerPreference(value),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected ? kColorPrimary.withOpacity(0.1) : Colors.grey[900],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? kColorPrimary : Colors.grey[700]!,
            width: 2,
          ),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: isSelected ? kColorPrimary : Colors.grey[700],
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                color: Colors.white,
                size: 20,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      color: Colors.grey[400],
                      fontSize: 14.sp,
                    ),
                  ),
                ],
              ),
            ),
            if (isSelected)
              Icon(
                FontAwesomeIcons.check,
                color: kColorPrimary,
                size: 20,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildSettingItem({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.grey[900],
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[700],
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                color: Colors.white,
                size: 20,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      color: Colors.grey[400],
                      fontSize: 14.sp,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              FontAwesomeIcons.chevronRight,
              color: Colors.grey[400],
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  void _showAboutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.grey[900],
        title: const Text(
          'حول التطبيق',
          style: TextStyle(color: Colors.white),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Nova Yemen IPTV',
              style: TextStyle(
                color: kColorPrimary,
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'الإصدار: 1.0.0',
              style: TextStyle(color: Colors.white),
            ),
            const SizedBox(height: 8),
            const Text(
              'تطبيق IPTV متقدم مع مشغلين فيديو احترافيين',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text(
              'موافق',
              style: TextStyle(color: kColorPrimary),
            ),
          ),
        ],
      ),
    );
  }

  void _showClearCacheDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.grey[900],
        title: const Text(
          'مسح التخزين المؤقت',
          style: TextStyle(color: Colors.white),
        ),
        content: const Text(
          'هل تريد مسح جميع الملفات المؤقتة؟ هذا قد يحسن أداء التطبيق.',
          style: TextStyle(color: Colors.grey),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text(
              'إلغاء',
              style: TextStyle(color: Colors.grey),
            ),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              Get.snackbar(
                'تم المسح',
                'تم مسح التخزين المؤقت بنجاح',
                backgroundColor: kColorPrimary,
                colorText: Colors.white,
              );
            },
            child: Text(
              'مسح',
              style: TextStyle(color: kColorPrimary),
            ),
          ),
        ],
      ),
    );
  }
}
