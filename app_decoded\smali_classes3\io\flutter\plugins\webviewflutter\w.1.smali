.class public final synthetic Lio/flutter/plugins/webviewflutter/w;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lio/flutter/plugin/common/BasicMessageChannel$Reply;


# instance fields
.field public final synthetic a:LM5/k;

.field public final synthetic b:Ljava/lang/String;


# direct methods
.method public synthetic constructor <init>(LM5/k;Ljava/lang/String;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lio/flutter/plugins/webviewflutter/w;->a:LM5/k;

    .line 5
    .line 6
    iput-object p2, p0, Lio/flutter/plugins/webviewflutter/w;->b:Ljava/lang/String;

    .line 7
    .line 8
    return-void
.end method


# virtual methods
.method public final reply(Ljava/lang/Object;)V
    .locals 2

    .line 1
    iget-object v0, p0, Lio/flutter/plugins/webviewflutter/w;->a:LM5/k;

    iget-object v1, p0, Lio/flutter/plugins/webviewflutter/w;->b:Ljava/lang/String;

    invoke-static {v0, v1, p1}, Lio/flutter/plugins/webviewflutter/PigeonApiFileChooserParams;->a(LM5/k;Ljava/lang/String;Ljava/lang/Object;)V

    return-void
.end method
