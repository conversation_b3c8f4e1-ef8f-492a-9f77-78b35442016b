.class public final LU5/o;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LU5/f;


# instance fields
.field public final a:LU5/f;

.field public final b:LM5/k;


# direct methods
.method public constructor <init>(LU5/f;LM5/k;)V
    .locals 1

    .line 1
    const-string v0, "sequence"

    .line 2
    .line 3
    invoke-static {p1, v0}, Lkotlin/jvm/internal/r;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    const-string v0, "transformer"

    .line 7
    .line 8
    invoke-static {p2, v0}, Lkotlin/jvm/internal/r;->f(Lja<PERSON>/lang/Object;Ljava/lang/String;)V

    .line 9
    .line 10
    .line 11
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 12
    .line 13
    .line 14
    iput-object p1, p0, LU5/o;->a:LU5/f;

    .line 15
    .line 16
    iput-object p2, p0, LU5/o;->b:LM5/k;

    .line 17
    .line 18
    return-void
.end method

.method public static final synthetic c(LU5/o;)LU5/f;
    .locals 0

    .line 1
    iget-object p0, p0, LU5/o;->a:LU5/f;

    .line 2
    .line 3
    return-object p0
.end method

.method public static final synthetic d(LU5/o;)LM5/k;
    .locals 0

    .line 1
    iget-object p0, p0, LU5/o;->b:LM5/k;

    .line 2
    .line 3
    return-object p0
.end method


# virtual methods
.method public iterator()Ljava/util/Iterator;
    .locals 1

    .line 1
    new-instance v0, LU5/o$a;

    .line 2
    .line 3
    invoke-direct {v0, p0}, LU5/o$a;-><init>(LU5/o;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method
