import 'package:flutter/material.dart';
import 'package:flutter_vlc_player/flutter_vlc_player.dart';
import 'package:volume_controller/volume_controller.dart';
import 'package:screen_brightness/screen_brightness.dart';

class VideoGestureDetector extends StatefulWidget {
  final Widget child;
  final VlcPlayerController controller;
  final Function()? onTap;
  final Function()? onDoubleTap;
  final Function(double)? onVolumeChanged;
  final Function(double)? onBrightnessChanged;
  final Function(Duration)? onSeek;

  const VideoGestureDetector({
    super.key,
    required this.child,
    required this.controller,
    this.onTap,
    this.onDoubleTap,
    this.onVolumeChanged,
    this.onBrightnessChanged,
    this.onSeek,
  });

  @override
  State<VideoGestureDetector> createState() => _VideoGestureDetectorState();
}

class _VideoGestureDetectorState extends State<VideoGestureDetector> {
  bool _isAdjusting = false;
  String _adjustmentType = '';
  double _adjustmentValue = 0.0;
  Offset? _startPosition;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap,
      onDoubleTap: () {
        widget.onDoubleTap?.call();
        _togglePlayPause();
      },
      onPanStart: (details) {
        _startPosition = details.localPosition;
        _isAdjusting = false;
      },
      onPanUpdate: (details) {
        if (_startPosition == null) return;

        final screenWidth = MediaQuery.of(context).size.width;
        final screenHeight = MediaQuery.of(context).size.height;
        final deltaX = details.localPosition.dx - _startPosition!.dx;
        final deltaY = details.localPosition.dy - _startPosition!.dy;

        // تحديد نوع الإيماءة
        if (!_isAdjusting) {
          if (deltaX.abs() > deltaY.abs() && deltaX.abs() > 20) {
            // إيماءة أفقية - تغيير الوقت
            _adjustmentType = 'seek';
            _isAdjusting = true;
          } else if (deltaY.abs() > 20) {
            // إيماءة عمودية
            if (_startPosition!.dx < screenWidth / 2) {
              // الجانب الأيسر - السطوع
              _adjustmentType = 'brightness';
            } else {
              // الجانب الأيمن - الصوت
              _adjustmentType = 'volume';
            }
            _isAdjusting = true;
          }
        }

        if (_isAdjusting) {
          switch (_adjustmentType) {
            case 'seek':
              _handleSeekGesture(deltaX, screenWidth);
              break;
            case 'volume':
              _handleVolumeGesture(-deltaY, screenHeight);
              break;
            case 'brightness':
              _handleBrightnessGesture(-deltaY, screenHeight);
              break;
          }
        }
      },
      onPanEnd: (details) {
        if (_isAdjusting) {
          _applyAdjustment();
        }
        _isAdjusting = false;
        _adjustmentType = '';
        _startPosition = null;
      },
      child: Stack(
        children: [
          widget.child,
          if (_isAdjusting) _buildAdjustmentIndicator(),
        ],
      ),
    );
  }

  void _togglePlayPause() {
    if (widget.controller.value.isPlaying) {
      widget.controller.pause();
    } else {
      widget.controller.play();
    }
  }

  void _handleSeekGesture(double deltaX, double screenWidth) {
    final duration = widget.controller.value.duration;
    if (duration.inSeconds == 0) return;

    final seekPercentage = deltaX / screenWidth;
    final seekSeconds = seekPercentage * duration.inSeconds;
    final currentPosition = widget.controller.value.position.inSeconds;
    final newPosition = (currentPosition + seekSeconds).clamp(0.0, duration.inSeconds.toDouble());
    
    _adjustmentValue = seekSeconds;
    setState(() {});
  }

  void _handleVolumeGesture(double deltaY, double screenHeight) {
    final volumeChange = deltaY / screenHeight;
    VolumeController().getVolume().then((currentVolume) {
      final newVolume = (currentVolume + volumeChange).clamp(0.0, 1.0);
      _adjustmentValue = newVolume;
      setState(() {});
    });
  }

  void _handleBrightnessGesture(double deltaY, double screenHeight) {
    final brightnessChange = deltaY / screenHeight;
    ScreenBrightness().current.then((currentBrightness) {
      final newBrightness = (currentBrightness + brightnessChange).clamp(0.0, 1.0);
      _adjustmentValue = newBrightness;
      setState(() {});
    });
  }

  void _applyAdjustment() {
    switch (_adjustmentType) {
      case 'seek':
        final duration = widget.controller.value.duration;
        final currentPosition = widget.controller.value.position.inSeconds;
        final newPosition = (currentPosition + _adjustmentValue).clamp(0.0, duration.inSeconds.toDouble());
        final seekDuration = Duration(seconds: newPosition.round());
        widget.controller.seekTo(seekDuration);
        widget.onSeek?.call(seekDuration);
        break;
      case 'volume':
        VolumeController().setVolume(_adjustmentValue);
        widget.onVolumeChanged?.call(_adjustmentValue);
        break;
      case 'brightness':
        ScreenBrightness().setScreenBrightness(_adjustmentValue);
        widget.onBrightnessChanged?.call(_adjustmentValue);
        break;
    }
  }

  Widget _buildAdjustmentIndicator() {
    IconData icon;
    String text;
    
    switch (_adjustmentType) {
      case 'seek':
        icon = _adjustmentValue > 0 ? Icons.fast_forward : Icons.fast_rewind;
        text = '${_adjustmentValue > 0 ? '+' : ''}${_adjustmentValue.round()}s';
        break;
      case 'volume':
        icon = _adjustmentValue > 0.5 ? Icons.volume_up : 
               _adjustmentValue > 0.1 ? Icons.volume_down : Icons.volume_off;
        text = '${(_adjustmentValue * 100).round()}%';
        break;
      case 'brightness':
        icon = _adjustmentValue > 0.5 ? Icons.brightness_high : Icons.brightness_low;
        text = '${(_adjustmentValue * 100).round()}%';
        break;
      default:
        return const SizedBox.shrink();
    }

    return Center(
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
        decoration: BoxDecoration(
          color: Colors.black54,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: Colors.white, size: 32),
            const SizedBox(height: 8),
            Text(
              text,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
