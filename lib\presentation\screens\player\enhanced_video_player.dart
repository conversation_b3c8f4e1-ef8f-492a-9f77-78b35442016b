import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_vlc_player/flutter_vlc_player.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:wakelock_plus/wakelock_plus.dart';
import 'package:volume_controller/volume_controller.dart';
import '../../../helpers/helpers.dart';

class EnhancedVideoPlayer extends StatefulWidget {
  final String videoUrl;
  final String title;
  final bool isLive;

  const EnhancedVideoPlayer({
    Key? key,
    required this.videoUrl,
    required this.title,
    this.isLive = false,
  }) : super(key: key);

  @override
  State<EnhancedVideoPlayer> createState() => _EnhancedVideoPlayerState();
}

class _EnhancedVideoPlayerState extends State<EnhancedVideoPlayer> {
  late VlcPlayerController _controller;
  bool _showControls = true;
  bool _isPlaying = false;
  bool _isLoading = true;

  // Progress tracking
  Duration _currentPosition = Duration.zero;
  Duration _totalDuration = Duration.zero;
  double _sliderValue = 0.0;
  bool _isDragging = false;

  // Volume & Brightness
  double _currentVolume = 0.5;
  double _currentBrightness = 0.5;

  // Auto-hide timer
  Timer? _hideTimer;

  @override
  void initState() {
    super.initState();
    _initializePlayer();
    _initializeVolume();
    WakelockPlus.enable();
    _startHideTimer();
  }

  void _initializePlayer() {
    _controller = VlcPlayerController.network(
      widget.videoUrl,
      hwAcc: HwAcc.full,
      autoPlay: true,
      autoInitialize: true,
    );

    _controller.addListener(_playerListener);
  }

  void _initializeVolume() async {
    try {
      _currentVolume = await VolumeController().getVolume();
      setState(() {});
    } catch (e) {
      debugPrint('Error getting volume: $e');
    }
  }

  void _playerListener() {
    if (!mounted) return;

    final value = _controller.value;

    setState(() {
      _isPlaying = value.isPlaying;
      _isLoading = !value.isInitialized;

      if (value.isInitialized && !_isDragging) {
        _currentPosition = value.position;
        _totalDuration = value.duration;

        if (_totalDuration.inMilliseconds > 0) {
          _sliderValue =
              _currentPosition.inMilliseconds / _totalDuration.inMilliseconds;
        }
      }
    });
  }

  void _startHideTimer() {
    _hideTimer?.cancel();
    _hideTimer = Timer(const Duration(seconds: 5), () {
      if (mounted && _showControls) {
        setState(() {
          _showControls = false;
        });
      }
    });
  }

  void _toggleControls() {
    setState(() {
      _showControls = !_showControls;
    });
    if (_showControls) {
      _startHideTimer();
    }
  }

  void _togglePlayPause() {
    if (_isPlaying) {
      _controller.pause();
    } else {
      _controller.play();
    }
    _startHideTimer();
  }

  void _onSliderChanged(double value) {
    setState(() {
      _sliderValue = value;
      _isDragging = true;
    });
  }

  void _onSliderChangeEnd(double value) {
    final position = Duration(
      milliseconds: (value * _totalDuration.inMilliseconds).round(),
    );
    _controller.seekTo(position);
    setState(() {
      _isDragging = false;
    });
    _startHideTimer();
  }

  void _seek(Duration delta) {
    final newPosition = _currentPosition + delta;
    final clampedPosition = Duration(
      milliseconds:
          newPosition.inMilliseconds.clamp(0, _totalDuration.inMilliseconds),
    );
    _controller.seekTo(clampedPosition);
  }

  void _showPlayerSettings() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.black.withOpacity(0.9),
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'إعدادات المشغل',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            ListTile(
              leading: const Icon(FontAwesomeIcons.expand, color: Colors.white),
              title: const Text('ملء الشاشة',
                  style: TextStyle(color: Colors.white)),
              onTap: () {
                Navigator.pop(context);
                // Toggle fullscreen logic here
              },
            ),
            ListTile(
              leading:
                  const Icon(FontAwesomeIcons.volumeHigh, color: Colors.white),
              title: const Text('إعدادات الصوت',
                  style: TextStyle(color: Colors.white)),
              onTap: () {
                Navigator.pop(context);
                _showVolumeSettings();
              },
            ),
            ListTile(
              leading: const Icon(FontAwesomeIcons.gear, color: Colors.white),
              title: const Text('إعدادات متقدمة',
                  style: TextStyle(color: Colors.white)),
              onTap: () {
                Navigator.pop(context);
                _showAdvancedSettings();
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showVolumeSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.grey[900],
        title:
            const Text('إعدادات الصوت', style: TextStyle(color: Colors.white)),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('مستوى الصوت: ${(_currentVolume * 100).round()}%',
                style: const TextStyle(color: Colors.white)),
            Slider(
              value: _currentVolume,
              onChanged: (value) {
                setState(() {
                  _currentVolume = value;
                });
                VolumeController().setVolume(value);
              },
              activeColor: kColorPrimary,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('موافق', style: TextStyle(color: kColorPrimary)),
          ),
        ],
      ),
    );
  }

  void _showAdvancedSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.grey[900],
        title:
            const Text('إعدادات متقدمة', style: TextStyle(color: Colors.white)),
        content: const Text(
          'ستتوفر المزيد من الإعدادات المتقدمة قريباً',
          style: TextStyle(color: Colors.grey),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('موافق', style: TextStyle(color: kColorPrimary)),
          ),
        ],
      ),
    );
  }

  void _showSpeedMenu() {
    final speeds = [0.5, 0.75, 1.0, 1.25, 1.5, 2.0];
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.black.withOpacity(0.9),
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'سرعة التشغيل',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ...speeds
                .map((speed) => ListTile(
                      title: Text(
                        '${speed}x',
                        style: const TextStyle(color: Colors.white),
                      ),
                      trailing: speed == 1.0
                          ? Icon(FontAwesomeIcons.check, color: kColorPrimary)
                          : null,
                      onTap: () {
                        Navigator.pop(context);
                        _controller.setPlaybackSpeed(speed);
                        Get.snackbar(
                          'تم التغيير',
                          'تم تغيير سرعة التشغيل إلى ${speed}x',
                          backgroundColor: kColorPrimary,
                          colorText: Colors.white,
                          duration: const Duration(seconds: 1),
                        );
                      },
                    ))
                .toList(),
          ],
        ),
      ),
    );
  }

  void _toggleFullscreen() {
    // For now, just show a message
    Get.snackbar(
      'ملء الشاشة',
      'ميزة ملء الشاشة ستتوفر قريباً',
      backgroundColor: kColorPrimary,
      colorText: Colors.white,
      duration: const Duration(seconds: 2),
    );
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '${twoDigits(hours)}:${twoDigits(minutes)}:${twoDigits(seconds)}';
    } else {
      return '${twoDigits(minutes)}:${twoDigits(seconds)}';
    }
  }

  @override
  void dispose() {
    _hideTimer?.cancel();
    _controller.removeListener(_playerListener);
    _controller.dispose();
    WakelockPlus.disable();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // Video Player - Full Screen
          SizedBox(
            width: double.infinity,
            height: double.infinity,
            child: VlcPlayer(
              controller: _controller,
              aspectRatio: 16 / 9,
              placeholder: Container(
                color: Colors.black,
                child: const Center(
                  child: CircularProgressIndicator(
                    color: kColorPrimary,
                  ),
                ),
              ),
            ),
          ),

          // Gesture Detector Overlay - منفصل عن المشغل
          Positioned.fill(
            child: GestureDetector(
              onTap: _toggleControls,
              behavior: HitTestBehavior.translucent,
              child: Container(
                color: Colors.transparent,
              ),
            ),
          ),

          // Loading Indicator
          if (_isLoading)
            const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(
                    color: kColorPrimary,
                  ),
                  SizedBox(height: 16),
                  Text(
                    'جاري تحميل الفيديو...',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ),

          // Status Indicator - لإظهار حالة الاستجابة
          if (!_showControls && !_isLoading)
            Positioned(
              top: 50,
              right: 20,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.7),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  'اضغط للتحكم',
                  style: const TextStyle(
                    color: Colors.white70,
                    fontSize: 12,
                  ),
                ),
              ),
            ),

          // Controls Overlay
          if (_showControls)
            IgnorePointer(
              ignoring: false,
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.black.withOpacity(0.8),
                      Colors.transparent,
                      Colors.transparent,
                      Colors.black.withOpacity(0.8),
                    ],
                  ),
                ),
                child: SafeArea(
                  child: Column(
                    children: [
                      // Top Controls
                      _buildTopControls(),

                      const Spacer(),

                      // Center Controls
                      _buildCenterControls(),

                      const Spacer(),

                      // Bottom Controls
                      if (!widget.isLive) _buildBottomControls(),
                    ],
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildTopControls() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        children: [
          // Back Button
          Container(
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.6),
              shape: BoxShape.circle,
            ),
            child: IconButton(
              onPressed: () => Get.back(),
              icon: const Icon(
                FontAwesomeIcons.chevronLeft,
                color: Colors.white,
                size: 20,
              ),
            ),
          ),
          const SizedBox(width: 16),

          // Title
          Expanded(
            child: Text(
              widget.title,
              style: TextStyle(
                color: Colors.white,
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),

          // Settings Button
          Container(
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.6),
              shape: BoxShape.circle,
            ),
            child: IconButton(
              onPressed: _showPlayerSettings,
              icon: const Icon(
                FontAwesomeIcons.gear,
                color: Colors.white,
                size: 18,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCenterControls() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // Rewind Button
        _buildControlButton(
          icon: FontAwesomeIcons.backward,
          onPressed: () => _seek(const Duration(seconds: -10)),
        ),

        // Play/Pause Button
        Container(
          width: 70,
          height: 70,
          decoration: BoxDecoration(
            color: kColorPrimary.withOpacity(0.9),
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: kColorPrimary.withOpacity(0.3),
                blurRadius: 10,
                spreadRadius: 2,
              ),
            ],
          ),
          child: IconButton(
            onPressed: _togglePlayPause,
            icon: Icon(
              _isPlaying ? FontAwesomeIcons.pause : FontAwesomeIcons.play,
              color: Colors.white,
              size: 28,
            ),
          ),
        ),

        // Forward Button
        _buildControlButton(
          icon: FontAwesomeIcons.forward,
          onPressed: () => _seek(const Duration(seconds: 10)),
        ),
      ],
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback onPressed,
  }) {
    return Container(
      width: 50,
      height: 50,
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.7),
        shape: BoxShape.circle,
        border: Border.all(
          color: Colors.white.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: IconButton(
        onPressed: onPressed,
        icon: Icon(
          icon,
          color: Colors.white,
          size: 20,
        ),
      ),
    );
  }

  Widget _buildBottomControls() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          // Additional Controls Row
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              // Speed Control
              GestureDetector(
                onTap: _showSpeedMenu,
                child: _buildSpeedButton(),
              ),

              // Volume Control
              GestureDetector(
                onTap: _showVolumeSettings,
                child: _buildVolumeButton(),
              ),

              // Fullscreen Toggle
              _buildControlButton(
                icon: FontAwesomeIcons.expand,
                onPressed: _toggleFullscreen,
              ),

              // More Options
              _buildControlButton(
                icon: FontAwesomeIcons.ellipsisVertical,
                onPressed: _showPlayerSettings,
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Progress Slider
          Row(
            children: [
              Text(
                _formatDuration(_currentPosition),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: SliderTheme(
                    data: SliderTheme.of(context).copyWith(
                      activeTrackColor: kColorPrimary,
                      inactiveTrackColor: Colors.white.withOpacity(0.3),
                      thumbColor: kColorPrimary,
                      thumbShape:
                          const RoundSliderThumbShape(enabledThumbRadius: 8),
                      overlayShape:
                          const RoundSliderOverlayShape(overlayRadius: 16),
                    ),
                    child: Slider(
                      value: _sliderValue,
                      onChanged: _onSliderChanged,
                      onChangeEnd: _onSliderChangeEnd,
                    ),
                  ),
                ),
              ),
              Text(
                _formatDuration(_totalDuration),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSpeedButton() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.7),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: Colors.white.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: const Text(
        '1.0x',
        style: TextStyle(
          color: Colors.white,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildVolumeButton() {
    return Container(
      width: 50,
      height: 50,
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.7),
        shape: BoxShape.circle,
        border: Border.all(
          color: Colors.white.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: IconButton(
        onPressed: () {
          // Toggle volume
        },
        icon: Icon(
          _currentVolume < 0.1
              ? FontAwesomeIcons.volumeXmark
              : _currentVolume < 0.7
                  ? FontAwesomeIcons.volumeLow
                  : FontAwesomeIcons.volumeHigh,
          color: Colors.white,
          size: 18,
        ),
      ),
    );
  }
}
