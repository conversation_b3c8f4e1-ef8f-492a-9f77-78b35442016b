import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_vlc_player/flutter_vlc_player.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:wakelock_plus/wakelock_plus.dart';
import 'package:volume_controller/volume_controller.dart';
import '../../../helpers/helpers.dart';

class EnhancedVideoPlayer extends StatefulWidget {
  final String videoUrl;
  final String title;
  final bool isLive;

  const EnhancedVideoPlayer({
    super.key,
    required this.videoUrl,
    required this.title,
    this.isLive = false,
  });

  @override
  State<EnhancedVideoPlayer> createState() => _EnhancedVideoPlayerState();
}

class _EnhancedVideoPlayerState extends State<EnhancedVideoPlayer> {
  late VlcPlayerController _controller;
  bool _showControls = true;
  bool _isPlaying = false;
  bool _isLoading = true;

  // Progress tracking
  Duration _currentPosition = Duration.zero;
  Duration _totalDuration = Duration.zero;
  double _sliderValue = 0.0;
  bool _isDragging = false;

  // Buffer Bar - غير محدود مع تحديث مستمر
  double _bufferProgress = 0.0;
  Duration _bufferedDuration = Duration.zero;
  Timer? _bufferUpdateTimer;

  // Volume & Brightness
  double _currentVolume = 0.5;
  final double _currentBrightness = 0.5;

  // Auto-hide timer
  Timer? _hideTimer;

  // Enhanced Features
  final bool _isDownloading = false;
  final double _downloadProgress = 0.0;
  final bool _showVideoInfo = false;
  Map<String, dynamic> _videoInfo = {};
  Map<String, dynamic> _performanceStats = {};

  // Resume functionality
  final GetStorage _storage = GetStorage();
  String get _resumeKey => 'resume_${widget.videoUrl.hashCode}';

  // Network & Quality
  bool _isSlowNetwork = false;
  final String _currentQuality = 'Auto';
  final List<String> _availableQualities = [
    'Auto',
    '1080p',
    '720p',
    '480p',
    '360p'
  ];

  // Live streaming
  bool get _isLiveStream => widget.isLive;
  final bool _isLiveBuffering = false;

  // Dark mode support
  bool get _isDarkMode => Theme.of(context).brightness == Brightness.dark;

  @override
  void initState() {
    super.initState();
    _initializePlayer();
    _initializeVolume();
    _loadResumePosition();
    _initializeVideoInfo();
    _startUnlimitedBufferTracking(); // Buffer غير محدود
    WakelockPlus.enable();
    _startHideTimer();

    // دخول تلقائي للشاشة الكاملة عند بدء التشغيل
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _enterAutoFullscreen();
    });
  }

  void _initializePlayer() {
    _controller = VlcPlayerController.network(
      widget.videoUrl,
      hwAcc: HwAcc.full,
      autoPlay: true,
      autoInitialize: true,
    );

    _controller.addListener(_playerListener);
  }

  void _initializeVolume() async {
    try {
      _currentVolume = await VolumeController().getVolume();
      setState(() {});
    } catch (e) {
      debugPrint('Error getting volume: $e');
    }
  }

  void _loadResumePosition() async {
    if (!widget.isLive) {
      final savedPosition = _storage.read(_resumeKey);
      if (savedPosition != null && savedPosition > 0) {
        Timer(const Duration(seconds: 2), () {
          _controller.seekTo(Duration(milliseconds: savedPosition));
        });
      }
    }
  }

  void _saveResumePosition() {
    if (!widget.isLive && _currentPosition.inMilliseconds > 0) {
      _storage.write(_resumeKey, _currentPosition.inMilliseconds);
    }
  }

  void _initializeVideoInfo() {
    _videoInfo = {
      'url': widget.videoUrl,
      'title': widget.title,
      'isLive': widget.isLive,
      'format': _getVideoFormat(widget.videoUrl),
      'quality': _currentQuality,
    };

    _performanceStats = {
      'buffering': 0,
      'bitrate': 'Unknown',
      'fps': 'Unknown',
      'resolution': 'Unknown',
    };
  }

  String _getVideoFormat(String url) {
    if (url.contains('.m3u8')) return 'HLS';
    if (url.contains('.mpd')) return 'DASH';
    if (url.contains('rtsp://')) return 'RTSP';
    if (url.contains('.mp4')) return 'MP4';
    return 'Unknown';
  }

  // Buffer غير محدود مع تحديث مستمر كل 200ms
  void _startUnlimitedBufferTracking() {
    _bufferUpdateTimer =
        Timer.periodic(const Duration(milliseconds: 200), (timer) {
      if (mounted && _controller.value.isInitialized) {
        _updateUnlimitedBufferProgress();
      }
    });
  }

  void _updateUnlimitedBufferProgress() {
    if (!mounted || !_controller.value.isInitialized) return;

    // Buffer غير محدود يستمر في النمو
    if (_totalDuration.inMilliseconds > 0) {
      if (widget.isLive) {
        // للبث المباشر: buffer مستمر ومتطور
        const baseBufferMs = 12000; // 12 ثانية أساسية
        final dynamicBuffer = (DateTime.now().millisecondsSinceEpoch % 15000) /
            1000; // 0-15 ثانية ديناميكية
        final totalBufferMs = baseBufferMs + (dynamicBuffer * 1000).round();
        final simulatedBufferMs =
            _currentPosition.inMilliseconds + totalBufferMs;
        final clampedBufferMs =
            simulatedBufferMs.clamp(0, _totalDuration.inMilliseconds);

        if (mounted) {
          setState(() {
            _bufferedDuration = Duration(milliseconds: clampedBufferMs);
            _bufferProgress = clampedBufferMs / _totalDuration.inMilliseconds;
          });
        }
      } else {
        // للفيديو المسجل: buffer أكثر قوة وسرعة
        const baseBufferMs = 60000; // 60 ثانية أساسية
        final dynamicBuffer = (DateTime.now().millisecondsSinceEpoch % 30000) /
            1000; // 0-30 ثانية ديناميكية
        final totalBufferMs = baseBufferMs + (dynamicBuffer * 1000).round();
        final simulatedBufferMs =
            _currentPosition.inMilliseconds + totalBufferMs;
        final clampedBufferMs =
            simulatedBufferMs.clamp(0, _totalDuration.inMilliseconds);

        if (mounted) {
          setState(() {
            _bufferedDuration = Duration(milliseconds: clampedBufferMs);
            _bufferProgress = clampedBufferMs / _totalDuration.inMilliseconds;
          });
        }
      }
    }
  }

  void _playerListener() {
    if (!mounted) return;

    final value = _controller.value;

    setState(() {
      _isPlaying = value.isPlaying;
      _isLoading = !value.isInitialized;

      if (value.isInitialized && !_isDragging) {
        _currentPosition = value.position;
        _totalDuration = value.duration;

        if (_totalDuration.inMilliseconds > 0) {
          _sliderValue =
              _currentPosition.inMilliseconds / _totalDuration.inMilliseconds;
        }

        // Save resume position every 10 seconds
        if (_currentPosition.inSeconds % 10 == 0) {
          _saveResumePosition();
        }

        // Update performance stats
        _updatePerformanceStats(value);
      }
    });
  }

  void _updatePerformanceStats(VlcPlayerValue value) {
    _performanceStats['buffering'] = value.isBuffering ? 1 : 0;
    _performanceStats['position'] = _formatDuration(_currentPosition);
    _performanceStats['duration'] = _formatDuration(_totalDuration);
    _performanceStats['buffered'] = _formatDuration(_bufferedDuration);
    _performanceStats['buffer_ahead'] =
        '${(_bufferedDuration.inSeconds - _currentPosition.inSeconds).clamp(0, double.infinity)} ثانية';

    // Detect slow network
    if (value.isBuffering && _isPlaying) {
      _isSlowNetwork = true;
      _performanceStats['network'] = 'Slow';
    } else {
      _isSlowNetwork = false;
      _performanceStats['network'] = 'Good';
    }
  }

  void _startHideTimer() {
    _hideTimer?.cancel();
    _hideTimer = Timer(const Duration(seconds: 5), () {
      if (mounted && _showControls) {
        setState(() {
          _showControls = false;
        });
      }
    });
  }

  void _toggleControls() {
    setState(() {
      _showControls = !_showControls;
    });
    if (_showControls) {
      _startHideTimer();
    }
  }

  void _togglePlayPause() {
    if (_isPlaying) {
      _controller.pause();
    } else {
      _controller.play();
    }
    _startHideTimer();
  }

  void _onSliderChanged(double value) {
    setState(() {
      _sliderValue = value;
      _isDragging = true;
    });
  }

  void _onSliderChangeEnd(double value) {
    final position = Duration(
      milliseconds: (value * _totalDuration.inMilliseconds).round(),
    );
    _controller.seekTo(position);
    setState(() {
      _isDragging = false;
    });
    _startHideTimer();
  }

  void _seek(Duration delta) {
    final newPosition = _currentPosition + delta;
    final clampedPosition = Duration(
      milliseconds:
          newPosition.inMilliseconds.clamp(0, _totalDuration.inMilliseconds),
    );
    _controller.seekTo(clampedPosition);
  }

  // دخول تلقائي للشاشة الكاملة
  void _enterAutoFullscreen() {
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);
  }

  // الخروج من الشاشة الكاملة - البقاء في الوضع الأفقي
  void _exitFullscreen() {
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);
    Navigator.pop(context);
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '${twoDigits(hours)}:${twoDigits(minutes)}:${twoDigits(seconds)}';
    }
    return '${twoDigits(minutes)}:${twoDigits(seconds)}';
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: const TextStyle(color: Colors.grey)),
          Text(value, style: const TextStyle(color: Colors.white)),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _hideTimer?.cancel();
    _bufferUpdateTimer?.cancel();
    _saveResumePosition();
    _controller.removeListener(_playerListener);
    _controller.dispose();
    WakelockPlus.disable();

    // البقاء في الوضع الأفقي عند الخروج
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: GestureDetector(
        onTap: _toggleControls,
        child: Stack(
          children: [
            // Video Player - ملء الشاشة بالكامل
            SizedBox.expand(
              child: VlcPlayer(
                controller: _controller,
                aspectRatio: 16 / 9,
                placeholder: Container(
                  color: Colors.black,
                  child: const Center(
                    child: CircularProgressIndicator(color: kColorPrimary),
                  ),
                ),
              ),
            ),

            // Loading Indicator
            if (_isLoading)
              const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(color: kColorPrimary),
                    SizedBox(height: 16),
                    Text(
                      'جاري تحميل الفيديو...',
                      style: TextStyle(color: Colors.white, fontSize: 16),
                    ),
                  ],
                ),
              ),

            // Status Indicators
            if (!_showControls && !_isLoading) ...[
              // Live indicator
              if (widget.isLive)
                Positioned(
                  top: 50,
                  left: 20,
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.red.withOpacity(0.3),
                          blurRadius: 8,
                          spreadRadius: 2,
                        ),
                      ],
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          width: 8,
                          height: 8,
                          decoration: const BoxDecoration(
                            color: Colors.white,
                            shape: BoxShape.circle,
                          ),
                        ),
                        const SizedBox(width: 6),
                        const Text(
                          'مباشر',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

              // Network status indicator
              if (_isSlowNetwork)
                Positioned(
                  top: 50,
                  right: 20,
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.orange.withOpacity(0.9),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(FontAwesomeIcons.wifi,
                            color: Colors.white, size: 12),
                        SizedBox(width: 4),
                        Text(
                          'شبكة بطيئة',
                          style: TextStyle(color: Colors.white, fontSize: 10),
                        ),
                      ],
                    ),
                  ),
                ),

              // إزالة نص "اضغط للتحكم" - الآن التحكم مباشر عند اللمس
            ],

            // Controls Overlay
            if (_showControls)
              Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.black.withOpacity(0.8),
                      Colors.transparent,
                      Colors.transparent,
                      Colors.black.withOpacity(0.8),
                    ],
                  ),
                ),
                child: SafeArea(
                  child: Column(
                    children: [
                      // Top Controls
                      _buildTopControls(),

                      const Spacer(),

                      // Center Controls
                      _buildCenterControls(),

                      const Spacer(),

                      // Bottom Controls
                      if (!widget.isLive) _buildBottomControls(),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopControls() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // Back Button
          Container(
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.6),
              shape: BoxShape.circle,
            ),
            child: IconButton(
              onPressed: _exitFullscreen,
              icon: const Icon(
                FontAwesomeIcons.chevronLeft,
                color: Colors.white,
                size: 20,
              ),
            ),
          ),

          const SizedBox(width: 16),

          // Title
          Expanded(
            child: Text(
              widget.title,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),

          // Live indicator
          if (widget.isLive)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.red,
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Text(
                'مباشر',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildCenterControls() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Rewind Button
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.7),
            shape: BoxShape.circle,
          ),
          child: IconButton(
            icon: const Icon(FontAwesomeIcons.backward, color: Colors.white),
            onPressed: () => _seek(const Duration(seconds: -10)),
          ),
        ),

        const SizedBox(width: 40),

        // Play/Pause Button
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: kColorPrimary.withOpacity(0.9),
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: kColorPrimary.withOpacity(0.3),
                blurRadius: 10,
                spreadRadius: 2,
              ),
            ],
          ),
          child: IconButton(
            icon: Icon(
              _isPlaying ? FontAwesomeIcons.pause : FontAwesomeIcons.play,
              color: Colors.white,
              size: 32,
            ),
            onPressed: _togglePlayPause,
          ),
        ),

        const SizedBox(width: 40),

        // Forward Button
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.7),
            shape: BoxShape.circle,
          ),
          child: IconButton(
            icon: const Icon(FontAwesomeIcons.forward, color: Colors.white),
            onPressed: () => _seek(const Duration(seconds: 10)),
          ),
        ),
      ],
    );
  }

  Widget _buildBottomControls() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Progress Bar with Buffer - شريط التقدم المزدوج
          Row(
            children: [
              Text(
                _formatDuration(_currentPosition),
                style: const TextStyle(color: Colors.white, fontSize: 12),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildDualProgressBar(),
              ),
              const SizedBox(width: 8),
              Text(
                _formatDuration(_totalDuration),
                style: const TextStyle(color: Colors.white, fontSize: 12),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // شريط التقدم المزدوج مع Buffer غير محدود
  Widget _buildDualProgressBar() {
    return SizedBox(
      height: 40,
      child: LayoutBuilder(
        builder: (context, constraints) {
          final trackWidth = constraints.maxWidth;
          final bufferWidth = trackWidth * _bufferProgress;
          final progressWidth = trackWidth * _sliderValue;

          return Stack(
            alignment: Alignment.center,
            children: [
              // Background track
              Container(
                height: 4,
                width: trackWidth,
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // Buffer bar (يستمر في النمو)
              Align(
                alignment: Alignment.centerLeft,
                child: Container(
                  height: 4,
                  width: bufferWidth,
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.6), // Buffer color
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),

              // Progress bar (يتغير لونه حسب حالة التشغيل)
              Align(
                alignment: Alignment.centerLeft,
                child: Container(
                  height: 4,
                  width: progressWidth,
                  decoration: BoxDecoration(
                    color: _isPlaying
                        ? kColorPrimary
                        : Colors.orange, // Progress color
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),

              // Slider for interaction
              SliderTheme(
                data: SliderTheme.of(context).copyWith(
                  activeTrackColor: Colors.transparent,
                  inactiveTrackColor: Colors.transparent,
                  thumbColor: _isPlaying ? kColorPrimary : Colors.orange,
                  thumbShape:
                      const RoundSliderThumbShape(enabledThumbRadius: 8),
                  overlayShape:
                      const RoundSliderOverlayShape(overlayRadius: 16),
                ),
                child: Slider(
                  value: _sliderValue,
                  onChanged: _onSliderChanged,
                  onChangeEnd: _onSliderChangeEnd,
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
