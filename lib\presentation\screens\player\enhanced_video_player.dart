import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_vlc_player/flutter_vlc_player.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:wakelock_plus/wakelock_plus.dart';
import 'package:volume_controller/volume_controller.dart';
import '../../../helpers/helpers.dart';

class EnhancedVideoPlayer extends StatefulWidget {
  final String videoUrl;
  final String title;
  final bool isLive;

  const EnhancedVideoPlayer({
    Key? key,
    required this.videoUrl,
    required this.title,
    this.isLive = false,
  }) : super(key: key);

  @override
  State<EnhancedVideoPlayer> createState() => _EnhancedVideoPlayerState();
}

class _EnhancedVideoPlayerState extends State<EnhancedVideoPlayer> {
  late VlcPlayerController _controller;
  bool _showControls = true;
  bool _isPlaying = false;
  bool _isLoading = true;

  // Progress tracking
  Duration _currentPosition = Duration.zero;
  Duration _totalDuration = Duration.zero;
  double _sliderValue = 0.0;
  bool _isDragging = false;

  // Buffer Bar
  double _bufferProgress = 0.0;
  Duration _bufferedDuration = Duration.zero;
  Timer? _bufferUpdateTimer;

  // Volume & Brightness
  double _currentVolume = 0.5;
  double _currentBrightness = 0.5;

  // Auto-hide timer
  Timer? _hideTimer;

  // Enhanced Features
  bool _isDownloading = false;
  double _downloadProgress = 0.0;
  bool _showVideoInfo = false;
  Map<String, dynamic> _videoInfo = {};
  Map<String, dynamic> _performanceStats = {};

  // Resume functionality
  final GetStorage _storage = GetStorage();
  String get _resumeKey => 'resume_${widget.videoUrl.hashCode}';

  // Network & Quality
  bool _isSlowNetwork = false;
  String _currentQuality = 'Auto';
  List<String> _availableQualities = ['Auto', '1080p', '720p', '480p', '360p'];

  // Live streaming
  bool get _isLiveStream => widget.isLive;
  bool _isLiveBuffering = false;

  // Dark mode support
  bool get _isDarkMode => Theme.of(context).brightness == Brightness.dark;

  // Full screen support
  bool _isFullScreen = false;

  @override
  void initState() {
    super.initState();
    _initializePlayer();
    _initializeVolume();
    _loadResumePosition();
    _initializeVideoInfo();
    _startBufferTracking();
    WakelockPlus.enable();
    _startHideTimer();
  }

  void _initializePlayer() {
    _controller = VlcPlayerController.network(
      widget.videoUrl,
      hwAcc: HwAcc.full,
      autoPlay: true,
      autoInitialize: true,
    );

    _controller.addListener(_playerListener);
  }

  void _initializeVolume() async {
    try {
      _currentVolume = await VolumeController().getVolume();
      setState(() {});
    } catch (e) {
      debugPrint('Error getting volume: $e');
    }
  }

  void _loadResumePosition() async {
    if (!widget.isLive) {
      final savedPosition = _storage.read(_resumeKey);
      if (savedPosition != null && savedPosition > 0) {
        // Resume from saved position after player is initialized
        Timer(const Duration(seconds: 2), () {
          _controller.seekTo(Duration(milliseconds: savedPosition));
        });
      }
    }
  }

  void _saveResumePosition() {
    if (!widget.isLive && _currentPosition.inMilliseconds > 0) {
      _storage.write(_resumeKey, _currentPosition.inMilliseconds);
    }
  }

  void _initializeVideoInfo() {
    _videoInfo = {
      'url': widget.videoUrl,
      'title': widget.title,
      'isLive': widget.isLive,
      'format': _getVideoFormat(widget.videoUrl),
      'quality': _currentQuality,
    };

    _performanceStats = {
      'buffering': 0,
      'bitrate': 'Unknown',
      'fps': 'Unknown',
      'resolution': 'Unknown',
    };
  }

  String _getVideoFormat(String url) {
    if (url.contains('.m3u8')) return 'HLS';
    if (url.contains('.mpd')) return 'DASH';
    if (url.contains('rtsp://')) return 'RTSP';
    if (url.contains('.mp4')) return 'MP4';
    return 'Unknown';
  }

  void _startBufferTracking() {
    // Start continuous buffer tracking timer - غير محدود
    _bufferUpdateTimer =
        Timer.periodic(const Duration(milliseconds: 200), (timer) {
      if (mounted && _controller.value.isInitialized) {
        _updateBufferProgress();
      }
    });
  }

  void _updateBufferProgress() {
    if (!mounted || !_controller.value.isInitialized) return;

    // Continuous buffer progress simulation - غير محدود
    if (_totalDuration.inMilliseconds > 0) {
      if (widget.isLive) {
        // For live streams, buffer continuously grows
        const baseBufferMs = 8000; // 8 seconds base buffer
        final dynamicBuffer = (DateTime.now().millisecondsSinceEpoch % 10000) /
            1000; // 0-10 seconds dynamic
        final totalBufferMs = baseBufferMs + (dynamicBuffer * 1000).round();
        final simulatedBufferMs =
            _currentPosition.inMilliseconds + totalBufferMs;
        final clampedBufferMs =
            simulatedBufferMs.clamp(0, _totalDuration.inMilliseconds);

        if (mounted) {
          setState(() {
            _bufferedDuration = Duration(milliseconds: clampedBufferMs);
            _bufferProgress = clampedBufferMs / _totalDuration.inMilliseconds;
          });
        }
      } else {
        // For VOD content, buffer grows continuously and more aggressively
        const baseBufferMs = 45000; // 45 seconds base buffer
        final dynamicBuffer = (DateTime.now().millisecondsSinceEpoch % 20000) /
            1000; // 0-20 seconds dynamic
        final totalBufferMs = baseBufferMs + (dynamicBuffer * 1000).round();
        final simulatedBufferMs =
            _currentPosition.inMilliseconds + totalBufferMs;
        final clampedBufferMs =
            simulatedBufferMs.clamp(0, _totalDuration.inMilliseconds);

        if (mounted) {
          setState(() {
            _bufferedDuration = Duration(milliseconds: clampedBufferMs);
            _bufferProgress = clampedBufferMs / _totalDuration.inMilliseconds;
          });
        }
      }
    }
  }

  void _playerListener() {
    if (!mounted) return;

    final value = _controller.value;

    setState(() {
      _isPlaying = value.isPlaying;
      _isLoading = !value.isInitialized;

      if (value.isInitialized && !_isDragging) {
        _currentPosition = value.position;
        _totalDuration = value.duration;

        if (_totalDuration.inMilliseconds > 0) {
          _sliderValue =
              _currentPosition.inMilliseconds / _totalDuration.inMilliseconds;
        }

        // Save resume position every 10 seconds
        if (_currentPosition.inSeconds % 10 == 0) {
          _saveResumePosition();
        }

        // Update performance stats
        _updatePerformanceStats(value);
      }
    });
  }

  void _updatePerformanceStats(VlcPlayerValue value) {
    _performanceStats['buffering'] = value.isBuffering ? 1 : 0;
    _performanceStats['position'] = _formatDuration(_currentPosition);
    _performanceStats['duration'] = _formatDuration(_totalDuration);
    _performanceStats['buffered'] = _formatDuration(_bufferedDuration);
    _performanceStats['buffer_ahead'] =
        '${(_bufferedDuration.inSeconds - _currentPosition.inSeconds).clamp(0, double.infinity)} ثانية';

    // Detect slow network
    if (value.isBuffering && _isPlaying) {
      _isSlowNetwork = true;
      _performanceStats['network'] = 'Slow';
    } else {
      _isSlowNetwork = false;
      _performanceStats['network'] = 'Good';
    }
  }

  void _startHideTimer() {
    _hideTimer?.cancel();
    _hideTimer = Timer(const Duration(seconds: 5), () {
      if (mounted && _showControls) {
        setState(() {
          _showControls = false;
        });
      }
    });
  }

  void _toggleControls() {
    setState(() {
      _showControls = !_showControls;
    });
    if (_showControls) {
      _startHideTimer();
    }
  }

  void _togglePlayPause() {
    if (_isPlaying) {
      _controller.pause();
    } else {
      _controller.play();
    }
    _startHideTimer();
  }

  void _onSliderChanged(double value) {
    setState(() {
      _sliderValue = value;
      _isDragging = true;
    });
  }

  void _onSliderChangeEnd(double value) {
    final position = Duration(
      milliseconds: (value * _totalDuration.inMilliseconds).round(),
    );
    _controller.seekTo(position);
    setState(() {
      _isDragging = false;
    });
    _startHideTimer();
  }

  void _seek(Duration delta) {
    final newPosition = _currentPosition + delta;
    final clampedPosition = Duration(
      milliseconds:
          newPosition.inMilliseconds.clamp(0, _totalDuration.inMilliseconds),
    );
    _controller.seekTo(clampedPosition);
  }

  void _showPlayerSettings() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.black.withOpacity(0.9),
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'إعدادات المشغل',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            ListTile(
              leading: const Icon(FontAwesomeIcons.expand, color: Colors.white),
              title: const Text('ملء الشاشة',
                  style: TextStyle(color: Colors.white)),
              onTap: () {
                Navigator.pop(context);
                _toggleFullscreen();
              },
            ),
            ListTile(
              leading:
                  const Icon(FontAwesomeIcons.volumeHigh, color: Colors.white),
              title: const Text('إعدادات الصوت',
                  style: TextStyle(color: Colors.white)),
              onTap: () {
                Navigator.pop(context);
                _showVolumeSettings();
              },
            ),
            ListTile(
              leading:
                  const Icon(FontAwesomeIcons.download, color: Colors.white),
              title: const Text('تحميل الفيديو',
                  style: TextStyle(color: Colors.white)),
              onTap: () {
                Navigator.pop(context);
                _showDownloadDialog();
              },
            ),
            ListTile(
              leading:
                  const Icon(FontAwesomeIcons.chartLine, color: Colors.white),
              title: const Text('معلومات الفيديو',
                  style: TextStyle(color: Colors.white)),
              onTap: () {
                Navigator.pop(context);
                _showVideoInfoDialog();
              },
            ),
            ListTile(
              leading: const Icon(FontAwesomeIcons.cog, color: Colors.white),
              title: const Text('الجودة والشبكة',
                  style: TextStyle(color: Colors.white)),
              onTap: () {
                Navigator.pop(context);
                _showQualitySettings();
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showVolumeSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.grey[900],
        title:
            const Text('إعدادات الصوت', style: TextStyle(color: Colors.white)),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('مستوى الصوت: ${(_currentVolume * 100).round()}%',
                style: const TextStyle(color: Colors.white)),
            Slider(
              value: _currentVolume,
              onChanged: (value) {
                setState(() {
                  _currentVolume = value;
                });
                VolumeController().setVolume(value);
              },
              activeColor: kColorPrimary,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('موافق', style: TextStyle(color: kColorPrimary)),
          ),
        ],
      ),
    );
  }

  void _showAdvancedSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.grey[900],
        title:
            const Text('إعدادات متقدمة', style: TextStyle(color: Colors.white)),
        content: const Text(
          'ستتوفر المزيد من الإعدادات المتقدمة قريباً',
          style: TextStyle(color: Colors.grey),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('موافق', style: TextStyle(color: kColorPrimary)),
          ),
        ],
      ),
    );
  }

  void _showSpeedMenu() {
    final speeds = [0.5, 0.75, 1.0, 1.25, 1.5, 2.0];
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.black.withOpacity(0.9),
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'سرعة التشغيل',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ...speeds
                .map((speed) => ListTile(
                      title: Text(
                        '${speed}x',
                        style: const TextStyle(color: Colors.white),
                      ),
                      trailing: speed == 1.0
                          ? Icon(FontAwesomeIcons.check, color: kColorPrimary)
                          : null,
                      onTap: () {
                        Navigator.pop(context);
                        _controller.setPlaybackSpeed(speed);
                        Get.snackbar(
                          'تم التغيير',
                          'تم تغيير سرعة التشغيل إلى ${speed}x',
                          backgroundColor: kColorPrimary,
                          colorText: Colors.white,
                          duration: const Duration(seconds: 1),
                        );
                      },
                    ))
                .toList(),
          ],
        ),
      ),
    );
  }

  void _toggleFullscreen() {
    setState(() {
      _isFullScreen = !_isFullScreen;
    });

    if (_isFullScreen) {
      // Enter fullscreen mode
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]);
    } else {
      // Exit fullscreen mode
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]);
    }
  }

  void _showDownloadDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.grey[900],
        title:
            const Text('تحميل الفيديو', style: TextStyle(color: Colors.white)),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (_isDownloading) ...[
              const CircularProgressIndicator(color: kColorPrimary),
              const SizedBox(height: 16),
              Text(
                'جاري التحميل... ${(_downloadProgress * 100).toInt()}%',
                style: const TextStyle(color: Colors.white),
              ),
              const SizedBox(height: 8),
              LinearProgressIndicator(
                value: _downloadProgress,
                backgroundColor: Colors.grey[700],
                valueColor: const AlwaysStoppedAnimation<Color>(kColorPrimary),
              ),
            ] else ...[
              const Text(
                'هل تريد تحميل هذا الفيديو للمشاهدة بدون إنترنت؟',
                style: TextStyle(color: Colors.white),
              ),
              const SizedBox(height: 16),
              Text(
                'الحجم المتوقع: غير محدد',
                style: TextStyle(color: Colors.grey[400]),
              ),
            ],
          ],
        ),
        actions: [
          if (!_isDownloading) ...[
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء', style: TextStyle(color: Colors.grey)),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                _startDownload();
              },
              child:
                  const Text('تحميل', style: TextStyle(color: kColorPrimary)),
            ),
          ] else ...[
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                _cancelDownload();
              },
              child: const Text('إيقاف', style: TextStyle(color: Colors.red)),
            ),
          ],
        ],
      ),
    );
  }

  void _startDownload() {
    setState(() {
      _isDownloading = true;
      _downloadProgress = 0.0;
    });

    // Simulate download progress
    Timer.periodic(const Duration(milliseconds: 100), (timer) {
      setState(() {
        _downloadProgress += 0.01;
      });

      if (_downloadProgress >= 1.0) {
        timer.cancel();
        setState(() {
          _isDownloading = false;
          _downloadProgress = 0.0;
        });
        Get.snackbar(
          'تم التحميل',
          'تم تحميل الفيديو بنجاح',
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      }
    });
  }

  void _cancelDownload() {
    setState(() {
      _isDownloading = false;
      _downloadProgress = 0.0;
    });
    Get.snackbar(
      'تم الإلغاء',
      'تم إلغاء التحميل',
      backgroundColor: Colors.orange,
      colorText: Colors.white,
    );
  }

  void _showVideoInfoDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.grey[900],
        title: const Text('معلومات الفيديو',
            style: TextStyle(color: Colors.white)),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildInfoRow('العنوان', _videoInfo['title'] ?? 'غير محدد'),
              _buildInfoRow('التنسيق', _videoInfo['format'] ?? 'غير محدد'),
              _buildInfoRow('الجودة', _currentQuality),
              _buildInfoRow(
                  'نوع البث', widget.isLive ? 'بث مباشر' : 'فيديو مسجل'),
              const Divider(color: Colors.grey),
              const Text(
                'إحصائيات الأداء:',
                style:
                    TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              _buildInfoRow(
                  'الموضع الحالي', _performanceStats['position'] ?? '00:00'),
              _buildInfoRow(
                  'المدة الإجمالية', _performanceStats['duration'] ?? '00:00'),
              _buildInfoRow(
                  'حالة الشبكة', _performanceStats['network'] ?? 'غير محدد'),
              _buildInfoRow('التخزين المؤقت',
                  _performanceStats['buffering'] == 1 ? 'نشط' : 'غير نشط'),
              if (_isSlowNetwork)
                Container(
                  margin: const EdgeInsets.only(top: 8),
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.orange.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Row(
                    children: [
                      Icon(FontAwesomeIcons.exclamationTriangle,
                          color: Colors.orange, size: 16),
                      SizedBox(width: 8),
                      Text('شبكة بطيئة مكتشفة',
                          style: TextStyle(color: Colors.orange)),
                    ],
                  ),
                ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق', style: TextStyle(color: kColorPrimary)),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: TextStyle(color: Colors.grey[400]),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  void _showQualitySettings() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.black.withOpacity(0.9),
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'إعدادات الجودة والشبكة',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(FontAwesomeIcons.video, color: Colors.white),
              title: const Text('الجودة الحالية',
                  style: TextStyle(color: Colors.white)),
              subtitle: Text(_currentQuality,
                  style: TextStyle(color: Colors.grey[400])),
              onTap: () {
                Navigator.pop(context);
                _showQualitySelector();
              },
            ),
            ListTile(
              leading: Icon(
                _isSlowNetwork
                    ? FontAwesomeIcons.wifi
                    : FontAwesomeIcons.signal,
                color: _isSlowNetwork ? Colors.orange : Colors.green,
              ),
              title: const Text('حالة الشبكة',
                  style: TextStyle(color: Colors.white)),
              subtitle: Text(
                _isSlowNetwork ? 'شبكة بطيئة' : 'شبكة جيدة',
                style: TextStyle(
                  color: _isSlowNetwork ? Colors.orange : Colors.green,
                ),
              ),
            ),
            if (widget.isLive)
              ListTile(
                leading: const Icon(FontAwesomeIcons.rss, color: Colors.red),
                title: const Text('بث مباشر',
                    style: TextStyle(color: Colors.white)),
                subtitle:
                    const Text('متصل', style: TextStyle(color: Colors.green)),
              ),
          ],
        ),
      ),
    );
  }

  void _showQualitySelector() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.black.withOpacity(0.9),
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'اختيار الجودة',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ..._availableQualities.map((quality) => ListTile(
                  title: Text(quality,
                      style: const TextStyle(color: Colors.white)),
                  trailing: quality == _currentQuality
                      ? const Icon(FontAwesomeIcons.check, color: kColorPrimary)
                      : null,
                  onTap: () {
                    setState(() {
                      _currentQuality = quality;
                    });
                    Navigator.pop(context);
                    Get.snackbar(
                      'تم التغيير',
                      'تم تغيير الجودة إلى $quality',
                      backgroundColor: kColorPrimary,
                      colorText: Colors.white,
                      duration: const Duration(seconds: 2),
                    );
                  },
                )),
          ],
        ),
      ),
    );
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '${twoDigits(hours)}:${twoDigits(minutes)}:${twoDigits(seconds)}';
    } else {
      return '${twoDigits(minutes)}:${twoDigits(seconds)}';
    }
  }

  @override
  void dispose() {
    _hideTimer?.cancel();
    _bufferUpdateTimer?.cancel();
    _saveResumePosition(); // Save position before disposing
    _controller.removeListener(_playerListener);
    _controller.dispose();
    WakelockPlus.disable();

    // Restore system UI and orientation
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_isFullScreen) {
      // Full screen mode
      return Scaffold(
        backgroundColor: Colors.black,
        body: GestureDetector(
          onTap: _toggleControls,
          child: Stack(
            children: [
              // Video Player - Full Screen
              SizedBox.expand(
                child: VlcPlayer(
                  controller: _controller,
                  aspectRatio: 16 / 9,
                  placeholder: Container(
                    color: Colors.black,
                    child: const Center(
                      child: CircularProgressIndicator(
                        color: kColorPrimary,
                      ),
                    ),
                  ),
                ),
              ),

              // Full screen controls
              if (_showControls) _buildFullScreenControls(),
            ],
          ),
        ),
      );
    }

    // Normal mode
    return Scaffold(
      backgroundColor: _isDarkMode ? Colors.black : Colors.grey[900],
      body: SafeArea(
        child: Column(
          children: [
            // Video Player Container
            Container(
              width: double.infinity,
              height: 25.h,
              color: Colors.black,
              child: Stack(
                children: [
                  // VLC Player
                  VlcPlayer(
                    controller: _controller,
                    aspectRatio: 16 / 9,
                    placeholder: const Center(
                      child: CircularProgressIndicator(color: kColorPrimary),
                    ),
                  ),

                  // Gesture Detector Overlay - منفصل عن المشغل
                  Positioned.fill(
                    child: GestureDetector(
                      onTap: _toggleControls,
                      behavior: HitTestBehavior.translucent,
                      child: Container(
                        color: Colors.transparent,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Loading Indicator
            if (_isLoading)
              const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(
                      color: kColorPrimary,
                    ),
                    SizedBox(height: 16),
                    Text(
                      'جاري تحميل الفيديو...',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),

            // Status Indicators
            if (!_showControls && !_isLoading) ...[
              // Live indicator
              if (widget.isLive)
                Positioned(
                  top: 50,
                  left: 20,
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          width: 8,
                          height: 8,
                          decoration: const BoxDecoration(
                            color: Colors.white,
                            shape: BoxShape.circle,
                          ),
                        ),
                        const SizedBox(width: 6),
                        const Text(
                          'بث مباشر',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

              // Network status indicator
              if (_isSlowNetwork)
                Positioned(
                  top: 50,
                  right: 20,
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.orange,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(FontAwesomeIcons.wifi,
                            color: Colors.white, size: 12),
                        SizedBox(width: 4),
                        Text(
                          'شبكة بطيئة',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

              // Touch indicator
              if (!widget.isLive && !_isSlowNetwork)
                Positioned(
                  top: 50,
                  right: 20,
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.7),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Text(
                      'اضغط للتحكم',
                      style: TextStyle(
                        color: Colors.white70,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ),
            ],

            // Controls Overlay
            if (_showControls)
              IgnorePointer(
                ignoring: false,
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.black.withOpacity(0.8),
                        Colors.transparent,
                        Colors.transparent,
                        Colors.black.withOpacity(0.8),
                      ],
                    ),
                  ),
                  child: SafeArea(
                    child: Column(
                      children: [
                        // Top Controls
                        _buildTopControls(),

                        const Spacer(),

                        // Center Controls
                        _buildCenterControls(),

                        const Spacer(),

                        // Bottom Controls
                        if (!widget.isLive) _buildBottomControls(),
                      ],
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopControls() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        children: [
          // Back Button
          Container(
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.6),
              shape: BoxShape.circle,
            ),
            child: IconButton(
              onPressed: () => Get.back(),
              icon: const Icon(
                FontAwesomeIcons.chevronLeft,
                color: Colors.white,
                size: 20,
              ),
            ),
          ),
          const SizedBox(width: 16),

          // Title
          Expanded(
            child: Text(
              widget.title,
              style: TextStyle(
                color: Colors.white,
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),

          // Settings Button
          Container(
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.6),
              shape: BoxShape.circle,
            ),
            child: IconButton(
              onPressed: _showPlayerSettings,
              icon: const Icon(
                FontAwesomeIcons.gear,
                color: Colors.white,
                size: 18,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCenterControls() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // Rewind Button
        _buildControlButton(
          icon: FontAwesomeIcons.backward,
          onPressed: () => _seek(const Duration(seconds: -10)),
        ),

        // Play/Pause Button
        Container(
          width: 70,
          height: 70,
          decoration: BoxDecoration(
            color: kColorPrimary.withOpacity(0.9),
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: kColorPrimary.withOpacity(0.3),
                blurRadius: 10,
                spreadRadius: 2,
              ),
            ],
          ),
          child: IconButton(
            onPressed: _togglePlayPause,
            icon: Icon(
              _isPlaying ? FontAwesomeIcons.pause : FontAwesomeIcons.play,
              color: Colors.white,
              size: 28,
            ),
          ),
        ),

        // Forward Button
        _buildControlButton(
          icon: FontAwesomeIcons.forward,
          onPressed: () => _seek(const Duration(seconds: 10)),
        ),
      ],
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback onPressed,
  }) {
    return Container(
      width: 50,
      height: 50,
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.7),
        shape: BoxShape.circle,
        border: Border.all(
          color: Colors.white.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: IconButton(
        onPressed: onPressed,
        icon: Icon(
          icon,
          color: Colors.white,
          size: 20,
        ),
      ),
    );
  }

  Widget _buildBottomControls() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          // Additional Controls Row
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              // Speed Control
              GestureDetector(
                onTap: _showSpeedMenu,
                child: _buildSpeedButton(),
              ),

              // Volume Control
              GestureDetector(
                onTap: _showVolumeSettings,
                child: _buildVolumeButton(),
              ),

              // Fullscreen Toggle
              _buildControlButton(
                icon: FontAwesomeIcons.expand,
                onPressed: _toggleFullscreen,
              ),

              // More Options
              _buildControlButton(
                icon: FontAwesomeIcons.ellipsisVertical,
                onPressed: _showPlayerSettings,
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Progress Slider
          Row(
            children: [
              Text(
                _formatDuration(_currentPosition),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: _buildProgressBarWithBuffer(),
                ),
              ),
              Text(
                _formatDuration(_totalDuration),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSpeedButton() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.7),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: Colors.white.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: const Text(
        '1.0x',
        style: TextStyle(
          color: Colors.white,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildVolumeButton() {
    return Container(
      width: 50,
      height: 50,
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.7),
        shape: BoxShape.circle,
        border: Border.all(
          color: Colors.white.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: IconButton(
        onPressed: () {
          // Toggle volume
        },
        icon: Icon(
          _currentVolume < 0.1
              ? FontAwesomeIcons.volumeXmark
              : _currentVolume < 0.7
                  ? FontAwesomeIcons.volumeLow
                  : FontAwesomeIcons.volumeHigh,
          color: Colors.white,
          size: 18,
        ),
      ),
    );
  }

  Widget _buildProgressBarWithBuffer() {
    return SizedBox(
      height: 40,
      child: LayoutBuilder(
        builder: (context, constraints) {
          final trackWidth = constraints.maxWidth;
          final bufferWidth = trackWidth * _bufferProgress;
          final progressWidth = trackWidth * _sliderValue;

          return Stack(
            alignment: Alignment.center,
            children: [
              // Background track
              Container(
                height: 4,
                width: trackWidth,
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // Buffer bar (shows buffered content) - رمادي فاتح
              Positioned(
                left: 0,
                child: Container(
                  height: 4,
                  width: bufferWidth,
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.6), // Buffer color
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),

              // Progress bar (shows current position) - لون أساسي
              Positioned(
                left: 0,
                child: Container(
                  height: 4,
                  width: progressWidth,
                  decoration: BoxDecoration(
                    color: _isPlaying
                        ? kColorPrimary
                        : Colors.orange, // Progress color
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),

              // Interactive slider (invisible track with visible thumb)
              SliderTheme(
                data: SliderTheme.of(context).copyWith(
                  activeTrackColor: Colors.transparent,
                  inactiveTrackColor: Colors.transparent,
                  thumbColor: _isPlaying ? kColorPrimary : Colors.orange,
                  thumbShape:
                      const RoundSliderThumbShape(enabledThumbRadius: 8),
                  overlayShape:
                      const RoundSliderOverlayShape(overlayRadius: 16),
                ),
                child: Slider(
                  value: _sliderValue,
                  onChanged: _onSliderChanged,
                  onChangeEnd: _onSliderChangeEnd,
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  // Full screen controls method
  Widget _buildFullScreenControls() {
    return GestureDetector(
      onTap: _toggleControls,
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.black.withValues(alpha: 0.7),
              Colors.transparent,
              Colors.transparent,
              Colors.black.withValues(alpha: 0.7),
            ],
          ),
        ),
        child: Stack(
          children: [
            // Top Controls
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: SafeArea(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      // Back Button
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.black.withValues(alpha: 0.6),
                          shape: BoxShape.circle,
                        ),
                        child: IconButton(
                          icon: const Icon(FontAwesomeIcons.chevronLeft,
                              color: Colors.white),
                          onPressed: () {
                            setState(() {
                              _isFullScreen = false;
                            });
                            SystemChrome.setEnabledSystemUIMode(
                                SystemUiMode.edgeToEdge);
                            SystemChrome.setPreferredOrientations([
                              DeviceOrientation.portraitUp,
                              DeviceOrientation.portraitDown,
                              DeviceOrientation.landscapeLeft,
                              DeviceOrientation.landscapeRight,
                            ]);
                          },
                        ),
                      ),
                      const SizedBox(width: 16),

                      // Title
                      Expanded(
                        child: Text(
                          widget.title,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),

                      // Live indicator
                      if (widget.isLive)
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.red,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(FontAwesomeIcons.circle,
                                  color: Colors.white, size: 8),
                              SizedBox(width: 4),
                              Text('مباشر',
                                  style: TextStyle(
                                      color: Colors.white, fontSize: 12)),
                            ],
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ),

            // Center Controls
            Center(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Rewind Button
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.6),
                      shape: BoxShape.circle,
                      border: Border.all(
                          color: Colors.white.withValues(alpha: 0.3), width: 1),
                    ),
                    child: IconButton(
                      icon: const Icon(FontAwesomeIcons.backward,
                          color: Colors.white),
                      onPressed: () => _seek(const Duration(seconds: -10)),
                    ),
                  ),

                  const SizedBox(width: 20),

                  // Play/Pause Button
                  Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      color: kColorPrimary.withValues(alpha: 0.9),
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: kColorPrimary.withValues(alpha: 0.3),
                          blurRadius: 15,
                          spreadRadius: 3,
                        ),
                      ],
                    ),
                    child: IconButton(
                      icon: Icon(
                        _isPlaying
                            ? FontAwesomeIcons.pause
                            : FontAwesomeIcons.play,
                        color: Colors.white,
                        size: 32,
                      ),
                      onPressed: _togglePlayPause,
                    ),
                  ),

                  const SizedBox(width: 20),

                  // Forward Button
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.6),
                      shape: BoxShape.circle,
                      border: Border.all(
                          color: Colors.white.withValues(alpha: 0.3), width: 1),
                    ),
                    child: IconButton(
                      icon: const Icon(FontAwesomeIcons.forward,
                          color: Colors.white),
                      onPressed: () => _seek(const Duration(seconds: 10)),
                    ),
                  ),
                ],
              ),
            ),

            // Bottom Controls
            if (!widget.isLive)
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: SafeArea(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      children: [
                        Text(
                          _formatDuration(_currentPosition),
                          style: const TextStyle(
                              color: Colors.white, fontSize: 12),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Stack(
                            children: [
                              // Background track
                              Container(
                                height: 4,
                                decoration: BoxDecoration(
                                  color: Colors.white.withValues(alpha: 0.3),
                                  borderRadius: BorderRadius.circular(2),
                                ),
                              ),

                              // Buffer bar
                              FractionallySizedBox(
                                widthFactor: _bufferProgress.clamp(0.0, 1.0),
                                child: Container(
                                  height: 4,
                                  decoration: BoxDecoration(
                                    color: Colors.white.withValues(alpha: 0.6),
                                    borderRadius: BorderRadius.circular(2),
                                  ),
                                ),
                              ),

                              // Progress slider
                              SliderTheme(
                                data: SliderTheme.of(context).copyWith(
                                  activeTrackColor: kColorPrimary,
                                  inactiveTrackColor: Colors.transparent,
                                  thumbColor: kColorPrimary,
                                  overlayColor:
                                      kColorPrimary.withValues(alpha: 0.2),
                                  thumbShape: const RoundSliderThumbShape(
                                      enabledThumbRadius: 8),
                                  trackHeight: 4,
                                ),
                                child: Slider(
                                  value: _sliderValue.clamp(0.0, 1.0),
                                  onChanged: _onSliderChanged,
                                  onChangeEnd: _onSliderChangeEnd,
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          _formatDuration(_totalDuration),
                          style: const TextStyle(
                              color: Colors.white, fontSize: 12),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}

// Full Screen Video Player Widget
class _FullScreenVideoPlayer extends StatefulWidget {
  final VlcPlayerController controller;
  final String videoTitle;
  final Duration currentPosition;
  final Duration totalDuration;
  final bool isPlaying;
  final double bufferProgress;
  final bool isLive;

  const _FullScreenVideoPlayer({
    required this.controller,
    required this.videoTitle,
    required this.currentPosition,
    required this.totalDuration,
    required this.isPlaying,
    required this.bufferProgress,
    required this.isLive,
  });

  @override
  State<_FullScreenVideoPlayer> createState() => _FullScreenVideoPlayerState();
}

class _FullScreenVideoPlayerState extends State<_FullScreenVideoPlayer> {
  bool _showControls = true;
  Timer? _hideTimer;
  Duration _currentPosition = Duration.zero;
  Duration _totalDuration = Duration.zero;
  double _sliderValue = 0.0;
  bool _isDragging = false;
  bool _isPlaying = false;

  @override
  void initState() {
    super.initState();

    // Set landscape orientation and hide system UI
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);

    // Initialize values
    _currentPosition = widget.currentPosition;
    _totalDuration = widget.totalDuration;
    _isPlaying = widget.isPlaying;

    if (_totalDuration.inMilliseconds > 0) {
      _sliderValue =
          _currentPosition.inMilliseconds / _totalDuration.inMilliseconds;
    }

    // Add listener to controller
    widget.controller.addListener(_playerListener);

    // Start hide timer
    _startHideTimer();
  }

  @override
  void dispose() {
    _hideTimer?.cancel();
    widget.controller.removeListener(_playerListener);

    // Restore system UI and orientation
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);

    super.dispose();
  }

  void _playerListener() {
    if (!mounted) return;

    final value = widget.controller.value;

    setState(() {
      _isPlaying = value.isPlaying;

      if (value.isInitialized && !_isDragging) {
        _currentPosition = value.position;
        _totalDuration = value.duration;

        if (_totalDuration.inMilliseconds > 0) {
          _sliderValue =
              _currentPosition.inMilliseconds / _totalDuration.inMilliseconds;
        }
      }
    });
  }

  void _startHideTimer() {
    _hideTimer?.cancel();
    _hideTimer = Timer(const Duration(seconds: 5), () {
      if (mounted && _showControls) {
        setState(() {
          _showControls = false;
        });
      }
    });
  }

  void _toggleControls() {
    setState(() {
      _showControls = !_showControls;
    });
    if (_showControls) {
      _startHideTimer();
    }
  }

  void _togglePlayPause() {
    if (_isPlaying) {
      widget.controller.pause();
    } else {
      widget.controller.play();
    }
    _startHideTimer();
  }

  void _onSliderChanged(double value) {
    setState(() {
      _sliderValue = value;
      _isDragging = true;
    });
  }

  void _onSliderChangeEnd(double value) {
    final position = Duration(
      milliseconds: (value * _totalDuration.inMilliseconds).round(),
    );
    widget.controller.seekTo(position);
    setState(() {
      _isDragging = false;
    });
    _startHideTimer();
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = twoDigits(duration.inHours);
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));

    if (duration.inHours > 0) {
      return '$hours:$minutes:$seconds';
    }
    return '$minutes:$seconds';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: GestureDetector(
        onTap: _toggleControls,
        child: Stack(
          children: [
            // Video Player
            Center(
              child: AspectRatio(
                aspectRatio: 16 / 9,
                child: VlcPlayer(
                  controller: widget.controller,
                  aspectRatio: 16 / 9,
                  placeholder: const Center(
                    child: CircularProgressIndicator(color: kColorPrimary),
                  ),
                ),
              ),
            ),

            // Controls Overlay
            if (_showControls)
              Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.black.withValues(alpha: 0.8),
                      Colors.transparent,
                      Colors.transparent,
                      Colors.black.withValues(alpha: 0.8),
                    ],
                  ),
                ),
              ),

            // Top Controls
            if (_showControls)
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                child: SafeArea(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      children: [
                        // Back Button
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.black.withValues(alpha: 0.6),
                            shape: BoxShape.circle,
                          ),
                          child: IconButton(
                            icon: const Icon(FontAwesomeIcons.chevronLeft,
                                color: Colors.white),
                            onPressed: () {
                              Navigator.pop(context, _currentPosition);
                            },
                          ),
                        ),
                        const SizedBox(width: 16),

                        // Title
                        Expanded(
                          child: Text(
                            widget.videoTitle,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),

                        // Live indicator
                        if (widget.isLive)
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.red,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: const Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(FontAwesomeIcons.circle,
                                    color: Colors.white, size: 8),
                                SizedBox(width: 4),
                                Text('مباشر',
                                    style: TextStyle(
                                        color: Colors.white, fontSize: 12)),
                              ],
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              ),

            // Center Play/Pause Button
            if (_showControls)
              Center(
                child: Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: kColorPrimary.withValues(alpha: 0.9),
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: kColorPrimary.withValues(alpha: 0.3),
                        blurRadius: 15,
                        spreadRadius: 3,
                      ),
                    ],
                  ),
                  child: IconButton(
                    icon: Icon(
                      _isPlaying
                          ? FontAwesomeIcons.pause
                          : FontAwesomeIcons.play,
                      color: Colors.white,
                      size: 32,
                    ),
                    onPressed: _togglePlayPause,
                  ),
                ),
              ),

            // Bottom Controls
            if (_showControls)
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: SafeArea(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Progress Bar
                        Row(
                          children: [
                            Text(
                              _formatDuration(_currentPosition),
                              style: const TextStyle(
                                  color: Colors.white, fontSize: 12),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: SliderTheme(
                                data: SliderTheme.of(context).copyWith(
                                  activeTrackColor: kColorPrimary,
                                  inactiveTrackColor:
                                      Colors.white.withValues(alpha: 0.3),
                                  thumbColor: kColorPrimary,
                                  overlayColor:
                                      kColorPrimary.withValues(alpha: 0.2),
                                  thumbShape: const RoundSliderThumbShape(
                                      enabledThumbRadius: 8),
                                  trackHeight: 4,
                                ),
                                child: Slider(
                                  value: _sliderValue.clamp(0.0, 1.0),
                                  onChanged: _onSliderChanged,
                                  onChangeEnd: _onSliderChangeEnd,
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              _formatDuration(_totalDuration),
                              style: const TextStyle(
                                  color: Colors.white, fontSize: 12),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
