.class public final LX5/r;
.super LX5/u0;
.source "SourceFile"


# instance fields
.field public final e:LX5/n;


# direct methods
.method public constructor <init>(LX5/n;)V
    .locals 0

    .line 1
    invoke-direct {p0}, LX5/u0;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LX5/r;->e:LX5/n;

    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public bridge synthetic invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Ljava/lang/Throwable;

    .line 2
    .line 3
    invoke-virtual {p0, p1}, LX5/r;->v(Ljava/lang/Throwable;)V

    .line 4
    .line 5
    .line 6
    sget-object p1, Ly5/I;->a:Ly5/I;

    .line 7
    .line 8
    return-object p1
.end method

.method public v(Ljava/lang/Throwable;)V
    .locals 1

    .line 1
    iget-object p1, p0, LX5/r;->e:LX5/n;

    .line 2
    .line 3
    invoke-virtual {p0}, LX5/z0;->w()LX5/A0;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-virtual {p1, v0}, LX5/n;->u(LX5/s0;)Ljava/lang/Throwable;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-virtual {p1, v0}, LX5/n;->I(Ljava/lang/Throwable;)V

    .line 12
    .line 13
    .line 14
    return-void
.end method
