.class public interface abstract LX5/m;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LD5/d;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LX5/m$a;
    }
.end annotation


# virtual methods
.method public abstract d(LX5/G;Ljava/lang/Object;)V
.end method

.method public abstract e(Ljava/lang/Object;LM5/k;)V
.end method

.method public abstract f(Ljava/lang/Object;Ljava/lang/Object;LM5/k;)Ljava/lang/Object;
.end method

.method public abstract m(LM5/k;)V
.end method

.method public abstract r(Ljava/lang/Throwable;)Z
.end method

.method public abstract y(Ljava/lang/Object;)V
.end method
