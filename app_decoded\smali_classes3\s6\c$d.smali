.class public final Ls6/c$d;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lv6/b;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Ls6/c;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x11
    name = "d"
.end annotation


# instance fields
.field public final a:Lv6/d$b;

.field public final b:LG6/X;

.field public final c:LG6/X;

.field public d:Z

.field public final synthetic e:Ls6/c;


# direct methods
.method public constructor <init>(Ls6/c;Lv6/d$b;)V
    .locals 1

    .line 1
    const-string v0, "this$0"

    .line 2
    .line 3
    invoke-static {p1, v0}, Lkotlin/jvm/internal/r;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    const-string v0, "editor"

    .line 7
    .line 8
    invoke-static {p2, v0}, L<PERSON>lin/jvm/internal/r;->f(<PERSON><PERSON><PERSON>/lang/Object;Ljava/lang/String;)V

    .line 9
    .line 10
    .line 11
    iput-object p1, p0, Ls6/c$d;->e:Ls6/c;

    .line 12
    .line 13
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 14
    .line 15
    .line 16
    iput-object p2, p0, Ls6/c$d;->a:Lv6/d$b;

    .line 17
    .line 18
    const/4 v0, 0x1

    .line 19
    invoke-virtual {p2, v0}, Lv6/d$b;->f(I)LG6/X;

    .line 20
    .line 21
    .line 22
    move-result-object p2

    .line 23
    iput-object p2, p0, Ls6/c$d;->b:LG6/X;

    .line 24
    .line 25
    new-instance v0, Ls6/c$d$a;

    .line 26
    .line 27
    invoke-direct {v0, p1, p0, p2}, Ls6/c$d$a;-><init>(Ls6/c;Ls6/c$d;LG6/X;)V

    .line 28
    .line 29
    .line 30
    iput-object v0, p0, Ls6/c$d;->c:LG6/X;

    .line 31
    .line 32
    return-void
.end method

.method public static final synthetic c(Ls6/c$d;)Lv6/d$b;
    .locals 0

    .line 1
    iget-object p0, p0, Ls6/c$d;->a:Lv6/d$b;

    .line 2
    .line 3
    return-object p0
.end method


# virtual methods
.method public a()V
    .locals 3

    .line 1
    iget-object v0, p0, Ls6/c$d;->e:Ls6/c;

    .line 2
    .line 3
    monitor-enter v0

    .line 4
    :try_start_0
    invoke-virtual {p0}, Ls6/c$d;->d()Z

    .line 5
    .line 6
    .line 7
    move-result v1
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 8
    if-eqz v1, :cond_0

    .line 9
    .line 10
    monitor-exit v0

    .line 11
    return-void

    .line 12
    :cond_0
    const/4 v1, 0x1

    .line 13
    :try_start_1
    invoke-virtual {p0, v1}, Ls6/c$d;->e(Z)V

    .line 14
    .line 15
    .line 16
    invoke-virtual {v0}, Ls6/c;->c()I

    .line 17
    .line 18
    .line 19
    move-result v2

    .line 20
    add-int/2addr v2, v1

    .line 21
    invoke-virtual {v0, v2}, Ls6/c;->l(I)V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 22
    .line 23
    .line 24
    monitor-exit v0

    .line 25
    iget-object v0, p0, Ls6/c$d;->b:LG6/X;

    .line 26
    .line 27
    invoke-static {v0}, Lt6/d;->m(Ljava/io/Closeable;)V

    .line 28
    .line 29
    .line 30
    :try_start_2
    iget-object v0, p0, Ls6/c$d;->a:Lv6/d$b;

    .line 31
    .line 32
    invoke-virtual {v0}, Lv6/d$b;->a()V
    :try_end_2
    .catch Ljava/io/IOException; {:try_start_2 .. :try_end_2} :catch_0

    .line 33
    .line 34
    .line 35
    :catch_0
    return-void

    .line 36
    :catchall_0
    move-exception v1

    .line 37
    monitor-exit v0

    .line 38
    throw v1
.end method

.method public b()LG6/X;
    .locals 1

    .line 1
    iget-object v0, p0, Ls6/c$d;->c:LG6/X;

    .line 2
    .line 3
    return-object v0
.end method

.method public final d()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Ls6/c$d;->d:Z

    .line 2
    .line 3
    return v0
.end method

.method public final e(Z)V
    .locals 0

    .line 1
    iput-boolean p1, p0, Ls6/c$d;->d:Z

    .line 2
    .line 3
    return-void
.end method
