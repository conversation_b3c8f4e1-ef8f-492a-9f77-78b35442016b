.class public Ls6/x;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/lang/Cloneable;
.implements Ls6/e$a;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Ls6/x$a;,
        Ls6/x$b;
    }
.end annotation


# static fields
.field public static final E:Ls6/x$b;

.field public static final F:Ljava/util/List;

.field public static final G:Ljava/util/List;


# instance fields
.field public final A:I

.field public final B:I

.field public final C:J

.field public final D:Lx6/h;

.field public final a:Ls6/p;

.field public final b:Ls6/k;

.field public final c:Ljava/util/List;

.field public final d:Ljava/util/List;

.field public final e:Ls6/r$c;

.field public final f:Z

.field public final g:Ls6/b;

.field public final h:Z

.field public final i:Z

.field public final j:Ls6/n;

.field public final k:Ls6/c;

.field public final l:Ls6/q;

.field public final m:Ljava/net/Proxy;

.field public final n:Ljava/net/ProxySelector;

.field public final o:Ls6/b;

.field public final p:Ljavax/net/SocketFactory;

.field public final q:Ljavax/net/ssl/SSLSocketFactory;

.field public final r:Ljavax/net/ssl/X509TrustManager;

.field public final s:Ljava/util/List;

.field public final t:Ljava/util/List;

.field public final u:Ljavax/net/ssl/HostnameVerifier;

.field public final v:Ls6/g;

.field public final w:LF6/c;

.field public final x:I

.field public final y:I

.field public final z:I


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 1
    new-instance v0, Ls6/x$b;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, v1}, Ls6/x$b;-><init>(Lkotlin/jvm/internal/j;)V

    .line 5
    .line 6
    .line 7
    sput-object v0, Ls6/x;->E:Ls6/x$b;

    .line 8
    .line 9
    sget-object v0, Ls6/y;->f:Ls6/y;

    .line 10
    .line 11
    sget-object v1, Ls6/y;->d:Ls6/y;

    .line 12
    .line 13
    filled-new-array {v0, v1}, [Ls6/y;

    .line 14
    .line 15
    .line 16
    move-result-object v0

    .line 17
    invoke-static {v0}, Lt6/d;->w([Ljava/lang/Object;)Ljava/util/List;

    .line 18
    .line 19
    .line 20
    move-result-object v0

    .line 21
    sput-object v0, Ls6/x;->F:Ljava/util/List;

    .line 22
    .line 23
    sget-object v0, Ls6/l;->i:Ls6/l;

    .line 24
    .line 25
    sget-object v1, Ls6/l;->k:Ls6/l;

    .line 26
    .line 27
    filled-new-array {v0, v1}, [Ls6/l;

    .line 28
    .line 29
    .line 30
    move-result-object v0

    .line 31
    invoke-static {v0}, Lt6/d;->w([Ljava/lang/Object;)Ljava/util/List;

    .line 32
    .line 33
    .line 34
    move-result-object v0

    .line 35
    sput-object v0, Ls6/x;->G:Ljava/util/List;

    .line 36
    .line 37
    return-void
.end method

.method public constructor <init>(Ls6/x$a;)V
    .locals 3

    .line 1
    const-string v0, "builder"

    .line 2
    .line 3
    invoke-static {p1, v0}, Lkotlin/jvm/internal/r;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 7
    .line 8
    .line 9
    invoke-virtual {p1}, Ls6/x$a;->p()Ls6/p;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    iput-object v0, p0, Ls6/x;->a:Ls6/p;

    .line 14
    .line 15
    invoke-virtual {p1}, Ls6/x$a;->m()Ls6/k;

    .line 16
    .line 17
    .line 18
    move-result-object v0

    .line 19
    iput-object v0, p0, Ls6/x;->b:Ls6/k;

    .line 20
    .line 21
    invoke-virtual {p1}, Ls6/x$a;->v()Ljava/util/List;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    invoke-static {v0}, Lt6/d;->T(Ljava/util/List;)Ljava/util/List;

    .line 26
    .line 27
    .line 28
    move-result-object v0

    .line 29
    iput-object v0, p0, Ls6/x;->c:Ljava/util/List;

    .line 30
    .line 31
    invoke-virtual {p1}, Ls6/x$a;->x()Ljava/util/List;

    .line 32
    .line 33
    .line 34
    move-result-object v0

    .line 35
    invoke-static {v0}, Lt6/d;->T(Ljava/util/List;)Ljava/util/List;

    .line 36
    .line 37
    .line 38
    move-result-object v0

    .line 39
    iput-object v0, p0, Ls6/x;->d:Ljava/util/List;

    .line 40
    .line 41
    invoke-virtual {p1}, Ls6/x$a;->r()Ls6/r$c;

    .line 42
    .line 43
    .line 44
    move-result-object v0

    .line 45
    iput-object v0, p0, Ls6/x;->e:Ls6/r$c;

    .line 46
    .line 47
    invoke-virtual {p1}, Ls6/x$a;->E()Z

    .line 48
    .line 49
    .line 50
    move-result v0

    .line 51
    iput-boolean v0, p0, Ls6/x;->f:Z

    .line 52
    .line 53
    invoke-virtual {p1}, Ls6/x$a;->g()Ls6/b;

    .line 54
    .line 55
    .line 56
    move-result-object v0

    .line 57
    iput-object v0, p0, Ls6/x;->g:Ls6/b;

    .line 58
    .line 59
    invoke-virtual {p1}, Ls6/x$a;->s()Z

    .line 60
    .line 61
    .line 62
    move-result v0

    .line 63
    iput-boolean v0, p0, Ls6/x;->h:Z

    .line 64
    .line 65
    invoke-virtual {p1}, Ls6/x$a;->t()Z

    .line 66
    .line 67
    .line 68
    move-result v0

    .line 69
    iput-boolean v0, p0, Ls6/x;->i:Z

    .line 70
    .line 71
    invoke-virtual {p1}, Ls6/x$a;->o()Ls6/n;

    .line 72
    .line 73
    .line 74
    move-result-object v0

    .line 75
    iput-object v0, p0, Ls6/x;->j:Ls6/n;

    .line 76
    .line 77
    invoke-virtual {p1}, Ls6/x$a;->h()Ls6/c;

    .line 78
    .line 79
    .line 80
    move-result-object v0

    .line 81
    iput-object v0, p0, Ls6/x;->k:Ls6/c;

    .line 82
    .line 83
    invoke-virtual {p1}, Ls6/x$a;->q()Ls6/q;

    .line 84
    .line 85
    .line 86
    move-result-object v0

    .line 87
    iput-object v0, p0, Ls6/x;->l:Ls6/q;

    .line 88
    .line 89
    invoke-virtual {p1}, Ls6/x$a;->A()Ljava/net/Proxy;

    .line 90
    .line 91
    .line 92
    move-result-object v0

    .line 93
    iput-object v0, p0, Ls6/x;->m:Ljava/net/Proxy;

    .line 94
    .line 95
    invoke-virtual {p1}, Ls6/x$a;->A()Ljava/net/Proxy;

    .line 96
    .line 97
    .line 98
    move-result-object v0

    .line 99
    if-eqz v0, :cond_0

    .line 100
    .line 101
    sget-object v0, LE6/a;->a:LE6/a;

    .line 102
    .line 103
    goto :goto_0

    .line 104
    :cond_0
    invoke-virtual {p1}, Ls6/x$a;->C()Ljava/net/ProxySelector;

    .line 105
    .line 106
    .line 107
    move-result-object v0

    .line 108
    if-nez v0, :cond_1

    .line 109
    .line 110
    invoke-static {}, Ljava/net/ProxySelector;->getDefault()Ljava/net/ProxySelector;

    .line 111
    .line 112
    .line 113
    move-result-object v0

    .line 114
    :cond_1
    if-nez v0, :cond_2

    .line 115
    .line 116
    sget-object v0, LE6/a;->a:LE6/a;

    .line 117
    .line 118
    :cond_2
    :goto_0
    iput-object v0, p0, Ls6/x;->n:Ljava/net/ProxySelector;

    .line 119
    .line 120
    invoke-virtual {p1}, Ls6/x$a;->B()Ls6/b;

    .line 121
    .line 122
    .line 123
    move-result-object v0

    .line 124
    iput-object v0, p0, Ls6/x;->o:Ls6/b;

    .line 125
    .line 126
    invoke-virtual {p1}, Ls6/x$a;->G()Ljavax/net/SocketFactory;

    .line 127
    .line 128
    .line 129
    move-result-object v0

    .line 130
    iput-object v0, p0, Ls6/x;->p:Ljavax/net/SocketFactory;

    .line 131
    .line 132
    invoke-virtual {p1}, Ls6/x$a;->n()Ljava/util/List;

    .line 133
    .line 134
    .line 135
    move-result-object v0

    .line 136
    iput-object v0, p0, Ls6/x;->s:Ljava/util/List;

    .line 137
    .line 138
    invoke-virtual {p1}, Ls6/x$a;->z()Ljava/util/List;

    .line 139
    .line 140
    .line 141
    move-result-object v1

    .line 142
    iput-object v1, p0, Ls6/x;->t:Ljava/util/List;

    .line 143
    .line 144
    invoke-virtual {p1}, Ls6/x$a;->u()Ljavax/net/ssl/HostnameVerifier;

    .line 145
    .line 146
    .line 147
    move-result-object v1

    .line 148
    iput-object v1, p0, Ls6/x;->u:Ljavax/net/ssl/HostnameVerifier;

    .line 149
    .line 150
    invoke-virtual {p1}, Ls6/x$a;->i()I

    .line 151
    .line 152
    .line 153
    move-result v1

    .line 154
    iput v1, p0, Ls6/x;->x:I

    .line 155
    .line 156
    invoke-virtual {p1}, Ls6/x$a;->l()I

    .line 157
    .line 158
    .line 159
    move-result v1

    .line 160
    iput v1, p0, Ls6/x;->y:I

    .line 161
    .line 162
    invoke-virtual {p1}, Ls6/x$a;->D()I

    .line 163
    .line 164
    .line 165
    move-result v1

    .line 166
    iput v1, p0, Ls6/x;->z:I

    .line 167
    .line 168
    invoke-virtual {p1}, Ls6/x$a;->I()I

    .line 169
    .line 170
    .line 171
    move-result v1

    .line 172
    iput v1, p0, Ls6/x;->A:I

    .line 173
    .line 174
    invoke-virtual {p1}, Ls6/x$a;->y()I

    .line 175
    .line 176
    .line 177
    move-result v1

    .line 178
    iput v1, p0, Ls6/x;->B:I

    .line 179
    .line 180
    invoke-virtual {p1}, Ls6/x$a;->w()J

    .line 181
    .line 182
    .line 183
    move-result-wide v1

    .line 184
    iput-wide v1, p0, Ls6/x;->C:J

    .line 185
    .line 186
    invoke-virtual {p1}, Ls6/x$a;->F()Lx6/h;

    .line 187
    .line 188
    .line 189
    move-result-object v1

    .line 190
    if-nez v1, :cond_3

    .line 191
    .line 192
    new-instance v1, Lx6/h;

    .line 193
    .line 194
    invoke-direct {v1}, Lx6/h;-><init>()V

    .line 195
    .line 196
    .line 197
    :cond_3
    iput-object v1, p0, Ls6/x;->D:Lx6/h;

    .line 198
    .line 199
    check-cast v0, Ljava/lang/Iterable;

    .line 200
    .line 201
    instance-of v1, v0, Ljava/util/Collection;

    .line 202
    .line 203
    if-eqz v1, :cond_4

    .line 204
    .line 205
    move-object v1, v0

    .line 206
    check-cast v1, Ljava/util/Collection;

    .line 207
    .line 208
    invoke-interface {v1}, Ljava/util/Collection;->isEmpty()Z

    .line 209
    .line 210
    .line 211
    move-result v1

    .line 212
    if-eqz v1, :cond_4

    .line 213
    .line 214
    goto :goto_1

    .line 215
    :cond_4
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 216
    .line 217
    .line 218
    move-result-object v0

    .line 219
    :cond_5
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 220
    .line 221
    .line 222
    move-result v1

    .line 223
    if-eqz v1, :cond_7

    .line 224
    .line 225
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 226
    .line 227
    .line 228
    move-result-object v1

    .line 229
    check-cast v1, Ls6/l;

    .line 230
    .line 231
    invoke-virtual {v1}, Ls6/l;->f()Z

    .line 232
    .line 233
    .line 234
    move-result v1

    .line 235
    if-eqz v1, :cond_5

    .line 236
    .line 237
    invoke-virtual {p1}, Ls6/x$a;->H()Ljavax/net/ssl/SSLSocketFactory;

    .line 238
    .line 239
    .line 240
    move-result-object v0

    .line 241
    if-eqz v0, :cond_6

    .line 242
    .line 243
    invoke-virtual {p1}, Ls6/x$a;->H()Ljavax/net/ssl/SSLSocketFactory;

    .line 244
    .line 245
    .line 246
    move-result-object v0

    .line 247
    iput-object v0, p0, Ls6/x;->q:Ljavax/net/ssl/SSLSocketFactory;

    .line 248
    .line 249
    invoke-virtual {p1}, Ls6/x$a;->j()LF6/c;

    .line 250
    .line 251
    .line 252
    move-result-object v0

    .line 253
    invoke-static {v0}, Lkotlin/jvm/internal/r;->c(Ljava/lang/Object;)V

    .line 254
    .line 255
    .line 256
    iput-object v0, p0, Ls6/x;->w:LF6/c;

    .line 257
    .line 258
    invoke-virtual {p1}, Ls6/x$a;->J()Ljavax/net/ssl/X509TrustManager;

    .line 259
    .line 260
    .line 261
    move-result-object v1

    .line 262
    invoke-static {v1}, Lkotlin/jvm/internal/r;->c(Ljava/lang/Object;)V

    .line 263
    .line 264
    .line 265
    iput-object v1, p0, Ls6/x;->r:Ljavax/net/ssl/X509TrustManager;

    .line 266
    .line 267
    invoke-virtual {p1}, Ls6/x$a;->k()Ls6/g;

    .line 268
    .line 269
    .line 270
    move-result-object p1

    .line 271
    invoke-static {v0}, Lkotlin/jvm/internal/r;->c(Ljava/lang/Object;)V

    .line 272
    .line 273
    .line 274
    invoke-virtual {p1, v0}, Ls6/g;->e(LF6/c;)Ls6/g;

    .line 275
    .line 276
    .line 277
    move-result-object p1

    .line 278
    iput-object p1, p0, Ls6/x;->v:Ls6/g;

    .line 279
    .line 280
    goto :goto_2

    .line 281
    :cond_6
    sget-object v0, LC6/j;->a:LC6/j$a;

    .line 282
    .line 283
    invoke-virtual {v0}, LC6/j$a;->g()LC6/j;

    .line 284
    .line 285
    .line 286
    move-result-object v1

    .line 287
    invoke-virtual {v1}, LC6/j;->p()Ljavax/net/ssl/X509TrustManager;

    .line 288
    .line 289
    .line 290
    move-result-object v1

    .line 291
    iput-object v1, p0, Ls6/x;->r:Ljavax/net/ssl/X509TrustManager;

    .line 292
    .line 293
    invoke-virtual {v0}, LC6/j$a;->g()LC6/j;

    .line 294
    .line 295
    .line 296
    move-result-object v0

    .line 297
    invoke-static {v1}, Lkotlin/jvm/internal/r;->c(Ljava/lang/Object;)V

    .line 298
    .line 299
    .line 300
    invoke-virtual {v0, v1}, LC6/j;->o(Ljavax/net/ssl/X509TrustManager;)Ljavax/net/ssl/SSLSocketFactory;

    .line 301
    .line 302
    .line 303
    move-result-object v0

    .line 304
    iput-object v0, p0, Ls6/x;->q:Ljavax/net/ssl/SSLSocketFactory;

    .line 305
    .line 306
    sget-object v0, LF6/c;->a:LF6/c$a;

    .line 307
    .line 308
    invoke-static {v1}, Lkotlin/jvm/internal/r;->c(Ljava/lang/Object;)V

    .line 309
    .line 310
    .line 311
    invoke-virtual {v0, v1}, LF6/c$a;->a(Ljavax/net/ssl/X509TrustManager;)LF6/c;

    .line 312
    .line 313
    .line 314
    move-result-object v0

    .line 315
    iput-object v0, p0, Ls6/x;->w:LF6/c;

    .line 316
    .line 317
    invoke-virtual {p1}, Ls6/x$a;->k()Ls6/g;

    .line 318
    .line 319
    .line 320
    move-result-object p1

    .line 321
    invoke-static {v0}, Lkotlin/jvm/internal/r;->c(Ljava/lang/Object;)V

    .line 322
    .line 323
    .line 324
    invoke-virtual {p1, v0}, Ls6/g;->e(LF6/c;)Ls6/g;

    .line 325
    .line 326
    .line 327
    move-result-object p1

    .line 328
    iput-object p1, p0, Ls6/x;->v:Ls6/g;

    .line 329
    .line 330
    goto :goto_2

    .line 331
    :cond_7
    :goto_1
    const/4 p1, 0x0

    .line 332
    iput-object p1, p0, Ls6/x;->q:Ljavax/net/ssl/SSLSocketFactory;

    .line 333
    .line 334
    iput-object p1, p0, Ls6/x;->w:LF6/c;

    .line 335
    .line 336
    iput-object p1, p0, Ls6/x;->r:Ljavax/net/ssl/X509TrustManager;

    .line 337
    .line 338
    sget-object p1, Ls6/g;->d:Ls6/g;

    .line 339
    .line 340
    iput-object p1, p0, Ls6/x;->v:Ls6/g;

    .line 341
    .line 342
    :goto_2
    invoke-virtual {p0}, Ls6/x;->F()V

    .line 343
    .line 344
    .line 345
    return-void
.end method

.method public static final synthetic d()Ljava/util/List;
    .locals 1

    .line 1
    sget-object v0, Ls6/x;->G:Ljava/util/List;

    .line 2
    .line 3
    return-object v0
.end method

.method public static final synthetic e()Ljava/util/List;
    .locals 1

    .line 1
    sget-object v0, Ls6/x;->F:Ljava/util/List;

    .line 2
    .line 3
    return-object v0
.end method


# virtual methods
.method public final A()Ljava/net/ProxySelector;
    .locals 1

    .line 1
    iget-object v0, p0, Ls6/x;->n:Ljava/net/ProxySelector;

    .line 2
    .line 3
    return-object v0
.end method

.method public final B()I
    .locals 1

    .line 1
    iget v0, p0, Ls6/x;->z:I

    .line 2
    .line 3
    return v0
.end method

.method public final C()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Ls6/x;->f:Z

    .line 2
    .line 3
    return v0
.end method

.method public final D()Ljavax/net/SocketFactory;
    .locals 1

    .line 1
    iget-object v0, p0, Ls6/x;->p:Ljavax/net/SocketFactory;

    .line 2
    .line 3
    return-object v0
.end method

.method public final E()Ljavax/net/ssl/SSLSocketFactory;
    .locals 2

    .line 1
    iget-object v0, p0, Ls6/x;->q:Ljavax/net/ssl/SSLSocketFactory;

    .line 2
    .line 3
    if-eqz v0, :cond_0

    .line 4
    .line 5
    return-object v0

    .line 6
    :cond_0
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 7
    .line 8
    const-string v1, "CLEARTEXT-only client"

    .line 9
    .line 10
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 11
    .line 12
    .line 13
    throw v0
.end method

.method public final F()V
    .locals 3

    .line 1
    iget-object v0, p0, Ls6/x;->c:Ljava/util/List;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-interface {v0, v1}, Ljava/util/List;->contains(Ljava/lang/Object;)Z

    .line 5
    .line 6
    .line 7
    move-result v0

    .line 8
    xor-int/lit8 v0, v0, 0x1

    .line 9
    .line 10
    if-eqz v0, :cond_b

    .line 11
    .line 12
    iget-object v0, p0, Ls6/x;->d:Ljava/util/List;

    .line 13
    .line 14
    invoke-interface {v0, v1}, Ljava/util/List;->contains(Ljava/lang/Object;)Z

    .line 15
    .line 16
    .line 17
    move-result v0

    .line 18
    xor-int/lit8 v0, v0, 0x1

    .line 19
    .line 20
    if-eqz v0, :cond_a

    .line 21
    .line 22
    iget-object v0, p0, Ls6/x;->s:Ljava/util/List;

    .line 23
    .line 24
    check-cast v0, Ljava/lang/Iterable;

    .line 25
    .line 26
    instance-of v1, v0, Ljava/util/Collection;

    .line 27
    .line 28
    if-eqz v1, :cond_0

    .line 29
    .line 30
    move-object v1, v0

    .line 31
    check-cast v1, Ljava/util/Collection;

    .line 32
    .line 33
    invoke-interface {v1}, Ljava/util/Collection;->isEmpty()Z

    .line 34
    .line 35
    .line 36
    move-result v1

    .line 37
    if-eqz v1, :cond_0

    .line 38
    .line 39
    goto :goto_0

    .line 40
    :cond_0
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 41
    .line 42
    .line 43
    move-result-object v0

    .line 44
    :cond_1
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 45
    .line 46
    .line 47
    move-result v1

    .line 48
    if-eqz v1, :cond_5

    .line 49
    .line 50
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 51
    .line 52
    .line 53
    move-result-object v1

    .line 54
    check-cast v1, Ls6/l;

    .line 55
    .line 56
    invoke-virtual {v1}, Ls6/l;->f()Z

    .line 57
    .line 58
    .line 59
    move-result v1

    .line 60
    if-eqz v1, :cond_1

    .line 61
    .line 62
    iget-object v0, p0, Ls6/x;->q:Ljavax/net/ssl/SSLSocketFactory;

    .line 63
    .line 64
    if-eqz v0, :cond_4

    .line 65
    .line 66
    iget-object v0, p0, Ls6/x;->w:LF6/c;

    .line 67
    .line 68
    if-eqz v0, :cond_3

    .line 69
    .line 70
    iget-object v0, p0, Ls6/x;->r:Ljavax/net/ssl/X509TrustManager;

    .line 71
    .line 72
    if-eqz v0, :cond_2

    .line 73
    .line 74
    goto :goto_1

    .line 75
    :cond_2
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 76
    .line 77
    const-string v1, "x509TrustManager == null"

    .line 78
    .line 79
    invoke-virtual {v1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 80
    .line 81
    .line 82
    move-result-object v1

    .line 83
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 84
    .line 85
    .line 86
    throw v0

    .line 87
    :cond_3
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 88
    .line 89
    const-string v1, "certificateChainCleaner == null"

    .line 90
    .line 91
    invoke-virtual {v1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 92
    .line 93
    .line 94
    move-result-object v1

    .line 95
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 96
    .line 97
    .line 98
    throw v0

    .line 99
    :cond_4
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 100
    .line 101
    const-string v1, "sslSocketFactory == null"

    .line 102
    .line 103
    invoke-virtual {v1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 104
    .line 105
    .line 106
    move-result-object v1

    .line 107
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 108
    .line 109
    .line 110
    throw v0

    .line 111
    :cond_5
    :goto_0
    iget-object v0, p0, Ls6/x;->q:Ljavax/net/ssl/SSLSocketFactory;

    .line 112
    .line 113
    const-string v1, "Check failed."

    .line 114
    .line 115
    if-nez v0, :cond_9

    .line 116
    .line 117
    iget-object v0, p0, Ls6/x;->w:LF6/c;

    .line 118
    .line 119
    if-nez v0, :cond_8

    .line 120
    .line 121
    iget-object v0, p0, Ls6/x;->r:Ljavax/net/ssl/X509TrustManager;

    .line 122
    .line 123
    if-nez v0, :cond_7

    .line 124
    .line 125
    iget-object v0, p0, Ls6/x;->v:Ls6/g;

    .line 126
    .line 127
    sget-object v2, Ls6/g;->d:Ls6/g;

    .line 128
    .line 129
    invoke-static {v0, v2}, Lkotlin/jvm/internal/r;->b(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 130
    .line 131
    .line 132
    move-result v0

    .line 133
    if-eqz v0, :cond_6

    .line 134
    .line 135
    :goto_1
    return-void

    .line 136
    :cond_6
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 137
    .line 138
    invoke-virtual {v1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 139
    .line 140
    .line 141
    move-result-object v1

    .line 142
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 143
    .line 144
    .line 145
    throw v0

    .line 146
    :cond_7
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 147
    .line 148
    invoke-virtual {v1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 149
    .line 150
    .line 151
    move-result-object v1

    .line 152
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 153
    .line 154
    .line 155
    throw v0

    .line 156
    :cond_8
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 157
    .line 158
    invoke-virtual {v1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 159
    .line 160
    .line 161
    move-result-object v1

    .line 162
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 163
    .line 164
    .line 165
    throw v0

    .line 166
    :cond_9
    new-instance v0, Ljava/lang/IllegalStateException;

    .line 167
    .line 168
    invoke-virtual {v1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 169
    .line 170
    .line 171
    move-result-object v1

    .line 172
    invoke-direct {v0, v1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 173
    .line 174
    .line 175
    throw v0

    .line 176
    :cond_a
    const-string v0, "Null network interceptor: "

    .line 177
    .line 178
    invoke-virtual {p0}, Ls6/x;->v()Ljava/util/List;

    .line 179
    .line 180
    .line 181
    move-result-object v1

    .line 182
    invoke-static {v0, v1}, Lkotlin/jvm/internal/r;->n(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/String;

    .line 183
    .line 184
    .line 185
    move-result-object v0

    .line 186
    new-instance v1, Ljava/lang/IllegalStateException;

    .line 187
    .line 188
    invoke-virtual {v0}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 189
    .line 190
    .line 191
    move-result-object v0

    .line 192
    invoke-direct {v1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 193
    .line 194
    .line 195
    throw v1

    .line 196
    :cond_b
    const-string v0, "Null interceptor: "

    .line 197
    .line 198
    invoke-virtual {p0}, Ls6/x;->u()Ljava/util/List;

    .line 199
    .line 200
    .line 201
    move-result-object v1

    .line 202
    invoke-static {v0, v1}, Lkotlin/jvm/internal/r;->n(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/String;

    .line 203
    .line 204
    .line 205
    move-result-object v0

    .line 206
    new-instance v1, Ljava/lang/IllegalStateException;

    .line 207
    .line 208
    invoke-virtual {v0}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 209
    .line 210
    .line 211
    move-result-object v0

    .line 212
    invoke-direct {v1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 213
    .line 214
    .line 215
    throw v1
.end method

.method public final G()I
    .locals 1

    .line 1
    iget v0, p0, Ls6/x;->A:I

    .line 2
    .line 3
    return v0
.end method

.method public b(Ls6/z;)Ls6/e;
    .locals 2

    .line 1
    const-string v0, "request"

    .line 2
    .line 3
    invoke-static {p1, v0}, Lkotlin/jvm/internal/r;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    new-instance v0, Lx6/e;

    .line 7
    .line 8
    const/4 v1, 0x0

    .line 9
    invoke-direct {v0, p0, p1, v1}, Lx6/e;-><init>(Ls6/x;Ls6/z;Z)V

    .line 10
    .line 11
    .line 12
    return-object v0
.end method

.method public clone()Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-super {p0}, Ljava/lang/Object;->clone()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method

.method public final f()Ls6/b;
    .locals 1

    .line 1
    iget-object v0, p0, Ls6/x;->g:Ls6/b;

    .line 2
    .line 3
    return-object v0
.end method

.method public final g()Ls6/c;
    .locals 1

    .line 1
    iget-object v0, p0, Ls6/x;->k:Ls6/c;

    .line 2
    .line 3
    return-object v0
.end method

.method public final h()I
    .locals 1

    .line 1
    iget v0, p0, Ls6/x;->x:I

    .line 2
    .line 3
    return v0
.end method

.method public final i()Ls6/g;
    .locals 1

    .line 1
    iget-object v0, p0, Ls6/x;->v:Ls6/g;

    .line 2
    .line 3
    return-object v0
.end method

.method public final j()I
    .locals 1

    .line 1
    iget v0, p0, Ls6/x;->y:I

    .line 2
    .line 3
    return v0
.end method

.method public final k()Ls6/k;
    .locals 1

    .line 1
    iget-object v0, p0, Ls6/x;->b:Ls6/k;

    .line 2
    .line 3
    return-object v0
.end method

.method public final l()Ljava/util/List;
    .locals 1

    .line 1
    iget-object v0, p0, Ls6/x;->s:Ljava/util/List;

    .line 2
    .line 3
    return-object v0
.end method

.method public final m()Ls6/n;
    .locals 1

    .line 1
    iget-object v0, p0, Ls6/x;->j:Ls6/n;

    .line 2
    .line 3
    return-object v0
.end method

.method public final n()Ls6/p;
    .locals 1

    .line 1
    iget-object v0, p0, Ls6/x;->a:Ls6/p;

    .line 2
    .line 3
    return-object v0
.end method

.method public final o()Ls6/q;
    .locals 1

    .line 1
    iget-object v0, p0, Ls6/x;->l:Ls6/q;

    .line 2
    .line 3
    return-object v0
.end method

.method public final p()Ls6/r$c;
    .locals 1

    .line 1
    iget-object v0, p0, Ls6/x;->e:Ls6/r$c;

    .line 2
    .line 3
    return-object v0
.end method

.method public final q()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Ls6/x;->h:Z

    .line 2
    .line 3
    return v0
.end method

.method public final r()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Ls6/x;->i:Z

    .line 2
    .line 3
    return v0
.end method

.method public final s()Lx6/h;
    .locals 1

    .line 1
    iget-object v0, p0, Ls6/x;->D:Lx6/h;

    .line 2
    .line 3
    return-object v0
.end method

.method public final t()Ljavax/net/ssl/HostnameVerifier;
    .locals 1

    .line 1
    iget-object v0, p0, Ls6/x;->u:Ljavax/net/ssl/HostnameVerifier;

    .line 2
    .line 3
    return-object v0
.end method

.method public final u()Ljava/util/List;
    .locals 1

    .line 1
    iget-object v0, p0, Ls6/x;->c:Ljava/util/List;

    .line 2
    .line 3
    return-object v0
.end method

.method public final v()Ljava/util/List;
    .locals 1

    .line 1
    iget-object v0, p0, Ls6/x;->d:Ljava/util/List;

    .line 2
    .line 3
    return-object v0
.end method

.method public final w()I
    .locals 1

    .line 1
    iget v0, p0, Ls6/x;->B:I

    .line 2
    .line 3
    return v0
.end method

.method public final x()Ljava/util/List;
    .locals 1

    .line 1
    iget-object v0, p0, Ls6/x;->t:Ljava/util/List;

    .line 2
    .line 3
    return-object v0
.end method

.method public final y()Ljava/net/Proxy;
    .locals 1

    .line 1
    iget-object v0, p0, Ls6/x;->m:Ljava/net/Proxy;

    .line 2
    .line 3
    return-object v0
.end method

.method public final z()Ls6/b;
    .locals 1

    .line 1
    iget-object v0, p0, Ls6/x;->o:Ls6/b;

    .line 2
    .line 3
    return-object v0
.end method
