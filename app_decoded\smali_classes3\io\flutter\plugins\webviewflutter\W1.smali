.class public final synthetic Lio/flutter/plugins/webviewflutter/W1;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LM5/k;


# direct methods
.method public synthetic constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Ly5/s;

    invoke-static {p1}, Lio/flutter/plugins/webviewflutter/WebViewClientProxyApi$WebViewClientCompatImpl;->j(Ly5/s;)Ly5/I;

    move-result-object p1

    return-object p1
.end method
