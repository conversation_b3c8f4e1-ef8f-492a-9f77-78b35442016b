name: mbark_iptv
description: A new Flutter project.

publish_to: 'none' # Remove this line if you wish to publish to pub.dev

version: 1.0.7+8

environment:
  sdk: '>=3.5.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter

  cupertino_icons: ^1.0.8
  font_awesome_flutter: ^10.7.0
  cached_network_image: ^3.4.1
  responsive_sizer: ^3.3.1
  awesome_snackbar_content: 0.1.1
  google_fonts: ^6.2.1
  flutter_rating_bar: ^4.0.1
  readmore: ^3.0.0
  filling_slider: ^0.0.3
  loading_animation_widget: ^1.2.1
  wakelock_plus: ^1.2.8

  #Logic
  flutter_bloc: ^8.1.6
  get: ^4.6.6
  intl: 0.20.2
  get_storage: ^2.1.1

  #Video Players & Media
  flutter_vlc_player: ^7.4.2
  pod_player: ^0.2.2
  media_kit: ^1.1.10+1
  media_kit_video: ^1.2.4
  media_kit_libs_video: ^1.0.4
  volume_controller: ^2.0.8

  #Http & Network
  dio: ^5.6.0
  url_launcher: ^6.3.0
  google_mobile_ads: ^5.1.0

dependency_overrides:
  wakelock_windows: any
  win32: any


dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^4.0.0

flutter:

  uses-material-design: true

  assets:
     - assets/images/
     - assets/images/ic_fill.svg
     - assets/images/ic_fit_width.svg
     - assets/images/ic_fit_height.svg

