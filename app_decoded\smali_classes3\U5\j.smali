.class public abstract LU5/j;
.super LU5/i;
.source "SourceFile"


# direct methods
.method public static a(Ljava/util/Iterator;)LU5/f;
    .locals 1

    .line 1
    const-string v0, "<this>"

    .line 2
    .line 3
    invoke-static {p0, v0}, <PERSON><PERSON><PERSON>/jvm/internal/r;->f(<PERSON><PERSON><PERSON>/lang/Object;Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    new-instance v0, LU5/j$a;

    .line 7
    .line 8
    invoke-direct {v0, p0}, LU5/j$a;-><init>(Ljava/util/Iterator;)V

    .line 9
    .line 10
    .line 11
    invoke-static {v0}, LU5/g;->b(LU5/f;)LU5/f;

    .line 12
    .line 13
    .line 14
    move-result-object p0

    .line 15
    return-object p0
.end method

.method public static b(LU5/f;)LU5/f;
    .locals 1

    .line 1
    const-string v0, "<this>"

    .line 2
    .line 3
    invoke-static {p0, v0}, <PERSON><PERSON><PERSON>/jvm/internal/r;->f(<PERSON><PERSON><PERSON>/lang/Object;Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    instance-of v0, p0, LU5/a;

    .line 7
    .line 8
    if-eqz v0, :cond_0

    .line 9
    .line 10
    goto :goto_0

    .line 11
    :cond_0
    new-instance v0, LU5/a;

    .line 12
    .line 13
    invoke-direct {v0, p0}, LU5/a;-><init>(LU5/f;)V

    .line 14
    .line 15
    .line 16
    move-object p0, v0

    .line 17
    :goto_0
    return-object p0
.end method

.method public static c()LU5/f;
    .locals 1

    .line 1
    sget-object v0, LU5/d;->a:LU5/d;

    .line 2
    .line 3
    return-object v0
.end method

.method public static d(Lkotlin/jvm/functions/Function0;LM5/k;)LU5/f;
    .locals 1

    .line 1
    const-string v0, "seedFunction"

    .line 2
    .line 3
    invoke-static {p0, v0}, Lkotlin/jvm/internal/r;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    const-string v0, "nextFunction"

    .line 7
    .line 8
    invoke-static {p1, v0}, Lkotlin/jvm/internal/r;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 9
    .line 10
    .line 11
    new-instance v0, LU5/e;

    .line 12
    .line 13
    invoke-direct {v0, p0, p1}, LU5/e;-><init>(Lkotlin/jvm/functions/Function0;LM5/k;)V

    .line 14
    .line 15
    .line 16
    return-object v0
.end method
