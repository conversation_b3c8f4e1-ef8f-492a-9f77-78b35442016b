import 'package:flutter/foundation.dart';
import '../models/video_models.dart';

class PlaylistManager extends ChangeNotifier {
  static final PlaylistManager _instance = PlaylistManager._internal();
  factory PlaylistManager() => _instance;
  PlaylistManager._internal();

  List<PlaylistItem> _playlist = [];
  int _currentIndex = 0;
  bool _isShuffleEnabled = false;
  bool _isRepeatEnabled = false;

  List<PlaylistItem> get playlist => _playlist;
  int get currentIndex => _currentIndex;
  bool get isShuffleEnabled => _isShuffleEnabled;
  bool get isRepeatEnabled => _isRepeatEnabled;
  
  PlaylistItem? get currentItem => 
      _playlist.isNotEmpty ? _playlist[_currentIndex] : null;

  bool get hasNext => _currentIndex < _playlist.length - 1;
  bool get hasPrevious => _currentIndex > 0;

  void setPlaylist(List<PlaylistItem> items, {int startIndex = 0}) {
    _playlist = items;
    _currentIndex = startIndex.clamp(0, items.length - 1);
    notifyListeners();
  }

  void addToPlaylist(PlaylistItem item) {
    _playlist.add(item);
    notifyListeners();
  }

  void removeFromPlaylist(int index) {
    if (index >= 0 && index < _playlist.length) {
      _playlist.removeAt(index);
      if (_currentIndex >= index && _currentIndex > 0) {
        _currentIndex--;
      }
      notifyListeners();
    }
  }

  void playNext() {
    if (_isShuffleEnabled) {
      _currentIndex = (_currentIndex + 1) % _playlist.length;
    } else if (hasNext) {
      _currentIndex++;
    } else if (_isRepeatEnabled) {
      _currentIndex = 0;
    }
    notifyListeners();
  }

  void playPrevious() {
    if (hasPrevious) {
      _currentIndex--;
    } else if (_isRepeatEnabled) {
      _currentIndex = _playlist.length - 1;
    }
    notifyListeners();
  }

  void playAtIndex(int index) {
    if (index >= 0 && index < _playlist.length) {
      _currentIndex = index;
      notifyListeners();
    }
  }

  void toggleShuffle() {
    _isShuffleEnabled = !_isShuffleEnabled;
    notifyListeners();
  }

  void toggleRepeat() {
    _isRepeatEnabled = !_isRepeatEnabled;
    notifyListeners();
  }

  void clearPlaylist() {
    _playlist.clear();
    _currentIndex = 0;
    notifyListeners();
  }
}
