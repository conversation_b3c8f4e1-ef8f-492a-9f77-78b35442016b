.class public abstract LV5/h$a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LV5/h;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation


# direct methods
.method public static a(LV5/h;)LV5/h$b;
    .locals 1

    .line 1
    new-instance v0, LV5/h$b;

    .line 2
    .line 3
    invoke-direct {v0, p0}, LV5/h$b;-><init>(LV5/h;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method
