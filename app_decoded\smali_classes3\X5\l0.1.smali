.class public final LX5/l0;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LX5/J;


# static fields
.field public static final a:LX5/l0;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, LX5/l0;

    .line 2
    .line 3
    invoke-direct {v0}, LX5/l0;-><init>()V

    .line 4
    .line 5
    .line 6
    sput-object v0, LX5/l0;->a:LX5/l0;

    .line 7
    .line 8
    return-void
.end method

.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public p()LD5/g;
    .locals 1

    .line 1
    sget-object v0, LD5/h;->a:LD5/h;

    .line 2
    .line 3
    return-object v0
.end method
