.class public final Ly6/h;
.super Ls6/C;
.source "SourceFile"


# instance fields
.field public final a:Ljava/lang/String;

.field public final b:J

.field public final c:LG6/g;


# direct methods
.method public constructor <init>(Ljava/lang/String;JLG6/g;)V
    .locals 1

    .line 1
    const-string v0, "source"

    .line 2
    .line 3
    invoke-static {p4, v0}, L<PERSON>lin/jvm/internal/r;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    invoke-direct {p0}, Ls6/C;-><init>()V

    .line 7
    .line 8
    .line 9
    iput-object p1, p0, Ly6/h;->a:Ljava/lang/String;

    .line 10
    .line 11
    iput-wide p2, p0, Ly6/h;->b:J

    .line 12
    .line 13
    iput-object p4, p0, Ly6/h;->c:LG6/g;

    .line 14
    .line 15
    return-void
.end method


# virtual methods
.method public contentLength()J
    .locals 2

    .line 1
    iget-wide v0, p0, Ly6/h;->b:J

    .line 2
    .line 3
    return-wide v0
.end method

.method public contentType()Ls6/w;
    .locals 2

    .line 1
    iget-object v0, p0, Ly6/h;->a:Ljava/lang/String;

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    const/4 v0, 0x0

    .line 6
    goto :goto_0

    .line 7
    :cond_0
    sget-object v1, Ls6/w;->e:Ls6/w$a;

    .line 8
    .line 9
    invoke-virtual {v1, v0}, Ls6/w$a;->b(Ljava/lang/String;)Ls6/w;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    :goto_0
    return-object v0
.end method

.method public source()LG6/g;
    .locals 1

    .line 1
    iget-object v0, p0, Ly6/h;->c:LG6/g;

    .line 2
    .line 3
    return-object v0
.end method
