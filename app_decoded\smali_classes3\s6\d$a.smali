.class public final Ls6/d$a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Ls6/d;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation


# instance fields
.field public a:Z

.field public b:Z

.field public c:I

.field public d:I

.field public e:I

.field public f:Z

.field public g:Z

.field public h:Z


# direct methods
.method public constructor <init>()V
    .locals 1

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    const/4 v0, -0x1

    .line 5
    iput v0, p0, Ls6/d$a;->c:I

    .line 6
    .line 7
    iput v0, p0, Ls6/d$a;->d:I

    .line 8
    .line 9
    iput v0, p0, Ls6/d$a;->e:I

    .line 10
    .line 11
    return-void
.end method


# virtual methods
.method public final a()Ls6/d;
    .locals 17

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    new-instance v16, Ls6/d;

    .line 4
    .line 5
    iget-boolean v2, v0, Ls6/d$a;->a:Z

    .line 6
    .line 7
    iget-boolean v3, v0, Ls6/d$a;->b:Z

    .line 8
    .line 9
    iget v4, v0, Ls6/d$a;->c:I

    .line 10
    .line 11
    iget v9, v0, Ls6/d$a;->d:I

    .line 12
    .line 13
    iget v10, v0, Ls6/d$a;->e:I

    .line 14
    .line 15
    iget-boolean v11, v0, Ls6/d$a;->f:Z

    .line 16
    .line 17
    iget-boolean v12, v0, Ls6/d$a;->g:Z

    .line 18
    .line 19
    iget-boolean v13, v0, Ls6/d$a;->h:Z

    .line 20
    .line 21
    const/4 v14, 0x0

    .line 22
    const/4 v15, 0x0

    .line 23
    const/4 v5, -0x1

    .line 24
    const/4 v6, 0x0

    .line 25
    const/4 v7, 0x0

    .line 26
    const/4 v8, 0x0

    .line 27
    move-object/from16 v1, v16

    .line 28
    .line 29
    invoke-direct/range {v1 .. v15}, Ls6/d;-><init>(ZZIIZZZIIZZZLjava/lang/String;Lkotlin/jvm/internal/j;)V

    .line 30
    .line 31
    .line 32
    return-object v16
.end method

.method public final b(J)I
    .locals 2

    .line 1
    const-wide/32 v0, 0x7fffffff

    .line 2
    .line 3
    .line 4
    cmp-long v0, p1, v0

    .line 5
    .line 6
    if-lez v0, :cond_0

    .line 7
    .line 8
    const p1, 0x7fffffff

    .line 9
    .line 10
    .line 11
    goto :goto_0

    .line 12
    :cond_0
    long-to-int p1, p1

    .line 13
    :goto_0
    return p1
.end method

.method public final c(ILjava/util/concurrent/TimeUnit;)Ls6/d$a;
    .locals 2

    .line 1
    const-string v0, "timeUnit"

    .line 2
    .line 3
    invoke-static {p2, v0}, Lkotlin/jvm/internal/r;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    if-ltz p1, :cond_0

    .line 7
    .line 8
    int-to-long v0, p1

    .line 9
    invoke-virtual {p2, v0, v1}, Ljava/util/concurrent/TimeUnit;->toSeconds(J)J

    .line 10
    .line 11
    .line 12
    move-result-wide p1

    .line 13
    invoke-virtual {p0, p1, p2}, Ls6/d$a;->b(J)I

    .line 14
    .line 15
    .line 16
    move-result p1

    .line 17
    iput p1, p0, Ls6/d$a;->d:I

    .line 18
    .line 19
    return-object p0

    .line 20
    :cond_0
    invoke-static {p1}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 21
    .line 22
    .line 23
    move-result-object p1

    .line 24
    const-string p2, "maxStale < 0: "

    .line 25
    .line 26
    invoke-static {p2, p1}, Lkotlin/jvm/internal/r;->n(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/String;

    .line 27
    .line 28
    .line 29
    move-result-object p1

    .line 30
    new-instance p2, Ljava/lang/IllegalArgumentException;

    .line 31
    .line 32
    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 33
    .line 34
    .line 35
    move-result-object p1

    .line 36
    invoke-direct {p2, p1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    .line 37
    .line 38
    .line 39
    throw p2
.end method

.method public final d()Ls6/d$a;
    .locals 1

    .line 1
    const/4 v0, 0x1

    .line 2
    iput-boolean v0, p0, Ls6/d$a;->a:Z

    .line 3
    .line 4
    return-object p0
.end method

.method public final e()Ls6/d$a;
    .locals 1

    .line 1
    const/4 v0, 0x1

    .line 2
    iput-boolean v0, p0, Ls6/d$a;->f:Z

    .line 3
    .line 4
    return-object p0
.end method
