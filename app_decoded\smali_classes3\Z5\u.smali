.class public interface abstract LZ5/u;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LZ5/u$a;
    }
.end annotation


# virtual methods
.method public abstract n(Ljava/lang/Object;LD5/d;)Ljava/lang/Object;
.end method

.method public abstract o(Ljava/lang/Throwable;)Z
.end method

.method public abstract q(LM5/k;)V
.end method

.method public abstract w(Ljava/lang/Object;)Ljava/lang/Object;
.end method

.method public abstract x()Z
.end method
