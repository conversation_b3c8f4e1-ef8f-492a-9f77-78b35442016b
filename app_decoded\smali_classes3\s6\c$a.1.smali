.class public final Ls6/c$a;
.super Ls6/C;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Ls6/c;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation


# instance fields
.field public final a:Lv6/d$d;

.field public final b:Ljava/lang/String;

.field public final c:Ljava/lang/String;

.field public final d:LG6/g;


# direct methods
.method public constructor <init>(Lv6/d$d;Ljava/lang/String;Ljava/lang/String;)V
    .locals 1

    .line 1
    const-string v0, "snapshot"

    .line 2
    .line 3
    invoke-static {p1, v0}, Lkotlin/jvm/internal/r;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    invoke-direct {p0}, Ls6/C;-><init>()V

    .line 7
    .line 8
    .line 9
    iput-object p1, p0, Ls6/c$a;->a:Lv6/d$d;

    .line 10
    .line 11
    iput-object p2, p0, Ls6/c$a;->b:Ljava/lang/String;

    .line 12
    .line 13
    iput-object p3, p0, Ls6/c$a;->c:Ljava/lang/String;

    .line 14
    .line 15
    const/4 p2, 0x1

    .line 16
    invoke-virtual {p1, p2}, Lv6/d$d;->b(I)LG6/Z;

    .line 17
    .line 18
    .line 19
    move-result-object p1

    .line 20
    new-instance p2, Ls6/c$a$a;

    .line 21
    .line 22
    invoke-direct {p2, p1, p0}, Ls6/c$a$a;-><init>(LG6/Z;Ls6/c$a;)V

    .line 23
    .line 24
    .line 25
    invoke-static {p2}, LG6/L;->d(LG6/Z;)LG6/g;

    .line 26
    .line 27
    .line 28
    move-result-object p1

    .line 29
    iput-object p1, p0, Ls6/c$a;->d:LG6/g;

    .line 30
    .line 31
    return-void
.end method


# virtual methods
.method public final b()Lv6/d$d;
    .locals 1

    .line 1
    iget-object v0, p0, Ls6/c$a;->a:Lv6/d$d;

    .line 2
    .line 3
    return-object v0
.end method

.method public contentLength()J
    .locals 3

    .line 1
    iget-object v0, p0, Ls6/c$a;->c:Ljava/lang/String;

    .line 2
    .line 3
    const-wide/16 v1, -0x1

    .line 4
    .line 5
    if-nez v0, :cond_0

    .line 6
    .line 7
    goto :goto_0

    .line 8
    :cond_0
    invoke-static {v0, v1, v2}, Lt6/d;->V(Ljava/lang/String;J)J

    .line 9
    .line 10
    .line 11
    move-result-wide v1

    .line 12
    :goto_0
    return-wide v1
.end method

.method public contentType()Ls6/w;
    .locals 2

    .line 1
    iget-object v0, p0, Ls6/c$a;->b:Ljava/lang/String;

    .line 2
    .line 3
    if-nez v0, :cond_0

    .line 4
    .line 5
    const/4 v0, 0x0

    .line 6
    goto :goto_0

    .line 7
    :cond_0
    sget-object v1, Ls6/w;->e:Ls6/w$a;

    .line 8
    .line 9
    invoke-virtual {v1, v0}, Ls6/w$a;->b(Ljava/lang/String;)Ls6/w;

    .line 10
    .line 11
    .line 12
    move-result-object v0

    .line 13
    :goto_0
    return-object v0
.end method

.method public source()LG6/g;
    .locals 1

    .line 1
    iget-object v0, p0, Ls6/c$a;->d:LG6/g;

    .line 2
    .line 3
    return-object v0
.end method
