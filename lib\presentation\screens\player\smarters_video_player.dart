import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_vlc_player/flutter_vlc_player.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:wakelock_plus/wakelock_plus.dart';
import 'package:volume_controller/volume_controller.dart';
import '../../../helpers/helpers.dart';

enum VideoFitMode {
  fill,
  fitWidth,
  fitHeight,
  contain,
}

class SmartersVideoPlayer extends StatefulWidget {
  final String videoUrl;
  final String title;
  final bool isLive;
  final List<String>? qualities;
  final List<String>? audioTracks;
  final List<String>? subtitleTracks;

  const SmartersVideoPlayer({
    Key? key,
    required this.videoUrl,
    required this.title,
    this.isLive = false,
    this.qualities,
    this.audioTracks,
    this.subtitleTracks,
  }) : super(key: key);

  @override
  State<SmartersVideoPlayer> createState() => _SmartersVideoPlayerState();
}

class _SmartersVideoPlayerState extends State<SmartersVideoPlayer> {
  late VlcPlayerController _controller;
  bool _showControls = true;
  bool _isPlaying = false;
  bool _isLoading = true;
  bool _isBuffering = false;

  // Progress tracking
  Duration _currentPosition = Duration.zero;
  Duration _totalDuration = Duration.zero;
  double _sliderValue = 0.0;
  bool _isDragging = false;

  // Buffer Bar
  double _bufferProgress = 0.0;
  Duration _bufferedDuration = Duration.zero;
  Timer? _bufferUpdateTimer;

  // Volume & Brightness
  double _currentVolume = 0.5;
  double _currentBrightness = 0.5;

  // Full screen support
  bool _isFullScreen = false;

  // Video fit mode
  VideoFitMode _fitMode = VideoFitMode.contain;

  // Subtitles & Audio tracks
  int _selectedSubtitleTrack = -1;
  int _selectedAudioTrack = 0;
  int _selectedQuality = 0;
  double _playbackSpeed = 1.0;

  // Auto-hide timer
  Timer? _hideTimer;

  // Gesture detection
  bool _isVolumeGesture = false;
  bool _isBrightnessGesture = false;
  bool _isSeekGesture = false;

  @override
  void initState() {
    super.initState();
    _initializePlayer();
    _initializeVolume();
    _startBufferTracking();
    WakelockPlus.enable();
    _startHideTimer();
  }

  void _initializePlayer() {
    _controller = VlcPlayerController.network(
      widget.videoUrl,
      hwAcc: HwAcc.full,
      autoPlay: true,
      autoInitialize: true,
      options: VlcPlayerOptions(
        advanced: VlcAdvancedOptions([
          VlcAdvancedOptions.networkCaching(2000),
          VlcAdvancedOptions.clockJitter(0),
        ]),
        video: VlcVideoOptions([
          VlcVideoOptions.dropLateFrames(true),
          VlcVideoOptions.skipFrames(true),
        ]),
        audio: VlcAudioOptions([
          VlcAudioOptions.audioTimeStretch(true),
        ]),
        rtp: VlcRtpOptions([
          VlcRtpOptions.rtpOverRtsp(true),
        ]),
      ),
    );

    _controller.addListener(_playerListener);
  }

  void _initializeVolume() async {
    try {
      _currentVolume = await VolumeController().getVolume();
      setState(() {});
    } catch (e) {
      debugPrint('Error getting volume: $e');
    }
  }

  void _startBufferTracking() {
    // Start continuous buffer tracking timer - غير محدود
    _bufferUpdateTimer =
        Timer.periodic(const Duration(milliseconds: 200), (timer) {
      if (mounted && _controller.value.isInitialized) {
        _updateBufferProgress();
      }
    });
  }

  void _updateBufferProgress() {
    if (!mounted || !_controller.value.isInitialized) return;

    // Continuous buffer progress simulation - غير محدود
    if (_totalDuration.inMilliseconds > 0) {
      if (widget.isLive) {
        // For live streams, buffer continuously grows
        const baseBufferMs = 8000; // 8 seconds base buffer
        final dynamicBuffer = (DateTime.now().millisecondsSinceEpoch % 10000) /
            1000; // 0-10 seconds dynamic
        final totalBufferMs = baseBufferMs + (dynamicBuffer * 1000).round();
        final simulatedBufferMs =
            _currentPosition.inMilliseconds + totalBufferMs;
        final clampedBufferMs =
            simulatedBufferMs.clamp(0, _totalDuration.inMilliseconds);

        if (mounted) {
          setState(() {
            _bufferedDuration = Duration(milliseconds: clampedBufferMs);
            _bufferProgress = clampedBufferMs / _totalDuration.inMilliseconds;
          });
        }
      } else {
        // For VOD content, buffer grows continuously and more aggressively
        const baseBufferMs = 45000; // 45 seconds base buffer
        final dynamicBuffer = (DateTime.now().millisecondsSinceEpoch % 20000) /
            1000; // 0-20 seconds dynamic
        final totalBufferMs = baseBufferMs + (dynamicBuffer * 1000).round();
        final simulatedBufferMs =
            _currentPosition.inMilliseconds + totalBufferMs;
        final clampedBufferMs =
            simulatedBufferMs.clamp(0, _totalDuration.inMilliseconds);

        if (mounted) {
          setState(() {
            _bufferedDuration = Duration(milliseconds: clampedBufferMs);
            _bufferProgress = clampedBufferMs / _totalDuration.inMilliseconds;
          });
        }
      }
    }
  }

  void _playerListener() {
    if (!mounted) return;

    final value = _controller.value;

    setState(() {
      _isPlaying = value.isPlaying;
      _isLoading = !value.isInitialized;
      _isBuffering = value.isBuffering;

      if (value.isInitialized && !_isDragging) {
        _currentPosition = value.position;
        _totalDuration = value.duration;

        if (_totalDuration.inMilliseconds > 0) {
          _sliderValue =
              _currentPosition.inMilliseconds / _totalDuration.inMilliseconds;
        }
      }
    });
  }

  void _startHideTimer() {
    _hideTimer?.cancel();
    _hideTimer = Timer(const Duration(seconds: 5), () {
      if (mounted && _showControls) {
        setState(() {
          _showControls = false;
        });
      }
    });
  }

  void _toggleControls() {
    setState(() {
      _showControls = !_showControls;
    });
    if (_showControls) {
      _startHideTimer();
    }
  }

  void _togglePlayPause() {
    if (_isPlaying) {
      _controller.pause();
    } else {
      _controller.play();
    }
    _startHideTimer();
  }

  void _onSliderChanged(double value) {
    setState(() {
      _sliderValue = value;
      _isDragging = true;
    });
  }

  void _onSliderChangeEnd(double value) {
    final position = Duration(
      milliseconds: (value * _totalDuration.inMilliseconds).round(),
    );
    _controller.seekTo(position);
    setState(() {
      _isDragging = false;
    });
    _startHideTimer();
  }

  void _changeVolume(double delta) {
    final newVolume = (_currentVolume + delta).clamp(0.0, 1.0);
    VolumeController().setVolume(newVolume);
    setState(() {
      _currentVolume = newVolume;
    });
  }

  void _changeBrightness(double delta) {
    final newBrightness = (_currentBrightness + delta).clamp(0.0, 1.0);
    // Note: You'll need to implement brightness control
    setState(() {
      _currentBrightness = newBrightness;
    });
  }

  void _seek(Duration delta) {
    final newPosition = _currentPosition + delta;
    final clampedPosition = Duration(
      milliseconds:
          newPosition.inMilliseconds.clamp(0, _totalDuration.inMilliseconds),
    );
    _controller.seekTo(clampedPosition);
  }

  void _cycleFitMode() {
    setState(() {
      switch (_fitMode) {
        case VideoFitMode.contain:
          _fitMode = VideoFitMode.fill;
          break;
        case VideoFitMode.fill:
          _fitMode = VideoFitMode.fitWidth;
          break;
        case VideoFitMode.fitWidth:
          _fitMode = VideoFitMode.fitHeight;
          break;
        case VideoFitMode.fitHeight:
          _fitMode = VideoFitMode.contain;
          break;
      }
    });
    _startHideTimer();
  }

  void _showSettingsMenu() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.black.withOpacity(0.9),
      builder: (context) => _buildSettingsMenu(),
    );
  }

  void _toggleFullscreen() {
    setState(() {
      _isFullScreen = !_isFullScreen;
    });

    if (_isFullScreen) {
      // Enter fullscreen mode
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]);
    } else {
      // Exit fullscreen mode
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]);
    }
  }

  Widget _buildSettingsMenu() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'إعدادات المشغل',
            style: TextStyle(
              color: Colors.white,
              fontSize: 20.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 20),

          // Playback Speed
          _buildSettingItem(
            'سرعة التشغيل',
            '${_playbackSpeed}x',
            () => _showSpeedMenu(),
          ),

          // Quality
          if (widget.qualities != null && widget.qualities!.isNotEmpty)
            _buildSettingItem(
              'الجودة',
              widget.qualities![_selectedQuality],
              () => _showQualityMenu(),
            ),

          // Audio Track
          if (widget.audioTracks != null && widget.audioTracks!.isNotEmpty)
            _buildSettingItem(
              'المسار الصوتي',
              widget.audioTracks![_selectedAudioTrack],
              () => _showAudioTrackMenu(),
            ),

          // Subtitles
          if (widget.subtitleTracks != null &&
              widget.subtitleTracks!.isNotEmpty)
            _buildSettingItem(
              'الترجمة',
              _selectedSubtitleTrack == -1
                  ? 'مغلقة'
                  : widget.subtitleTracks![_selectedSubtitleTrack],
              () => _showSubtitleMenu(),
            ),

          // Full Screen
          _buildSettingItem(
            'ملء الشاشة',
            'اضغط للتفعيل',
            () {
              Navigator.pop(context);
              _toggleFullscreen();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSettingItem(String title, String value, VoidCallback onTap) {
    return ListTile(
      title: Text(
        title,
        style: const TextStyle(color: Colors.white),
      ),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            value,
            style: TextStyle(color: kColorPrimary),
          ),
          const Icon(
            FontAwesomeIcons.chevronRight,
            color: Colors.white,
            size: 16,
          ),
        ],
      ),
      onTap: onTap,
    );
  }

  void _showSpeedMenu() {
    final speeds = [0.5, 0.75, 1.0, 1.25, 1.5, 2.0];
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.black.withOpacity(0.9),
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: speeds
              .map((speed) => ListTile(
                    title: Text(
                      '${speed}x',
                      style: const TextStyle(color: Colors.white),
                    ),
                    trailing: _playbackSpeed == speed
                        ? Icon(FontAwesomeIcons.check, color: kColorPrimary)
                        : null,
                    onTap: () {
                      setState(() {
                        _playbackSpeed = speed;
                      });
                      _controller.setPlaybackSpeed(speed);
                      Navigator.pop(context);
                      Navigator.pop(context);
                    },
                  ))
              .toList(),
        ),
      ),
    );
  }

  void _showQualityMenu() {
    if (widget.qualities == null) return;
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.black.withOpacity(0.9),
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: widget.qualities!
              .asMap()
              .entries
              .map((entry) => ListTile(
                    title: Text(
                      entry.value,
                      style: const TextStyle(color: Colors.white),
                    ),
                    trailing: _selectedQuality == entry.key
                        ? Icon(FontAwesomeIcons.check, color: kColorPrimary)
                        : null,
                    onTap: () {
                      setState(() {
                        _selectedQuality = entry.key;
                      });
                      // TODO: Implement quality change
                      Navigator.pop(context);
                      Navigator.pop(context);
                    },
                  ))
              .toList(),
        ),
      ),
    );
  }

  void _showAudioTrackMenu() {
    if (widget.audioTracks == null) return;
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.black.withOpacity(0.9),
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: widget.audioTracks!
              .asMap()
              .entries
              .map((entry) => ListTile(
                    title: Text(
                      entry.value,
                      style: const TextStyle(color: Colors.white),
                    ),
                    trailing: _selectedAudioTrack == entry.key
                        ? Icon(FontAwesomeIcons.check, color: kColorPrimary)
                        : null,
                    onTap: () {
                      setState(() {
                        _selectedAudioTrack = entry.key;
                      });
                      // TODO: Implement audio track change
                      Navigator.pop(context);
                      Navigator.pop(context);
                    },
                  ))
              .toList(),
        ),
      ),
    );
  }

  void _showSubtitleMenu() {
    if (widget.subtitleTracks == null) return;
    final subtitles = ['مغلقة', ...widget.subtitleTracks!];
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.black.withOpacity(0.9),
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: subtitles
              .asMap()
              .entries
              .map((entry) => ListTile(
                    title: Text(
                      entry.value,
                      style: const TextStyle(color: Colors.white),
                    ),
                    trailing: _selectedSubtitleTrack == (entry.key - 1)
                        ? Icon(FontAwesomeIcons.check, color: kColorPrimary)
                        : null,
                    onTap: () {
                      setState(() {
                        _selectedSubtitleTrack = entry.key - 1;
                      });
                      // TODO: Implement subtitle change
                      Navigator.pop(context);
                      Navigator.pop(context);
                    },
                  ))
              .toList(),
        ),
      ),
    );
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '${twoDigits(hours)}:${twoDigits(minutes)}:${twoDigits(seconds)}';
    } else {
      return '${twoDigits(minutes)}:${twoDigits(seconds)}';
    }
  }

  @override
  void dispose() {
    _hideTimer?.cancel();
    _bufferUpdateTimer?.cancel();
    _controller.removeListener(_playerListener);
    _controller.dispose();
    WakelockPlus.disable();

    // Restore system UI and orientation
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_isFullScreen) {
      // Full screen mode
      return Scaffold(
        backgroundColor: Colors.black,
        body: GestureDetector(
          onTap: _toggleControls,
          child: Stack(
            children: [
              // Video Player - Full Screen
              SizedBox.expand(
                child: VlcPlayer(
                  controller: _controller,
                  aspectRatio: 16 / 9,
                  placeholder: Container(
                    color: Colors.black,
                    child: const Center(
                      child: CircularProgressIndicator(
                        color: kColorPrimary,
                      ),
                    ),
                  ),
                ),
              ),

              // Full screen controls
              if (_showControls) _buildFullScreenControls(),
            ],
          ),
        ),
      );
    }

    // Normal mode
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Column(
          children: [
            // Video Player Container
            Container(
              width: double.infinity,
              height: 25.h,
              color: Colors.black,
              child: Stack(
                children: [
                  // VLC Player
                  VlcPlayer(
                    controller: _controller,
                    aspectRatio: 16 / 9,
                    placeholder: const Center(
                      child: CircularProgressIndicator(color: kColorPrimary),
                    ),
                  ),

                  // Gesture Detector Overlay
                  Positioned.fill(
                    child: GestureDetector(
                      onTap: _toggleControls,
                      behavior: HitTestBehavior.translucent,
                      onPanStart: (details) {
                        final screenWidth = MediaQuery.of(context).size.width;
                        final x = details.globalPosition.dx;

                        // Determine gesture type based on position
                        if (x < screenWidth * 0.3) {
                          _isBrightnessGesture = true;
                        } else if (x > screenWidth * 0.7) {
                          _isVolumeGesture = true;
                        } else {
                          _isSeekGesture = true;
                        }
                      },
                      onPanUpdate: (details) {
                        final delta =
                            details.delta.dy / 200; // Sensitivity adjustment

                        if (_isBrightnessGesture) {
                          _changeBrightness(-delta);
                        } else if (_isVolumeGesture) {
                          _changeVolume(-delta);
                        } else if (_isSeekGesture) {
                          final seekDelta =
                              Duration(seconds: (-delta * 10).round());
                          _seek(seekDelta);
                        }
                      },
                      onPanEnd: (details) {
                        _isBrightnessGesture = false;
                        _isVolumeGesture = false;
                        _isSeekGesture = false;
                      },
                      child: Container(
                        color: Colors.transparent,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Loading Indicator
            if (_isLoading || _isBuffering)
              const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(
                      color: kColorPrimary,
                    ),
                    SizedBox(height: 16),
                    Text(
                      'جاري تحميل الفيديو...',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),

            // Status Indicator
            if (!_showControls && !_isLoading && !_isBuffering)
              Positioned(
                top: 50,
                right: 20,
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.7),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    'اضغط للتحكم',
                    style: TextStyle(
                      color: Colors.white70,
                      fontSize: 12,
                    ),
                  ),
                ),
              ),

            // Controls Overlay
            if (_showControls)
              Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.black.withOpacity(0.7),
                      Colors.transparent,
                      Colors.transparent,
                      Colors.black.withOpacity(0.7),
                    ],
                  ),
                ),
                child: Stack(
                  children: [
                    // Top Controls
                    Positioned(
                      top: 0,
                      left: 0,
                      right: 0,
                      child: SafeArea(
                        child: _buildTopControls(),
                      ),
                    ),

                    // Center Controls
                    Center(
                      child: _buildCenterControls(),
                    ),

                    // Bottom Controls
                    if (!widget.isLive)
                      Positioned(
                        bottom: 0,
                        left: 0,
                        right: 0,
                        child: SafeArea(
                          child: _buildBottomControls(),
                        ),
                      ),
                  ],
                ),
              ),

            // Volume Indicator
            if (_isVolumeGesture)
              Positioned(
                right: 20,
                top: MediaQuery.of(context).size.height * 0.3,
                child: _buildVolumeIndicator(),
              ),

            // Brightness Indicator
            if (_isBrightnessGesture)
              Positioned(
                left: 20,
                top: MediaQuery.of(context).size.height * 0.3,
                child: _buildBrightnessIndicator(),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopControls() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        children: [
          IconButton(
            onPressed: () => Get.back(),
            icon: const Icon(
              FontAwesomeIcons.chevronLeft,
              color: Colors.white,
              size: 20,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              widget.title,
              style: TextStyle(
                color: Colors.white,
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          IconButton(
            onPressed: _cycleFitMode,
            icon: Icon(
              _fitMode == VideoFitMode.fill
                  ? FontAwesomeIcons.expand
                  : _fitMode == VideoFitMode.fitWidth
                      ? FontAwesomeIcons.arrowsLeftRight
                      : _fitMode == VideoFitMode.fitHeight
                          ? FontAwesomeIcons.arrowsUpDown
                          : FontAwesomeIcons.compress,
              color: Colors.white,
              size: 18,
            ),
          ),
          IconButton(
            onPressed: _showSettingsMenu,
            icon: const Icon(
              FontAwesomeIcons.gear,
              color: Colors.white,
              size: 18,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCenterControls() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // Brightness Control
        if (!isTv(context))
          _buildVerticalSlider(
            value: _currentBrightness,
            onChanged: (value) {
              setState(() {
                _currentBrightness = value;
              });
            },
            icon: _currentBrightness < 0.3
                ? FontAwesomeIcons.moon
                : _currentBrightness < 0.7
                    ? FontAwesomeIcons.sun
                    : FontAwesomeIcons.solidSun,
          ),

        // Previous Track/Rewind
        Container(
          decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.6),
            shape: BoxShape.circle,
            border: Border.all(color: Colors.white.withOpacity(0.3), width: 1),
          ),
          child: IconButton(
            onPressed: () {
              _seek(const Duration(seconds: -30));
            },
            icon: const Icon(
              FontAwesomeIcons.backwardStep,
              color: Colors.white,
              size: 24,
            ),
          ),
        ),

        // Play/Pause Button
        Container(
          decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.7),
            shape: BoxShape.circle,
            border: Border.all(color: kColorPrimary.withOpacity(0.5), width: 2),
          ),
          child: IconButton(
            onPressed: _togglePlayPause,
            icon: Icon(
              _isPlaying ? FontAwesomeIcons.pause : FontAwesomeIcons.play,
              color: Colors.white,
              size: 32,
            ),
          ),
        ),

        // Next Track/Forward
        Container(
          decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.6),
            shape: BoxShape.circle,
            border: Border.all(color: Colors.white.withOpacity(0.3), width: 1),
          ),
          child: IconButton(
            onPressed: () {
              _seek(const Duration(seconds: 30));
            },
            icon: const Icon(
              FontAwesomeIcons.forwardStep,
              color: Colors.white,
              size: 24,
            ),
          ),
        ),

        // Volume Control
        if (!isTv(context))
          _buildVerticalSlider(
            value: _currentVolume,
            onChanged: (value) {
              VolumeController().setVolume(value);
              setState(() {
                _currentVolume = value;
              });
            },
            icon: _currentVolume < 0.1
                ? FontAwesomeIcons.volumeXmark
                : _currentVolume < 0.7
                    ? FontAwesomeIcons.volumeLow
                    : FontAwesomeIcons.volumeHigh,
          ),
      ],
    );
  }

  Widget _buildBottomControls() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          // Additional Controls Row
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              // Previous/Rewind Button
              IconButton(
                onPressed: () {
                  _seek(const Duration(seconds: -10));
                },
                icon: const Icon(
                  FontAwesomeIcons.backward,
                  color: Colors.white,
                  size: 20,
                ),
              ),

              // Speed Control
              GestureDetector(
                onTap: _showSpeedMenu,
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.5),
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: Text(
                    '${_playbackSpeed}x',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),

              // Next/Forward Button
              IconButton(
                onPressed: () {
                  _seek(const Duration(seconds: 10));
                },
                icon: const Icon(
                  FontAwesomeIcons.forward,
                  color: Colors.white,
                  size: 20,
                ),
              ),

              // Fullscreen Toggle
              IconButton(
                onPressed: _cycleFitMode,
                icon: Icon(
                  _fitMode == VideoFitMode.fill
                      ? FontAwesomeIcons.compress
                      : FontAwesomeIcons.expand,
                  color: Colors.white,
                  size: 20,
                ),
              ),

              // Picture in Picture (if supported)
              IconButton(
                onPressed: () {
                  // TODO: Implement PiP
                },
                icon: const Icon(
                  FontAwesomeIcons.clone,
                  color: Colors.white,
                  size: 20,
                ),
              ),
            ],
          ),

          const SizedBox(height: 10),

          // Progress Slider
          Row(
            children: [
              Text(
                _formatDuration(_currentPosition),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                ),
              ),
              Expanded(
                child: _buildProgressBarWithBuffer(),
              ),
              Text(
                _formatDuration(_totalDuration),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildVerticalSlider({
    required double value,
    required ValueChanged<double> onChanged,
    required IconData icon,
  }) {
    return Container(
      height: 120,
      width: 40,
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.5),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            color: Colors.white,
            size: 16,
          ),
          const SizedBox(height: 8),
          Expanded(
            child: RotatedBox(
              quarterTurns: -1,
              child: Slider(
                value: value,
                onChanged: onChanged,
                activeColor: kColorPrimary,
                inactiveColor: Colors.white.withOpacity(0.3),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVolumeIndicator() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.7),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _currentVolume < 0.1
                ? FontAwesomeIcons.volumeXmark
                : _currentVolume < 0.7
                    ? FontAwesomeIcons.volumeLow
                    : FontAwesomeIcons.volumeHigh,
            color: Colors.white,
            size: 24,
          ),
          const SizedBox(height: 8),
          Text(
            '${(_currentVolume * 100).round()}%',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBrightnessIndicator() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.7),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _currentBrightness < 0.3
                ? FontAwesomeIcons.moon
                : _currentBrightness < 0.7
                    ? FontAwesomeIcons.sun
                    : FontAwesomeIcons.solidSun,
            color: Colors.white,
            size: 24,
          ),
          const SizedBox(height: 8),
          Text(
            '${(_currentBrightness * 100).round()}%',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressBarWithBuffer() {
    return SizedBox(
      height: 40,
      child: LayoutBuilder(
        builder: (context, constraints) {
          final trackWidth = constraints.maxWidth;
          final bufferWidth = trackWidth * _bufferProgress;
          final progressWidth = trackWidth * _sliderValue;

          return Stack(
            alignment: Alignment.center,
            children: [
              // Background track
              Container(
                height: 4,
                width: trackWidth,
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // Buffer bar (shows buffered content) - رمادي فاتح
              Positioned(
                left: 0,
                child: Container(
                  height: 4,
                  width: bufferWidth,
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.6), // Buffer color
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),

              // Progress bar (shows current position) - لون أساسي
              Positioned(
                left: 0,
                child: Container(
                  height: 4,
                  width: progressWidth,
                  decoration: BoxDecoration(
                    color: _isPlaying
                        ? kColorPrimary
                        : Colors.orange, // Progress color
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),

              // Interactive slider (invisible track with visible thumb)
              SliderTheme(
                data: SliderTheme.of(context).copyWith(
                  activeTrackColor: Colors.transparent,
                  inactiveTrackColor: Colors.transparent,
                  thumbColor: _isPlaying ? kColorPrimary : Colors.orange,
                  thumbShape:
                      const RoundSliderThumbShape(enabledThumbRadius: 8),
                  overlayShape:
                      const RoundSliderOverlayShape(overlayRadius: 16),
                ),
                child: Slider(
                  value: _sliderValue,
                  onChanged: _onSliderChanged,
                  onChangeEnd: _onSliderChangeEnd,
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  // Full screen controls method
  Widget _buildFullScreenControls() {
    return GestureDetector(
      onTap: _toggleControls,
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.black.withValues(alpha: 0.7),
              Colors.transparent,
              Colors.transparent,
              Colors.black.withValues(alpha: 0.7),
            ],
          ),
        ),
        child: Stack(
          children: [
            // Top Controls
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: SafeArea(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      // Back Button
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.black.withValues(alpha: 0.6),
                          shape: BoxShape.circle,
                        ),
                        child: IconButton(
                          icon: const Icon(FontAwesomeIcons.chevronLeft,
                              color: Colors.white),
                          onPressed: () {
                            setState(() {
                              _isFullScreen = false;
                            });
                            SystemChrome.setEnabledSystemUIMode(
                                SystemUiMode.edgeToEdge);
                            SystemChrome.setPreferredOrientations([
                              DeviceOrientation.portraitUp,
                              DeviceOrientation.portraitDown,
                              DeviceOrientation.landscapeLeft,
                              DeviceOrientation.landscapeRight,
                            ]);
                          },
                        ),
                      ),
                      const SizedBox(width: 16),

                      // Title
                      Expanded(
                        child: Text(
                          widget.title,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),

                      // Live indicator
                      if (widget.isLive)
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.red,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(FontAwesomeIcons.circle,
                                  color: Colors.white, size: 8),
                              SizedBox(width: 4),
                              Text('مباشر',
                                  style: TextStyle(
                                      color: Colors.white, fontSize: 12)),
                            ],
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ),

            // Center Controls
            Center(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Rewind Button
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.6),
                      shape: BoxShape.circle,
                      border: Border.all(
                          color: Colors.white.withValues(alpha: 0.3), width: 1),
                    ),
                    child: IconButton(
                      icon: const Icon(FontAwesomeIcons.backward,
                          color: Colors.white),
                      onPressed: () => _seek(const Duration(seconds: -10)),
                    ),
                  ),

                  const SizedBox(width: 20),

                  // Play/Pause Button
                  Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      color: kColorPrimary.withValues(alpha: 0.9),
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: kColorPrimary.withValues(alpha: 0.3),
                          blurRadius: 15,
                          spreadRadius: 3,
                        ),
                      ],
                    ),
                    child: IconButton(
                      icon: Icon(
                        _isPlaying
                            ? FontAwesomeIcons.pause
                            : FontAwesomeIcons.play,
                        color: Colors.white,
                        size: 32,
                      ),
                      onPressed: _togglePlayPause,
                    ),
                  ),

                  const SizedBox(width: 20),

                  // Forward Button
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.6),
                      shape: BoxShape.circle,
                      border: Border.all(
                          color: Colors.white.withValues(alpha: 0.3), width: 1),
                    ),
                    child: IconButton(
                      icon: const Icon(FontAwesomeIcons.forward,
                          color: Colors.white),
                      onPressed: () => _seek(const Duration(seconds: 10)),
                    ),
                  ),
                ],
              ),
            ),

            // Bottom Controls
            if (!widget.isLive)
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: SafeArea(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      children: [
                        Text(
                          _formatDuration(_currentPosition),
                          style: const TextStyle(
                              color: Colors.white, fontSize: 12),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: _buildProgressBarWithBuffer(),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          _formatDuration(_totalDuration),
                          style: const TextStyle(
                              color: Colors.white, fontSize: 12),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}

// Smarters Full Screen Video Player Widget
class _SmartersFullScreenPlayer extends StatefulWidget {
  final VlcPlayerController controller;
  final String videoTitle;
  final Duration currentPosition;
  final Duration totalDuration;
  final bool isPlaying;
  final double bufferProgress;
  final bool isLive;

  const _SmartersFullScreenPlayer({
    required this.controller,
    required this.videoTitle,
    required this.currentPosition,
    required this.totalDuration,
    required this.isPlaying,
    required this.bufferProgress,
    required this.isLive,
  });

  @override
  State<_SmartersFullScreenPlayer> createState() =>
      _SmartersFullScreenPlayerState();
}

class _SmartersFullScreenPlayerState extends State<_SmartersFullScreenPlayer> {
  bool _showControls = true;
  Timer? _hideTimer;
  Duration _currentPosition = Duration.zero;
  Duration _totalDuration = Duration.zero;
  double _sliderValue = 0.0;
  bool _isDragging = false;
  bool _isPlaying = false;

  @override
  void initState() {
    super.initState();

    // Set landscape orientation and hide system UI
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);

    // Initialize values
    _currentPosition = widget.currentPosition;
    _totalDuration = widget.totalDuration;
    _isPlaying = widget.isPlaying;

    if (_totalDuration.inMilliseconds > 0) {
      _sliderValue =
          _currentPosition.inMilliseconds / _totalDuration.inMilliseconds;
    }

    // Add listener to controller
    widget.controller.addListener(_playerListener);

    // Start hide timer
    _startHideTimer();
  }

  @override
  void dispose() {
    _hideTimer?.cancel();
    widget.controller.removeListener(_playerListener);

    // Restore system UI and orientation
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);

    super.dispose();
  }

  void _playerListener() {
    if (!mounted) return;

    final value = widget.controller.value;

    setState(() {
      _isPlaying = value.isPlaying;

      if (value.isInitialized && !_isDragging) {
        _currentPosition = value.position;
        _totalDuration = value.duration;

        if (_totalDuration.inMilliseconds > 0) {
          _sliderValue =
              _currentPosition.inMilliseconds / _totalDuration.inMilliseconds;
        }
      }
    });
  }

  void _startHideTimer() {
    _hideTimer?.cancel();
    _hideTimer = Timer(const Duration(seconds: 5), () {
      if (mounted && _showControls) {
        setState(() {
          _showControls = false;
        });
      }
    });
  }

  void _toggleControls() {
    setState(() {
      _showControls = !_showControls;
    });
    if (_showControls) {
      _startHideTimer();
    }
  }

  void _togglePlayPause() {
    if (_isPlaying) {
      widget.controller.pause();
    } else {
      widget.controller.play();
    }
    _startHideTimer();
  }

  void _onSliderChanged(double value) {
    setState(() {
      _sliderValue = value;
      _isDragging = true;
    });
  }

  void _onSliderChangeEnd(double value) {
    final position = Duration(
      milliseconds: (value * _totalDuration.inMilliseconds).round(),
    );
    widget.controller.seekTo(position);
    setState(() {
      _isDragging = false;
    });
    _startHideTimer();
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = twoDigits(duration.inHours);
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));

    if (duration.inHours > 0) {
      return '$hours:$minutes:$seconds';
    }
    return '$minutes:$seconds';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: GestureDetector(
        onTap: _toggleControls,
        child: Stack(
          children: [
            // Video Player - Full Screen
            SizedBox.expand(
              child: VlcPlayer(
                controller: widget.controller,
                aspectRatio: 16 / 9,
                placeholder: const Center(
                  child: CircularProgressIndicator(color: kColorPrimary),
                ),
              ),
            ),

            // Controls Overlay
            if (_showControls)
              Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.black.withValues(alpha: 0.7),
                      Colors.transparent,
                      Colors.transparent,
                      Colors.black.withValues(alpha: 0.7),
                    ],
                  ),
                ),
              ),

            // Top Controls
            if (_showControls)
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                child: SafeArea(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      children: [
                        // Back Button
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.black.withValues(alpha: 0.6),
                            shape: BoxShape.circle,
                          ),
                          child: IconButton(
                            icon: const Icon(FontAwesomeIcons.chevronLeft,
                                color: Colors.white),
                            onPressed: () {
                              Navigator.pop(context, _currentPosition);
                            },
                          ),
                        ),
                        const SizedBox(width: 16),

                        // Title
                        Expanded(
                          child: Text(
                            widget.videoTitle,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),

                        // Live indicator
                        if (widget.isLive)
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.red,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: const Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(FontAwesomeIcons.circle,
                                    color: Colors.white, size: 8),
                                SizedBox(width: 4),
                                Text('مباشر',
                                    style: TextStyle(
                                        color: Colors.white, fontSize: 12)),
                              ],
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              ),

            // Center Controls
            if (_showControls)
              Center(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Rewind Button
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.6),
                        shape: BoxShape.circle,
                        border: Border.all(
                            color: Colors.white.withValues(alpha: 0.3),
                            width: 1),
                      ),
                      child: IconButton(
                        icon: const Icon(FontAwesomeIcons.backward,
                            color: Colors.white),
                        onPressed: () {
                          final newPosition =
                              _currentPosition - const Duration(seconds: 10);
                          widget.controller.seekTo(newPosition);
                          _startHideTimer();
                        },
                      ),
                    ),

                    const SizedBox(width: 20),

                    // Play/Pause Button
                    Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        color: kColorPrimary.withValues(alpha: 0.9),
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: kColorPrimary.withValues(alpha: 0.3),
                            blurRadius: 15,
                            spreadRadius: 3,
                          ),
                        ],
                      ),
                      child: IconButton(
                        icon: Icon(
                          _isPlaying
                              ? FontAwesomeIcons.pause
                              : FontAwesomeIcons.play,
                          color: Colors.white,
                          size: 32,
                        ),
                        onPressed: _togglePlayPause,
                      ),
                    ),

                    const SizedBox(width: 20),

                    // Forward Button
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.6),
                        shape: BoxShape.circle,
                        border: Border.all(
                            color: Colors.white.withValues(alpha: 0.3),
                            width: 1),
                      ),
                      child: IconButton(
                        icon: const Icon(FontAwesomeIcons.forward,
                            color: Colors.white),
                        onPressed: () {
                          final newPosition =
                              _currentPosition + const Duration(seconds: 10);
                          widget.controller.seekTo(newPosition);
                          _startHideTimer();
                        },
                      ),
                    ),
                  ],
                ),
              ),

            // Bottom Controls
            if (_showControls)
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: SafeArea(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Progress Bar with Buffer
                        Row(
                          children: [
                            Text(
                              _formatDuration(_currentPosition),
                              style: const TextStyle(
                                  color: Colors.white, fontSize: 12),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Stack(
                                children: [
                                  // Background track
                                  Container(
                                    height: 4,
                                    decoration: BoxDecoration(
                                      color:
                                          Colors.white.withValues(alpha: 0.3),
                                      borderRadius: BorderRadius.circular(2),
                                    ),
                                  ),

                                  // Buffer bar
                                  FractionallySizedBox(
                                    widthFactor:
                                        widget.bufferProgress.clamp(0.0, 1.0),
                                    child: Container(
                                      height: 4,
                                      decoration: BoxDecoration(
                                        color:
                                            Colors.white.withValues(alpha: 0.6),
                                        borderRadius: BorderRadius.circular(2),
                                      ),
                                    ),
                                  ),

                                  // Progress slider
                                  SliderTheme(
                                    data: SliderTheme.of(context).copyWith(
                                      activeTrackColor: kColorPrimary,
                                      inactiveTrackColor: Colors.transparent,
                                      thumbColor: kColorPrimary,
                                      overlayColor:
                                          kColorPrimary.withValues(alpha: 0.2),
                                      thumbShape: const RoundSliderThumbShape(
                                          enabledThumbRadius: 8),
                                      trackHeight: 4,
                                    ),
                                    child: Slider(
                                      value: _sliderValue.clamp(0.0, 1.0),
                                      onChanged: _onSliderChanged,
                                      onChangeEnd: _onSliderChangeEnd,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              _formatDuration(_totalDuration),
                              style: const TextStyle(
                                  color: Colors.white, fontSize: 12),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
