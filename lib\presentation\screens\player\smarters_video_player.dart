import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_vlc_player/flutter_vlc_player.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:wakelock_plus/wakelock_plus.dart';
import 'package:volume_controller/volume_controller.dart';
import '../../../helpers/helpers.dart';

enum VideoFitMode {
  fill,
  fitWidth,
  fitHeight,
  contain,
}

class SmartersVideoPlayer extends StatefulWidget {
  final String videoUrl;
  final String title;
  final bool isLive;
  final List<String>? qualities;
  final List<String>? audioTracks;
  final List<String>? subtitleTracks;

  const SmartersVideoPlayer({
    Key? key,
    required this.videoUrl,
    required this.title,
    this.isLive = false,
    this.qualities,
    this.audioTracks,
    this.subtitleTracks,
  }) : super(key: key);

  @override
  State<SmartersVideoPlayer> createState() => _SmartersVideoPlayerState();
}

class _SmartersVideoPlayerState extends State<SmartersVideoPlayer> {
  late VlcPlayerController _controller;
  bool _showControls = true;
  bool _isPlaying = false;
  bool _isLoading = true;
  bool _isBuffering = false;

  // Progress tracking
  Duration _currentPosition = Duration.zero;
  Duration _totalDuration = Duration.zero;
  double _sliderValue = 0.0;
  bool _isDragging = false;

  // Volume & Brightness
  double _currentVolume = 0.5;
  double _currentBrightness = 0.5;

  // Video fit mode
  VideoFitMode _fitMode = VideoFitMode.contain;

  // Subtitles & Audio tracks
  int _selectedSubtitleTrack = -1;
  int _selectedAudioTrack = 0;
  int _selectedQuality = 0;
  double _playbackSpeed = 1.0;

  // Auto-hide timer
  Timer? _hideTimer;

  // Gesture detection
  bool _isVolumeGesture = false;
  bool _isBrightnessGesture = false;
  bool _isSeekGesture = false;

  @override
  void initState() {
    super.initState();
    _initializePlayer();
    _initializeVolume();
    WakelockPlus.enable();
    _startHideTimer();
  }

  void _initializePlayer() {
    _controller = VlcPlayerController.network(
      widget.videoUrl,
      hwAcc: HwAcc.full,
      autoPlay: true,
      autoInitialize: true,
      options: VlcPlayerOptions(
        advanced: VlcAdvancedOptions([
          VlcAdvancedOptions.networkCaching(2000),
          VlcAdvancedOptions.clockJitter(0),
        ]),
        video: VlcVideoOptions([
          VlcVideoOptions.dropLateFrames(true),
          VlcVideoOptions.skipFrames(true),
        ]),
        audio: VlcAudioOptions([
          VlcAudioOptions.audioTimeStretch(true),
        ]),
        rtp: VlcRtpOptions([
          VlcRtpOptions.rtpOverRtsp(true),
        ]),
      ),
    );

    _controller.addListener(_playerListener);
  }

  void _initializeVolume() async {
    try {
      _currentVolume = await VolumeController().getVolume();
      setState(() {});
    } catch (e) {
      debugPrint('Error getting volume: $e');
    }
  }

  void _playerListener() {
    if (!mounted) return;

    final value = _controller.value;

    setState(() {
      _isPlaying = value.isPlaying;
      _isLoading = !value.isInitialized;
      _isBuffering = value.isBuffering;

      if (value.isInitialized && !_isDragging) {
        _currentPosition = value.position;
        _totalDuration = value.duration;

        if (_totalDuration.inMilliseconds > 0) {
          _sliderValue =
              _currentPosition.inMilliseconds / _totalDuration.inMilliseconds;
        }
      }
    });
  }

  void _startHideTimer() {
    _hideTimer?.cancel();
    _hideTimer = Timer(const Duration(seconds: 5), () {
      if (mounted && _showControls) {
        setState(() {
          _showControls = false;
        });
      }
    });
  }

  void _toggleControls() {
    setState(() {
      _showControls = !_showControls;
    });
    if (_showControls) {
      _startHideTimer();
    }
  }

  void _togglePlayPause() {
    if (_isPlaying) {
      _controller.pause();
    } else {
      _controller.play();
    }
    _startHideTimer();
  }

  void _onSliderChanged(double value) {
    setState(() {
      _sliderValue = value;
      _isDragging = true;
    });
  }

  void _onSliderChangeEnd(double value) {
    final position = Duration(
      milliseconds: (value * _totalDuration.inMilliseconds).round(),
    );
    _controller.seekTo(position);
    setState(() {
      _isDragging = false;
    });
    _startHideTimer();
  }

  void _changeVolume(double delta) {
    final newVolume = (_currentVolume + delta).clamp(0.0, 1.0);
    VolumeController().setVolume(newVolume);
    setState(() {
      _currentVolume = newVolume;
    });
  }

  void _changeBrightness(double delta) {
    final newBrightness = (_currentBrightness + delta).clamp(0.0, 1.0);
    // Note: You'll need to implement brightness control
    setState(() {
      _currentBrightness = newBrightness;
    });
  }

  void _seek(Duration delta) {
    final newPosition = _currentPosition + delta;
    final clampedPosition = Duration(
      milliseconds:
          newPosition.inMilliseconds.clamp(0, _totalDuration.inMilliseconds),
    );
    _controller.seekTo(clampedPosition);
  }

  void _cycleFitMode() {
    setState(() {
      switch (_fitMode) {
        case VideoFitMode.contain:
          _fitMode = VideoFitMode.fill;
          break;
        case VideoFitMode.fill:
          _fitMode = VideoFitMode.fitWidth;
          break;
        case VideoFitMode.fitWidth:
          _fitMode = VideoFitMode.fitHeight;
          break;
        case VideoFitMode.fitHeight:
          _fitMode = VideoFitMode.contain;
          break;
      }
    });
    _startHideTimer();
  }

  void _showSettingsMenu() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.black.withOpacity(0.9),
      builder: (context) => _buildSettingsMenu(),
    );
  }

  Widget _buildSettingsMenu() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'إعدادات المشغل',
            style: TextStyle(
              color: Colors.white,
              fontSize: 20.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 20),

          // Playback Speed
          _buildSettingItem(
            'سرعة التشغيل',
            '${_playbackSpeed}x',
            () => _showSpeedMenu(),
          ),

          // Quality
          if (widget.qualities != null && widget.qualities!.isNotEmpty)
            _buildSettingItem(
              'الجودة',
              widget.qualities![_selectedQuality],
              () => _showQualityMenu(),
            ),

          // Audio Track
          if (widget.audioTracks != null && widget.audioTracks!.isNotEmpty)
            _buildSettingItem(
              'المسار الصوتي',
              widget.audioTracks![_selectedAudioTrack],
              () => _showAudioTrackMenu(),
            ),

          // Subtitles
          if (widget.subtitleTracks != null &&
              widget.subtitleTracks!.isNotEmpty)
            _buildSettingItem(
              'الترجمة',
              _selectedSubtitleTrack == -1
                  ? 'مغلقة'
                  : widget.subtitleTracks![_selectedSubtitleTrack],
              () => _showSubtitleMenu(),
            ),
        ],
      ),
    );
  }

  Widget _buildSettingItem(String title, String value, VoidCallback onTap) {
    return ListTile(
      title: Text(
        title,
        style: const TextStyle(color: Colors.white),
      ),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            value,
            style: TextStyle(color: kColorPrimary),
          ),
          const Icon(
            FontAwesomeIcons.chevronRight,
            color: Colors.white,
            size: 16,
          ),
        ],
      ),
      onTap: onTap,
    );
  }

  void _showSpeedMenu() {
    final speeds = [0.5, 0.75, 1.0, 1.25, 1.5, 2.0];
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.black.withOpacity(0.9),
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: speeds
              .map((speed) => ListTile(
                    title: Text(
                      '${speed}x',
                      style: const TextStyle(color: Colors.white),
                    ),
                    trailing: _playbackSpeed == speed
                        ? Icon(FontAwesomeIcons.check, color: kColorPrimary)
                        : null,
                    onTap: () {
                      setState(() {
                        _playbackSpeed = speed;
                      });
                      _controller.setPlaybackSpeed(speed);
                      Navigator.pop(context);
                      Navigator.pop(context);
                    },
                  ))
              .toList(),
        ),
      ),
    );
  }

  void _showQualityMenu() {
    if (widget.qualities == null) return;
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.black.withOpacity(0.9),
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: widget.qualities!
              .asMap()
              .entries
              .map((entry) => ListTile(
                    title: Text(
                      entry.value,
                      style: const TextStyle(color: Colors.white),
                    ),
                    trailing: _selectedQuality == entry.key
                        ? Icon(FontAwesomeIcons.check, color: kColorPrimary)
                        : null,
                    onTap: () {
                      setState(() {
                        _selectedQuality = entry.key;
                      });
                      // TODO: Implement quality change
                      Navigator.pop(context);
                      Navigator.pop(context);
                    },
                  ))
              .toList(),
        ),
      ),
    );
  }

  void _showAudioTrackMenu() {
    if (widget.audioTracks == null) return;
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.black.withOpacity(0.9),
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: widget.audioTracks!
              .asMap()
              .entries
              .map((entry) => ListTile(
                    title: Text(
                      entry.value,
                      style: const TextStyle(color: Colors.white),
                    ),
                    trailing: _selectedAudioTrack == entry.key
                        ? Icon(FontAwesomeIcons.check, color: kColorPrimary)
                        : null,
                    onTap: () {
                      setState(() {
                        _selectedAudioTrack = entry.key;
                      });
                      // TODO: Implement audio track change
                      Navigator.pop(context);
                      Navigator.pop(context);
                    },
                  ))
              .toList(),
        ),
      ),
    );
  }

  void _showSubtitleMenu() {
    if (widget.subtitleTracks == null) return;
    final subtitles = ['مغلقة', ...widget.subtitleTracks!];
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.black.withOpacity(0.9),
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: subtitles
              .asMap()
              .entries
              .map((entry) => ListTile(
                    title: Text(
                      entry.value,
                      style: const TextStyle(color: Colors.white),
                    ),
                    trailing: _selectedSubtitleTrack == (entry.key - 1)
                        ? Icon(FontAwesomeIcons.check, color: kColorPrimary)
                        : null,
                    onTap: () {
                      setState(() {
                        _selectedSubtitleTrack = entry.key - 1;
                      });
                      // TODO: Implement subtitle change
                      Navigator.pop(context);
                      Navigator.pop(context);
                    },
                  ))
              .toList(),
        ),
      ),
    );
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '${twoDigits(hours)}:${twoDigits(minutes)}:${twoDigits(seconds)}';
    } else {
      return '${twoDigits(minutes)}:${twoDigits(seconds)}';
    }
  }

  @override
  void dispose() {
    _hideTimer?.cancel();
    _controller.removeListener(_playerListener);
    _controller.dispose();
    WakelockPlus.disable();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: GestureDetector(
        onTap: _toggleControls,
        onPanStart: (details) {
          final screenWidth = MediaQuery.of(context).size.width;
          final x = details.globalPosition.dx;

          // Determine gesture type based on position
          if (x < screenWidth * 0.3) {
            _isBrightnessGesture = true;
          } else if (x > screenWidth * 0.7) {
            _isVolumeGesture = true;
          } else {
            _isSeekGesture = true;
          }
        },
        onPanUpdate: (details) {
          final delta = details.delta.dy / 200; // Sensitivity adjustment

          if (_isBrightnessGesture) {
            _changeBrightness(-delta);
          } else if (_isVolumeGesture) {
            _changeVolume(-delta);
          } else if (_isSeekGesture) {
            final seekDelta = Duration(seconds: (-delta * 10).round());
            _seek(seekDelta);
          }
        },
        onPanEnd: (details) {
          _isBrightnessGesture = false;
          _isVolumeGesture = false;
          _isSeekGesture = false;
        },
        child: Stack(
          children: [
            // Video Player - Full Screen
            Container(
              width: double.infinity,
              height: double.infinity,
              child: VlcPlayer(
                controller: _controller,
                aspectRatio: 16 / 9,
                placeholder: Container(
                  color: Colors.black,
                  child: const Center(
                    child: CircularProgressIndicator(
                      color: kColorPrimary,
                    ),
                  ),
                ),
              ),
            ),

            // Loading Indicator
            if (_isLoading || _isBuffering)
              const Center(
                child: CircularProgressIndicator(
                  color: kColorPrimary,
                ),
              ),

            // Controls Overlay
            if (_showControls)
              Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.black.withOpacity(0.7),
                      Colors.transparent,
                      Colors.transparent,
                      Colors.black.withOpacity(0.7),
                    ],
                  ),
                ),
                child: SafeArea(
                  child: Column(
                    children: [
                      // Top Controls
                      _buildTopControls(),

                      const Spacer(),

                      // Center Controls
                      _buildCenterControls(),

                      const Spacer(),

                      // Bottom Controls
                      if (!widget.isLive) _buildBottomControls(),
                    ],
                  ),
                ),
              ),

            // Volume Indicator
            if (_isVolumeGesture)
              Positioned(
                right: 20,
                top: MediaQuery.of(context).size.height * 0.3,
                child: _buildVolumeIndicator(),
              ),

            // Brightness Indicator
            if (_isBrightnessGesture)
              Positioned(
                left: 20,
                top: MediaQuery.of(context).size.height * 0.3,
                child: _buildBrightnessIndicator(),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopControls() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        children: [
          IconButton(
            onPressed: () => Get.back(),
            icon: const Icon(
              FontAwesomeIcons.chevronLeft,
              color: Colors.white,
              size: 20,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              widget.title,
              style: TextStyle(
                color: Colors.white,
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          IconButton(
            onPressed: _cycleFitMode,
            icon: Icon(
              _fitMode == VideoFitMode.fill
                  ? FontAwesomeIcons.expand
                  : _fitMode == VideoFitMode.fitWidth
                      ? FontAwesomeIcons.arrowsLeftRight
                      : _fitMode == VideoFitMode.fitHeight
                          ? FontAwesomeIcons.arrowsUpDown
                          : FontAwesomeIcons.compress,
              color: Colors.white,
              size: 18,
            ),
          ),
          IconButton(
            onPressed: _showSettingsMenu,
            icon: const Icon(
              FontAwesomeIcons.gear,
              color: Colors.white,
              size: 18,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCenterControls() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // Brightness Control
        if (!isTv(context))
          _buildVerticalSlider(
            value: _currentBrightness,
            onChanged: (value) {
              setState(() {
                _currentBrightness = value;
              });
            },
            icon: _currentBrightness < 0.3
                ? FontAwesomeIcons.moon
                : _currentBrightness < 0.7
                    ? FontAwesomeIcons.sun
                    : FontAwesomeIcons.solidSun,
          ),

        // Previous Track/Rewind
        Container(
          decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.6),
            shape: BoxShape.circle,
            border: Border.all(color: Colors.white.withOpacity(0.3), width: 1),
          ),
          child: IconButton(
            onPressed: () {
              _seek(const Duration(seconds: -30));
            },
            icon: const Icon(
              FontAwesomeIcons.backwardStep,
              color: Colors.white,
              size: 24,
            ),
          ),
        ),

        // Play/Pause Button
        Container(
          decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.7),
            shape: BoxShape.circle,
            border: Border.all(color: kColorPrimary.withOpacity(0.5), width: 2),
          ),
          child: IconButton(
            onPressed: _togglePlayPause,
            icon: Icon(
              _isPlaying ? FontAwesomeIcons.pause : FontAwesomeIcons.play,
              color: Colors.white,
              size: 32,
            ),
          ),
        ),

        // Next Track/Forward
        Container(
          decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.6),
            shape: BoxShape.circle,
            border: Border.all(color: Colors.white.withOpacity(0.3), width: 1),
          ),
          child: IconButton(
            onPressed: () {
              _seek(const Duration(seconds: 30));
            },
            icon: const Icon(
              FontAwesomeIcons.forwardStep,
              color: Colors.white,
              size: 24,
            ),
          ),
        ),

        // Volume Control
        if (!isTv(context))
          _buildVerticalSlider(
            value: _currentVolume,
            onChanged: (value) {
              VolumeController().setVolume(value);
              setState(() {
                _currentVolume = value;
              });
            },
            icon: _currentVolume < 0.1
                ? FontAwesomeIcons.volumeXmark
                : _currentVolume < 0.7
                    ? FontAwesomeIcons.volumeLow
                    : FontAwesomeIcons.volumeHigh,
          ),
      ],
    );
  }

  Widget _buildBottomControls() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          // Additional Controls Row
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              // Previous/Rewind Button
              IconButton(
                onPressed: () {
                  _seek(const Duration(seconds: -10));
                },
                icon: const Icon(
                  FontAwesomeIcons.backward,
                  color: Colors.white,
                  size: 20,
                ),
              ),

              // Speed Control
              GestureDetector(
                onTap: _showSpeedMenu,
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.5),
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: Text(
                    '${_playbackSpeed}x',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),

              // Next/Forward Button
              IconButton(
                onPressed: () {
                  _seek(const Duration(seconds: 10));
                },
                icon: const Icon(
                  FontAwesomeIcons.forward,
                  color: Colors.white,
                  size: 20,
                ),
              ),

              // Fullscreen Toggle
              IconButton(
                onPressed: _cycleFitMode,
                icon: Icon(
                  _fitMode == VideoFitMode.fill
                      ? FontAwesomeIcons.compress
                      : FontAwesomeIcons.expand,
                  color: Colors.white,
                  size: 20,
                ),
              ),

              // Picture in Picture (if supported)
              IconButton(
                onPressed: () {
                  // TODO: Implement PiP
                },
                icon: const Icon(
                  FontAwesomeIcons.clone,
                  color: Colors.white,
                  size: 20,
                ),
              ),
            ],
          ),

          const SizedBox(height: 10),

          // Progress Slider
          Row(
            children: [
              Text(
                _formatDuration(_currentPosition),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                ),
              ),
              Expanded(
                child: Slider(
                  value: _sliderValue,
                  onChanged: _onSliderChanged,
                  onChangeEnd: _onSliderChangeEnd,
                  activeColor: kColorPrimary,
                  inactiveColor: Colors.white.withOpacity(0.3),
                ),
              ),
              Text(
                _formatDuration(_totalDuration),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildVerticalSlider({
    required double value,
    required ValueChanged<double> onChanged,
    required IconData icon,
  }) {
    return Container(
      height: 120,
      width: 40,
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.5),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            color: Colors.white,
            size: 16,
          ),
          const SizedBox(height: 8),
          Expanded(
            child: RotatedBox(
              quarterTurns: -1,
              child: Slider(
                value: value,
                onChanged: onChanged,
                activeColor: kColorPrimary,
                inactiveColor: Colors.white.withOpacity(0.3),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVolumeIndicator() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.7),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _currentVolume < 0.1
                ? FontAwesomeIcons.volumeXmark
                : _currentVolume < 0.7
                    ? FontAwesomeIcons.volumeLow
                    : FontAwesomeIcons.volumeHigh,
            color: Colors.white,
            size: 24,
          ),
          const SizedBox(height: 8),
          Text(
            '${(_currentVolume * 100).round()}%',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBrightnessIndicator() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.7),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _currentBrightness < 0.3
                ? FontAwesomeIcons.moon
                : _currentBrightness < 0.7
                    ? FontAwesomeIcons.sun
                    : FontAwesomeIcons.solidSun,
            color: Colors.white,
            size: 24,
          ),
          const SizedBox(height: 8),
          Text(
            '${(_currentBrightness * 100).round()}%',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}
