.class public final LX5/A0$d;
.super Lc6/q$a;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = LX5/A0;->D(Ljava/lang/Object;LX5/F0;LX5/z0;)Z
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation


# instance fields
.field public final synthetic d:LX5/A0;

.field public final synthetic e:Ljava/lang/Object;


# direct methods
.method public constructor <init>(Lc6/q;LX5/A0;Ljava/lang/Object;)V
    .locals 0

    .line 1
    iput-object p2, p0, LX5/A0$d;->d:LX5/A0;

    .line 2
    .line 3
    iput-object p3, p0, LX5/A0$d;->e:Ljava/lang/Object;

    .line 4
    .line 5
    invoke-direct {p0, p1}, Lc6/q$a;-><init>(Lc6/q;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method


# virtual methods
.method public bridge synthetic d(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Lc6/q;

    .line 2
    .line 3
    invoke-virtual {p0, p1}, LX5/A0$d;->f(Lc6/q;)Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    return-object p1
.end method

.method public f(Lc6/q;)Ljava/lang/Object;
    .locals 1

    .line 1
    iget-object p1, p0, LX5/A0$d;->d:LX5/A0;

    .line 2
    .line 3
    invoke-virtual {p1}, LX5/A0;->e0()Ljava/lang/Object;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    iget-object v0, p0, LX5/A0$d;->e:Ljava/lang/Object;

    .line 8
    .line 9
    if-ne p1, v0, :cond_0

    .line 10
    .line 11
    const/4 p1, 0x0

    .line 12
    goto :goto_0

    .line 13
    :cond_0
    invoke-static {}, Lc6/p;->a()Ljava/lang/Object;

    .line 14
    .line 15
    .line 16
    move-result-object p1

    .line 17
    :goto_0
    return-object p1
.end method
