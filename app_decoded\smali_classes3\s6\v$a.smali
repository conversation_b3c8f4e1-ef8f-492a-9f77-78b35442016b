.class public interface abstract Ls6/v$a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Ls6/v;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x609
    name = "a"
.end annotation


# virtual methods
.method public abstract a()Ls6/z;
.end method

.method public abstract b(Ls6/z;)Ls6/B;
.end method

.method public abstract call()Ls6/e;
.end method
