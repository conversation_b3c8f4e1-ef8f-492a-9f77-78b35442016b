.class public interface abstract LZ5/t;
.super Ljava/lang/Object;
.source "SourceFile"


# virtual methods
.method public abstract c(Ljava/util/concurrent/CancellationException;)V
.end method

.method public abstract g()Ljava/lang/Object;
.end method

.method public abstract i(LD5/d;)Ljava/lang/Object;
.end method

.method public abstract iterator()LZ5/f;
.end method

.method public abstract u(LD5/d;)Ljava/lang/Object;
.end method
