.class public final Ls6/A$a$b;
.super Ls6/A;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Ls6/A$a;->a(LG6/h;Ls6/w;)Ls6/A;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation


# instance fields
.field public final synthetic a:Ls6/w;

.field public final synthetic b:LG6/h;


# direct methods
.method public constructor <init>(Ls6/w;LG6/h;)V
    .locals 0

    .line 1
    iput-object p1, p0, Ls6/A$a$b;->a:Ls6/w;

    .line 2
    .line 3
    iput-object p2, p0, Ls6/A$a$b;->b:LG6/h;

    .line 4
    .line 5
    invoke-direct {p0}, Ls6/A;-><init>()V

    .line 6
    .line 7
    .line 8
    return-void
.end method


# virtual methods
.method public contentLength()J
    .locals 2

    .line 1
    iget-object v0, p0, Ls6/A$a$b;->b:LG6/h;

    .line 2
    .line 3
    invoke-virtual {v0}, LG6/h;->y()I

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    int-to-long v0, v0

    .line 8
    return-wide v0
.end method

.method public contentType()Ls6/w;
    .locals 1

    .line 1
    iget-object v0, p0, Ls6/A$a$b;->a:Ls6/w;

    .line 2
    .line 3
    return-object v0
.end method

.method public writeTo(LG6/f;)V
    .locals 1

    .line 1
    const-string v0, "sink"

    .line 2
    .line 3
    invoke-static {p1, v0}, Lkotlin/jvm/internal/r;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Ls6/A$a$b;->b:LG6/h;

    .line 7
    .line 8
    invoke-interface {p1, v0}, LG6/f;->p0(LG6/h;)LG6/f;

    .line 9
    .line 10
    .line 11
    return-void
.end method
