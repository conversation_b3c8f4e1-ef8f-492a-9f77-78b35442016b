.class public final Lv6/d;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/io/Closeable;
.implements Ljava/io/Flushable;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lv6/d$d;,
        Lv6/d$b;,
        Lv6/d$c;,
        Lv6/d$a;
    }
.end annotation


# static fields
.field public static final A:Ljava/lang/String;

.field public static final B:J

.field public static final C:LV5/j;

.field public static final D:Ljava/lang/String;

.field public static final E:Ljava/lang/String;

.field public static final F:Ljava/lang/String;

.field public static final G:Ljava/lang/String;

.field public static final v:Lv6/d$a;

.field public static final w:Ljava/lang/String;

.field public static final x:Ljava/lang/String;

.field public static final y:Ljava/lang/String;

.field public static final z:Ljava/lang/String;


# instance fields
.field public final a:LB6/a;

.field public final b:Ljava/io/File;

.field public final c:I

.field public final d:I

.field public e:J

.field public final f:Ljava/io/File;

.field public final g:Ljava/io/File;

.field public final h:Ljava/io/File;

.field public i:J

.field public j:LG6/f;

.field public final k:Ljava/util/LinkedHashMap;

.field public l:I

.field public m:Z

.field public n:Z

.field public o:Z

.field public p:Z

.field public q:Z

.field public r:Z

.field public s:J

.field public final t:Lw6/d;

.field public final u:Lv6/d$e;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 1
    new-instance v0, Lv6/d$a;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, v1}, Lv6/d$a;-><init>(Lkotlin/jvm/internal/j;)V

    .line 5
    .line 6
    .line 7
    sput-object v0, Lv6/d;->v:Lv6/d$a;

    .line 8
    .line 9
    const-string v0, "journal"

    .line 10
    .line 11
    sput-object v0, Lv6/d;->w:Ljava/lang/String;

    .line 12
    .line 13
    const-string v0, "journal.tmp"

    .line 14
    .line 15
    sput-object v0, Lv6/d;->x:Ljava/lang/String;

    .line 16
    .line 17
    const-string v0, "journal.bkp"

    .line 18
    .line 19
    sput-object v0, Lv6/d;->y:Ljava/lang/String;

    .line 20
    .line 21
    const-string v0, "libcore.io.DiskLruCache"

    .line 22
    .line 23
    sput-object v0, Lv6/d;->z:Ljava/lang/String;

    .line 24
    .line 25
    const-string v0, "1"

    .line 26
    .line 27
    sput-object v0, Lv6/d;->A:Ljava/lang/String;

    .line 28
    .line 29
    const-wide/16 v0, -0x1

    .line 30
    .line 31
    sput-wide v0, Lv6/d;->B:J

    .line 32
    .line 33
    new-instance v0, LV5/j;

    .line 34
    .line 35
    const-string v1, "[a-z0-9_-]{1,120}"

    .line 36
    .line 37
    invoke-direct {v0, v1}, LV5/j;-><init>(Ljava/lang/String;)V

    .line 38
    .line 39
    .line 40
    sput-object v0, Lv6/d;->C:LV5/j;

    .line 41
    .line 42
    const-string v0, "CLEAN"

    .line 43
    .line 44
    sput-object v0, Lv6/d;->D:Ljava/lang/String;

    .line 45
    .line 46
    const-string v0, "DIRTY"

    .line 47
    .line 48
    sput-object v0, Lv6/d;->E:Ljava/lang/String;

    .line 49
    .line 50
    const-string v0, "REMOVE"

    .line 51
    .line 52
    sput-object v0, Lv6/d;->F:Ljava/lang/String;

    .line 53
    .line 54
    const-string v0, "READ"

    .line 55
    .line 56
    sput-object v0, Lv6/d;->G:Ljava/lang/String;

    .line 57
    .line 58
    return-void
.end method

.method public constructor <init>(LB6/a;Ljava/io/File;IIJLw6/e;)V
    .locals 2

    .line 1
    const-string v0, "fileSystem"

    .line 2
    .line 3
    invoke-static {p1, v0}, Lkotlin/jvm/internal/r;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    const-string v0, "directory"

    .line 7
    .line 8
    invoke-static {p2, v0}, Lkotlin/jvm/internal/r;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 9
    .line 10
    .line 11
    const-string v0, "taskRunner"

    .line 12
    .line 13
    invoke-static {p7, v0}, Lkotlin/jvm/internal/r;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 14
    .line 15
    .line 16
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 17
    .line 18
    .line 19
    iput-object p1, p0, Lv6/d;->a:LB6/a;

    .line 20
    .line 21
    iput-object p2, p0, Lv6/d;->b:Ljava/io/File;

    .line 22
    .line 23
    iput p3, p0, Lv6/d;->c:I

    .line 24
    .line 25
    iput p4, p0, Lv6/d;->d:I

    .line 26
    .line 27
    iput-wide p5, p0, Lv6/d;->e:J

    .line 28
    .line 29
    new-instance p1, Ljava/util/LinkedHashMap;

    .line 30
    .line 31
    const/high16 p3, 0x3f400000    # 0.75f

    .line 32
    .line 33
    const/4 v0, 0x1

    .line 34
    const/4 v1, 0x0

    .line 35
    invoke-direct {p1, v1, p3, v0}, Ljava/util/LinkedHashMap;-><init>(IFZ)V

    .line 36
    .line 37
    .line 38
    iput-object p1, p0, Lv6/d;->k:Ljava/util/LinkedHashMap;

    .line 39
    .line 40
    invoke-virtual {p7}, Lw6/e;->i()Lw6/d;

    .line 41
    .line 42
    .line 43
    move-result-object p1

    .line 44
    iput-object p1, p0, Lv6/d;->t:Lw6/d;

    .line 45
    .line 46
    sget-object p1, Lt6/d;->i:Ljava/lang/String;

    .line 47
    .line 48
    const-string p3, " Cache"

    .line 49
    .line 50
    invoke-static {p1, p3}, Lkotlin/jvm/internal/r;->n(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/String;

    .line 51
    .line 52
    .line 53
    move-result-object p1

    .line 54
    new-instance p3, Lv6/d$e;

    .line 55
    .line 56
    invoke-direct {p3, p0, p1}, Lv6/d$e;-><init>(Lv6/d;Ljava/lang/String;)V

    .line 57
    .line 58
    .line 59
    iput-object p3, p0, Lv6/d;->u:Lv6/d$e;

    .line 60
    .line 61
    const-wide/16 v0, 0x0

    .line 62
    .line 63
    cmp-long p1, p5, v0

    .line 64
    .line 65
    if-lez p1, :cond_1

    .line 66
    .line 67
    if-lez p4, :cond_0

    .line 68
    .line 69
    new-instance p1, Ljava/io/File;

    .line 70
    .line 71
    sget-object p3, Lv6/d;->w:Ljava/lang/String;

    .line 72
    .line 73
    invoke-direct {p1, p2, p3}, Ljava/io/File;-><init>(Ljava/io/File;Ljava/lang/String;)V

    .line 74
    .line 75
    .line 76
    iput-object p1, p0, Lv6/d;->f:Ljava/io/File;

    .line 77
    .line 78
    new-instance p1, Ljava/io/File;

    .line 79
    .line 80
    sget-object p3, Lv6/d;->x:Ljava/lang/String;

    .line 81
    .line 82
    invoke-direct {p1, p2, p3}, Ljava/io/File;-><init>(Ljava/io/File;Ljava/lang/String;)V

    .line 83
    .line 84
    .line 85
    iput-object p1, p0, Lv6/d;->g:Ljava/io/File;

    .line 86
    .line 87
    new-instance p1, Ljava/io/File;

    .line 88
    .line 89
    sget-object p3, Lv6/d;->y:Ljava/lang/String;

    .line 90
    .line 91
    invoke-direct {p1, p2, p3}, Ljava/io/File;-><init>(Ljava/io/File;Ljava/lang/String;)V

    .line 92
    .line 93
    .line 94
    iput-object p1, p0, Lv6/d;->h:Ljava/io/File;

    .line 95
    .line 96
    return-void

    .line 97
    :cond_0
    new-instance p1, Ljava/lang/IllegalArgumentException;

    .line 98
    .line 99
    const-string p2, "valueCount <= 0"

    .line 100
    .line 101
    invoke-virtual {p2}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 102
    .line 103
    .line 104
    move-result-object p2

    .line 105
    invoke-direct {p1, p2}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    .line 106
    .line 107
    .line 108
    throw p1

    .line 109
    :cond_1
    new-instance p1, Ljava/lang/IllegalArgumentException;

    .line 110
    .line 111
    const-string p2, "maxSize <= 0"

    .line 112
    .line 113
    invoke-virtual {p2}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 114
    .line 115
    .line 116
    move-result-object p2

    .line 117
    invoke-direct {p1, p2}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    .line 118
    .line 119
    .line 120
    throw p1
.end method

.method public static synthetic B(Lv6/d;Ljava/lang/String;JILjava/lang/Object;)Lv6/d$b;
    .locals 0

    .line 1
    and-int/lit8 p4, p4, 0x2

    .line 2
    .line 3
    if-eqz p4, :cond_0

    .line 4
    .line 5
    sget-wide p2, Lv6/d;->B:J

    .line 6
    .line 7
    :cond_0
    invoke-virtual {p0, p1, p2, p3}, Lv6/d;->A(Ljava/lang/String;J)Lv6/d$b;

    .line 8
    .line 9
    .line 10
    move-result-object p0

    .line 11
    return-object p0
.end method

.method public static final synthetic a(Lv6/d;)Z
    .locals 0

    .line 1
    iget-boolean p0, p0, Lv6/d;->n:Z

    .line 2
    .line 3
    return p0
.end method

.method public static final synthetic b(Lv6/d;)Z
    .locals 0

    .line 1
    iget-boolean p0, p0, Lv6/d;->o:Z

    .line 2
    .line 3
    return p0
.end method

.method public static final synthetic c(Lv6/d;)Z
    .locals 0

    .line 1
    invoke-virtual {p0}, Lv6/d;->m0()Z

    .line 2
    .line 3
    .line 4
    move-result p0

    .line 5
    return p0
.end method

.method public static final synthetic e(Lv6/d;Z)V
    .locals 0

    .line 1
    iput-boolean p1, p0, Lv6/d;->m:Z

    .line 2
    .line 3
    return-void
.end method

.method public static final synthetic h(Lv6/d;LG6/f;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lv6/d;->j:LG6/f;

    .line 2
    .line 3
    return-void
.end method

.method public static final synthetic j(Lv6/d;Z)V
    .locals 0

    .line 1
    iput-boolean p1, p0, Lv6/d;->r:Z

    .line 2
    .line 3
    return-void
.end method

.method public static final synthetic l(Lv6/d;Z)V
    .locals 0

    .line 1
    iput-boolean p1, p0, Lv6/d;->q:Z

    .line 2
    .line 3
    return-void
.end method

.method public static final synthetic s(Lv6/d;I)V
    .locals 0

    .line 1
    iput p1, p0, Lv6/d;->l:I

    .line 2
    .line 3
    return-void
.end method


# virtual methods
.method public final declared-synchronized A(Ljava/lang/String;J)Lv6/d$b;
    .locals 9

    .line 1
    monitor-enter p0

    .line 2
    :try_start_0
    const-string v0, "key"

    .line 3
    .line 4
    invoke-static {p1, v0}, Lkotlin/jvm/internal/r;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 5
    .line 6
    .line 7
    invoke-virtual {p0}, Lv6/d;->j0()V

    .line 8
    .line 9
    .line 10
    invoke-virtual {p0}, Lv6/d;->t()V

    .line 11
    .line 12
    .line 13
    invoke-virtual {p0, p1}, Lv6/d;->R0(Ljava/lang/String;)V

    .line 14
    .line 15
    .line 16
    iget-object v0, p0, Lv6/d;->k:Ljava/util/LinkedHashMap;

    .line 17
    .line 18
    invoke-virtual {v0, p1}, Ljava/util/LinkedHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    check-cast v0, Lv6/d$c;

    .line 23
    .line 24
    sget-wide v1, Lv6/d;->B:J

    .line 25
    .line 26
    cmp-long v1, p2, v1

    .line 27
    .line 28
    const/4 v2, 0x0

    .line 29
    if-eqz v1, :cond_1

    .line 30
    .line 31
    if-eqz v0, :cond_0

    .line 32
    .line 33
    invoke-virtual {v0}, Lv6/d$c;->h()J

    .line 34
    .line 35
    .line 36
    move-result-wide v3
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 37
    cmp-long p2, v3, p2

    .line 38
    .line 39
    if-eqz p2, :cond_1

    .line 40
    .line 41
    goto :goto_0

    .line 42
    :catchall_0
    move-exception p1

    .line 43
    goto :goto_3

    .line 44
    :cond_0
    :goto_0
    monitor-exit p0

    .line 45
    return-object v2

    .line 46
    :cond_1
    if-nez v0, :cond_2

    .line 47
    .line 48
    move-object p2, v2

    .line 49
    goto :goto_1

    .line 50
    :cond_2
    :try_start_1
    invoke-virtual {v0}, Lv6/d$c;->b()Lv6/d$b;

    .line 51
    .line 52
    .line 53
    move-result-object p2
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 54
    :goto_1
    if-eqz p2, :cond_3

    .line 55
    .line 56
    monitor-exit p0

    .line 57
    return-object v2

    .line 58
    :cond_3
    if-eqz v0, :cond_4

    .line 59
    .line 60
    :try_start_2
    invoke-virtual {v0}, Lv6/d$c;->f()I

    .line 61
    .line 62
    .line 63
    move-result p2
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    .line 64
    if-eqz p2, :cond_4

    .line 65
    .line 66
    monitor-exit p0

    .line 67
    return-object v2

    .line 68
    :cond_4
    :try_start_3
    iget-boolean p2, p0, Lv6/d;->q:Z

    .line 69
    .line 70
    if-nez p2, :cond_8

    .line 71
    .line 72
    iget-boolean p2, p0, Lv6/d;->r:Z

    .line 73
    .line 74
    if-eqz p2, :cond_5

    .line 75
    .line 76
    goto :goto_2

    .line 77
    :cond_5
    iget-object p2, p0, Lv6/d;->j:LG6/f;

    .line 78
    .line 79
    invoke-static {p2}, Lkotlin/jvm/internal/r;->c(Ljava/lang/Object;)V

    .line 80
    .line 81
    .line 82
    sget-object p3, Lv6/d;->E:Ljava/lang/String;

    .line 83
    .line 84
    invoke-interface {p2, p3}, LG6/f;->W(Ljava/lang/String;)LG6/f;

    .line 85
    .line 86
    .line 87
    move-result-object p3

    .line 88
    const/16 v1, 0x20

    .line 89
    .line 90
    invoke-interface {p3, v1}, LG6/f;->F(I)LG6/f;

    .line 91
    .line 92
    .line 93
    move-result-object p3

    .line 94
    invoke-interface {p3, p1}, LG6/f;->W(Ljava/lang/String;)LG6/f;

    .line 95
    .line 96
    .line 97
    move-result-object p3

    .line 98
    const/16 v1, 0xa

    .line 99
    .line 100
    invoke-interface {p3, v1}, LG6/f;->F(I)LG6/f;

    .line 101
    .line 102
    .line 103
    invoke-interface {p2}, LG6/f;->flush()V

    .line 104
    .line 105
    .line 106
    iget-boolean p2, p0, Lv6/d;->m:Z
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_0

    .line 107
    .line 108
    if-eqz p2, :cond_6

    .line 109
    .line 110
    monitor-exit p0

    .line 111
    return-object v2

    .line 112
    :cond_6
    if-nez v0, :cond_7

    .line 113
    .line 114
    :try_start_4
    new-instance v0, Lv6/d$c;

    .line 115
    .line 116
    invoke-direct {v0, p0, p1}, Lv6/d$c;-><init>(Lv6/d;Ljava/lang/String;)V

    .line 117
    .line 118
    .line 119
    iget-object p2, p0, Lv6/d;->k:Ljava/util/LinkedHashMap;

    .line 120
    .line 121
    invoke-interface {p2, p1, v0}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 122
    .line 123
    .line 124
    :cond_7
    new-instance p1, Lv6/d$b;

    .line 125
    .line 126
    invoke-direct {p1, p0, v0}, Lv6/d$b;-><init>(Lv6/d;Lv6/d$c;)V

    .line 127
    .line 128
    .line 129
    invoke-virtual {v0, p1}, Lv6/d$c;->l(Lv6/d$b;)V
    :try_end_4
    .catchall {:try_start_4 .. :try_end_4} :catchall_0

    .line 130
    .line 131
    .line 132
    monitor-exit p0

    .line 133
    return-object p1

    .line 134
    :cond_8
    :goto_2
    :try_start_5
    iget-object v3, p0, Lv6/d;->t:Lw6/d;

    .line 135
    .line 136
    iget-object v4, p0, Lv6/d;->u:Lv6/d$e;

    .line 137
    .line 138
    const/4 v7, 0x2

    .line 139
    const/4 v8, 0x0

    .line 140
    const-wide/16 v5, 0x0

    .line 141
    .line 142
    invoke-static/range {v3 .. v8}, Lw6/d;->j(Lw6/d;Lw6/a;JILjava/lang/Object;)V
    :try_end_5
    .catchall {:try_start_5 .. :try_end_5} :catchall_0

    .line 143
    .line 144
    .line 145
    monitor-exit p0

    .line 146
    return-object v2

    .line 147
    :goto_3
    monitor-exit p0

    .line 148
    throw p1
.end method

.method public final D0(Ljava/lang/String;)V
    .locals 16

    .line 1
    move-object/from16 v0, p0

    .line 2
    .line 3
    move-object/from16 v7, p1

    .line 4
    .line 5
    const/4 v8, 0x0

    .line 6
    const/4 v9, 0x1

    .line 7
    const/4 v5, 0x6

    .line 8
    const/4 v6, 0x0

    .line 9
    const/16 v2, 0x20

    .line 10
    .line 11
    const/4 v3, 0x0

    .line 12
    const/4 v4, 0x0

    .line 13
    move-object/from16 v1, p1

    .line 14
    .line 15
    invoke-static/range {v1 .. v6}, LV5/n;->Z(Ljava/lang/CharSequence;CIZILjava/lang/Object;)I

    .line 16
    .line 17
    .line 18
    move-result v10

    .line 19
    const-string v11, "unexpected journal line: "

    .line 20
    .line 21
    const/4 v12, -0x1

    .line 22
    if-eq v10, v12, :cond_6

    .line 23
    .line 24
    add-int/lit8 v13, v10, 0x1

    .line 25
    .line 26
    const/4 v5, 0x4

    .line 27
    const/4 v6, 0x0

    .line 28
    const/16 v2, 0x20

    .line 29
    .line 30
    const/4 v4, 0x0

    .line 31
    move-object/from16 v1, p1

    .line 32
    .line 33
    move v3, v13

    .line 34
    invoke-static/range {v1 .. v6}, LV5/n;->Z(Ljava/lang/CharSequence;CIZILjava/lang/Object;)I

    .line 35
    .line 36
    .line 37
    move-result v1

    .line 38
    const-string v2, "this as java.lang.String).substring(startIndex)"

    .line 39
    .line 40
    const/4 v3, 0x2

    .line 41
    const/4 v4, 0x0

    .line 42
    if-ne v1, v12, :cond_0

    .line 43
    .line 44
    invoke-virtual {v7, v13}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    .line 45
    .line 46
    .line 47
    move-result-object v5

    .line 48
    invoke-static {v5, v2}, Lkotlin/jvm/internal/r;->e(Ljava/lang/Object;Ljava/lang/String;)V

    .line 49
    .line 50
    .line 51
    sget-object v6, Lv6/d;->F:Ljava/lang/String;

    .line 52
    .line 53
    invoke-virtual {v6}, Ljava/lang/String;->length()I

    .line 54
    .line 55
    .line 56
    move-result v13

    .line 57
    if-ne v10, v13, :cond_1

    .line 58
    .line 59
    invoke-static {v7, v6, v8, v3, v4}, LV5/n;->I(Ljava/lang/String;Ljava/lang/String;ZILjava/lang/Object;)Z

    .line 60
    .line 61
    .line 62
    move-result v6

    .line 63
    if-eqz v6, :cond_1

    .line 64
    .line 65
    iget-object v1, v0, Lv6/d;->k:Ljava/util/LinkedHashMap;

    .line 66
    .line 67
    invoke-virtual {v1, v5}, Ljava/util/AbstractMap;->remove(Ljava/lang/Object;)Ljava/lang/Object;

    .line 68
    .line 69
    .line 70
    return-void

    .line 71
    :cond_0
    invoke-virtual {v7, v13, v1}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    .line 72
    .line 73
    .line 74
    move-result-object v5

    .line 75
    const-string v6, "this as java.lang.String\u2026ing(startIndex, endIndex)"

    .line 76
    .line 77
    invoke-static {v5, v6}, Lkotlin/jvm/internal/r;->e(Ljava/lang/Object;Ljava/lang/String;)V

    .line 78
    .line 79
    .line 80
    :cond_1
    iget-object v6, v0, Lv6/d;->k:Ljava/util/LinkedHashMap;

    .line 81
    .line 82
    invoke-virtual {v6, v5}, Ljava/util/LinkedHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 83
    .line 84
    .line 85
    move-result-object v6

    .line 86
    check-cast v6, Lv6/d$c;

    .line 87
    .line 88
    if-nez v6, :cond_2

    .line 89
    .line 90
    new-instance v6, Lv6/d$c;

    .line 91
    .line 92
    invoke-direct {v6, v0, v5}, Lv6/d$c;-><init>(Lv6/d;Ljava/lang/String;)V

    .line 93
    .line 94
    .line 95
    iget-object v13, v0, Lv6/d;->k:Ljava/util/LinkedHashMap;

    .line 96
    .line 97
    invoke-interface {v13, v5, v6}, Ljava/util/Map;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;

    .line 98
    .line 99
    .line 100
    :cond_2
    if-eq v1, v12, :cond_3

    .line 101
    .line 102
    sget-object v5, Lv6/d;->D:Ljava/lang/String;

    .line 103
    .line 104
    invoke-virtual {v5}, Ljava/lang/String;->length()I

    .line 105
    .line 106
    .line 107
    move-result v13

    .line 108
    if-ne v10, v13, :cond_3

    .line 109
    .line 110
    invoke-static {v7, v5, v8, v3, v4}, LV5/n;->I(Ljava/lang/String;Ljava/lang/String;ZILjava/lang/Object;)Z

    .line 111
    .line 112
    .line 113
    move-result v5

    .line 114
    if-eqz v5, :cond_3

    .line 115
    .line 116
    add-int/2addr v1, v9

    .line 117
    invoke-virtual {v7, v1}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    .line 118
    .line 119
    .line 120
    move-result-object v10

    .line 121
    invoke-static {v10, v2}, Lkotlin/jvm/internal/r;->e(Ljava/lang/Object;Ljava/lang/String;)V

    .line 122
    .line 123
    .line 124
    new-array v11, v9, [C

    .line 125
    .line 126
    const/16 v1, 0x20

    .line 127
    .line 128
    aput-char v1, v11, v8

    .line 129
    .line 130
    const/4 v14, 0x6

    .line 131
    const/4 v15, 0x0

    .line 132
    const/4 v12, 0x0

    .line 133
    const/4 v13, 0x0

    .line 134
    invoke-static/range {v10 .. v15}, LV5/n;->x0(Ljava/lang/CharSequence;[CZIILjava/lang/Object;)Ljava/util/List;

    .line 135
    .line 136
    .line 137
    move-result-object v1

    .line 138
    invoke-virtual {v6, v9}, Lv6/d$c;->o(Z)V

    .line 139
    .line 140
    .line 141
    invoke-virtual {v6, v4}, Lv6/d$c;->l(Lv6/d$b;)V

    .line 142
    .line 143
    .line 144
    invoke-virtual {v6, v1}, Lv6/d$c;->m(Ljava/util/List;)V

    .line 145
    .line 146
    .line 147
    goto :goto_0

    .line 148
    :cond_3
    if-ne v1, v12, :cond_4

    .line 149
    .line 150
    sget-object v2, Lv6/d;->E:Ljava/lang/String;

    .line 151
    .line 152
    invoke-virtual {v2}, Ljava/lang/String;->length()I

    .line 153
    .line 154
    .line 155
    move-result v5

    .line 156
    if-ne v10, v5, :cond_4

    .line 157
    .line 158
    invoke-static {v7, v2, v8, v3, v4}, LV5/n;->I(Ljava/lang/String;Ljava/lang/String;ZILjava/lang/Object;)Z

    .line 159
    .line 160
    .line 161
    move-result v2

    .line 162
    if-eqz v2, :cond_4

    .line 163
    .line 164
    new-instance v1, Lv6/d$b;

    .line 165
    .line 166
    invoke-direct {v1, v0, v6}, Lv6/d$b;-><init>(Lv6/d;Lv6/d$c;)V

    .line 167
    .line 168
    .line 169
    invoke-virtual {v6, v1}, Lv6/d$c;->l(Lv6/d$b;)V

    .line 170
    .line 171
    .line 172
    goto :goto_0

    .line 173
    :cond_4
    if-ne v1, v12, :cond_5

    .line 174
    .line 175
    sget-object v1, Lv6/d;->G:Ljava/lang/String;

    .line 176
    .line 177
    invoke-virtual {v1}, Ljava/lang/String;->length()I

    .line 178
    .line 179
    .line 180
    move-result v2

    .line 181
    if-ne v10, v2, :cond_5

    .line 182
    .line 183
    invoke-static {v7, v1, v8, v3, v4}, LV5/n;->I(Ljava/lang/String;Ljava/lang/String;ZILjava/lang/Object;)Z

    .line 184
    .line 185
    .line 186
    move-result v1

    .line 187
    if-eqz v1, :cond_5

    .line 188
    .line 189
    :goto_0
    return-void

    .line 190
    :cond_5
    new-instance v1, Ljava/io/IOException;

    .line 191
    .line 192
    invoke-static {v11, v7}, Lkotlin/jvm/internal/r;->n(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/String;

    .line 193
    .line 194
    .line 195
    move-result-object v2

    .line 196
    invoke-direct {v1, v2}, Ljava/io/IOException;-><init>(Ljava/lang/String;)V

    .line 197
    .line 198
    .line 199
    throw v1

    .line 200
    :cond_6
    new-instance v1, Ljava/io/IOException;

    .line 201
    .line 202
    invoke-static {v11, v7}, Lkotlin/jvm/internal/r;->n(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/String;

    .line 203
    .line 204
    .line 205
    move-result-object v2

    .line 206
    invoke-direct {v1, v2}, Ljava/io/IOException;-><init>(Ljava/lang/String;)V

    .line 207
    .line 208
    .line 209
    throw v1
.end method

.method public final declared-synchronized E0()V
    .locals 6

    .line 1
    monitor-enter p0

    .line 2
    :try_start_0
    iget-object v0, p0, Lv6/d;->j:LG6/f;

    .line 3
    .line 4
    if-nez v0, :cond_0

    .line 5
    .line 6
    goto :goto_0

    .line 7
    :cond_0
    invoke-interface {v0}, LG6/X;->close()V

    .line 8
    .line 9
    .line 10
    :goto_0
    iget-object v0, p0, Lv6/d;->a:LB6/a;

    .line 11
    .line 12
    iget-object v1, p0, Lv6/d;->g:Ljava/io/File;

    .line 13
    .line 14
    invoke-interface {v0, v1}, LB6/a;->b(Ljava/io/File;)LG6/X;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    invoke-static {v0}, LG6/L;->c(LG6/X;)LG6/f;

    .line 19
    .line 20
    .line 21
    move-result-object v0
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_1

    .line 22
    :try_start_1
    sget-object v1, Lv6/d;->z:Ljava/lang/String;

    .line 23
    .line 24
    invoke-interface {v0, v1}, LG6/f;->W(Ljava/lang/String;)LG6/f;

    .line 25
    .line 26
    .line 27
    move-result-object v1

    .line 28
    const/16 v2, 0xa

    .line 29
    .line 30
    invoke-interface {v1, v2}, LG6/f;->F(I)LG6/f;

    .line 31
    .line 32
    .line 33
    sget-object v1, Lv6/d;->A:Ljava/lang/String;

    .line 34
    .line 35
    invoke-interface {v0, v1}, LG6/f;->W(Ljava/lang/String;)LG6/f;

    .line 36
    .line 37
    .line 38
    move-result-object v1

    .line 39
    invoke-interface {v1, v2}, LG6/f;->F(I)LG6/f;

    .line 40
    .line 41
    .line 42
    iget v1, p0, Lv6/d;->c:I

    .line 43
    .line 44
    int-to-long v3, v1

    .line 45
    invoke-interface {v0, v3, v4}, LG6/f;->J0(J)LG6/f;

    .line 46
    .line 47
    .line 48
    move-result-object v1

    .line 49
    invoke-interface {v1, v2}, LG6/f;->F(I)LG6/f;

    .line 50
    .line 51
    .line 52
    invoke-virtual {p0}, Lv6/d;->i0()I

    .line 53
    .line 54
    .line 55
    move-result v1

    .line 56
    int-to-long v3, v1

    .line 57
    invoke-interface {v0, v3, v4}, LG6/f;->J0(J)LG6/f;

    .line 58
    .line 59
    .line 60
    move-result-object v1

    .line 61
    invoke-interface {v1, v2}, LG6/f;->F(I)LG6/f;

    .line 62
    .line 63
    .line 64
    invoke-interface {v0, v2}, LG6/f;->F(I)LG6/f;

    .line 65
    .line 66
    .line 67
    invoke-virtual {p0}, Lv6/d;->h0()Ljava/util/LinkedHashMap;

    .line 68
    .line 69
    .line 70
    move-result-object v1

    .line 71
    invoke-virtual {v1}, Ljava/util/LinkedHashMap;->values()Ljava/util/Collection;

    .line 72
    .line 73
    .line 74
    move-result-object v1

    .line 75
    invoke-interface {v1}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    .line 76
    .line 77
    .line 78
    move-result-object v1

    .line 79
    :goto_1
    invoke-interface {v1}, Ljava/util/Iterator;->hasNext()Z

    .line 80
    .line 81
    .line 82
    move-result v3

    .line 83
    if-eqz v3, :cond_2

    .line 84
    .line 85
    invoke-interface {v1}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 86
    .line 87
    .line 88
    move-result-object v3

    .line 89
    check-cast v3, Lv6/d$c;

    .line 90
    .line 91
    invoke-virtual {v3}, Lv6/d$c;->b()Lv6/d$b;

    .line 92
    .line 93
    .line 94
    move-result-object v4

    .line 95
    const/16 v5, 0x20

    .line 96
    .line 97
    if-eqz v4, :cond_1

    .line 98
    .line 99
    sget-object v4, Lv6/d;->E:Ljava/lang/String;

    .line 100
    .line 101
    invoke-interface {v0, v4}, LG6/f;->W(Ljava/lang/String;)LG6/f;

    .line 102
    .line 103
    .line 104
    move-result-object v4

    .line 105
    invoke-interface {v4, v5}, LG6/f;->F(I)LG6/f;

    .line 106
    .line 107
    .line 108
    invoke-virtual {v3}, Lv6/d$c;->d()Ljava/lang/String;

    .line 109
    .line 110
    .line 111
    move-result-object v3

    .line 112
    invoke-interface {v0, v3}, LG6/f;->W(Ljava/lang/String;)LG6/f;

    .line 113
    .line 114
    .line 115
    invoke-interface {v0, v2}, LG6/f;->F(I)LG6/f;

    .line 116
    .line 117
    .line 118
    goto :goto_1

    .line 119
    :catchall_0
    move-exception v1

    .line 120
    goto :goto_3

    .line 121
    :cond_1
    sget-object v4, Lv6/d;->D:Ljava/lang/String;

    .line 122
    .line 123
    invoke-interface {v0, v4}, LG6/f;->W(Ljava/lang/String;)LG6/f;

    .line 124
    .line 125
    .line 126
    move-result-object v4

    .line 127
    invoke-interface {v4, v5}, LG6/f;->F(I)LG6/f;

    .line 128
    .line 129
    .line 130
    invoke-virtual {v3}, Lv6/d$c;->d()Ljava/lang/String;

    .line 131
    .line 132
    .line 133
    move-result-object v4

    .line 134
    invoke-interface {v0, v4}, LG6/f;->W(Ljava/lang/String;)LG6/f;

    .line 135
    .line 136
    .line 137
    invoke-virtual {v3, v0}, Lv6/d$c;->s(LG6/f;)V

    .line 138
    .line 139
    .line 140
    invoke-interface {v0, v2}, LG6/f;->F(I)LG6/f;

    .line 141
    .line 142
    .line 143
    goto :goto_1

    .line 144
    :cond_2
    sget-object v1, Ly5/I;->a:Ly5/I;
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 145
    .line 146
    const/4 v1, 0x0

    .line 147
    :try_start_2
    invoke-static {v0, v1}, LK5/b;->a(Ljava/io/Closeable;Ljava/lang/Throwable;)V

    .line 148
    .line 149
    .line 150
    iget-object v0, p0, Lv6/d;->a:LB6/a;

    .line 151
    .line 152
    iget-object v1, p0, Lv6/d;->f:Ljava/io/File;

    .line 153
    .line 154
    invoke-interface {v0, v1}, LB6/a;->d(Ljava/io/File;)Z

    .line 155
    .line 156
    .line 157
    move-result v0

    .line 158
    if-eqz v0, :cond_3

    .line 159
    .line 160
    iget-object v0, p0, Lv6/d;->a:LB6/a;

    .line 161
    .line 162
    iget-object v1, p0, Lv6/d;->f:Ljava/io/File;

    .line 163
    .line 164
    iget-object v2, p0, Lv6/d;->h:Ljava/io/File;

    .line 165
    .line 166
    invoke-interface {v0, v1, v2}, LB6/a;->e(Ljava/io/File;Ljava/io/File;)V

    .line 167
    .line 168
    .line 169
    goto :goto_2

    .line 170
    :catchall_1
    move-exception v0

    .line 171
    goto :goto_4

    .line 172
    :cond_3
    :goto_2
    iget-object v0, p0, Lv6/d;->a:LB6/a;

    .line 173
    .line 174
    iget-object v1, p0, Lv6/d;->g:Ljava/io/File;

    .line 175
    .line 176
    iget-object v2, p0, Lv6/d;->f:Ljava/io/File;

    .line 177
    .line 178
    invoke-interface {v0, v1, v2}, LB6/a;->e(Ljava/io/File;Ljava/io/File;)V

    .line 179
    .line 180
    .line 181
    iget-object v0, p0, Lv6/d;->a:LB6/a;

    .line 182
    .line 183
    iget-object v1, p0, Lv6/d;->h:Ljava/io/File;

    .line 184
    .line 185
    invoke-interface {v0, v1}, LB6/a;->f(Ljava/io/File;)V

    .line 186
    .line 187
    .line 188
    invoke-virtual {p0}, Lv6/d;->q0()LG6/f;

    .line 189
    .line 190
    .line 191
    move-result-object v0

    .line 192
    iput-object v0, p0, Lv6/d;->j:LG6/f;

    .line 193
    .line 194
    const/4 v0, 0x0

    .line 195
    iput-boolean v0, p0, Lv6/d;->m:Z

    .line 196
    .line 197
    iput-boolean v0, p0, Lv6/d;->r:Z
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_1

    .line 198
    .line 199
    monitor-exit p0

    .line 200
    return-void

    .line 201
    :goto_3
    :try_start_3
    throw v1
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_2

    .line 202
    :catchall_2
    move-exception v2

    .line 203
    :try_start_4
    invoke-static {v0, v1}, LK5/b;->a(Ljava/io/Closeable;Ljava/lang/Throwable;)V

    .line 204
    .line 205
    .line 206
    throw v2
    :try_end_4
    .catchall {:try_start_4 .. :try_end_4} :catchall_1

    .line 207
    :goto_4
    monitor-exit p0

    .line 208
    throw v0
.end method

.method public final declared-synchronized I0(Ljava/lang/String;)Z
    .locals 5

    .line 1
    monitor-enter p0

    .line 2
    :try_start_0
    const-string v0, "key"

    .line 3
    .line 4
    invoke-static {p1, v0}, Lkotlin/jvm/internal/r;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 5
    .line 6
    .line 7
    invoke-virtual {p0}, Lv6/d;->j0()V

    .line 8
    .line 9
    .line 10
    invoke-virtual {p0}, Lv6/d;->t()V

    .line 11
    .line 12
    .line 13
    invoke-virtual {p0, p1}, Lv6/d;->R0(Ljava/lang/String;)V

    .line 14
    .line 15
    .line 16
    iget-object v0, p0, Lv6/d;->k:Ljava/util/LinkedHashMap;

    .line 17
    .line 18
    invoke-virtual {v0, p1}, Ljava/util/LinkedHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 19
    .line 20
    .line 21
    move-result-object p1

    .line 22
    check-cast p1, Lv6/d$c;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 23
    .line 24
    const/4 v0, 0x0

    .line 25
    if-nez p1, :cond_0

    .line 26
    .line 27
    monitor-exit p0

    .line 28
    return v0

    .line 29
    :cond_0
    :try_start_1
    invoke-virtual {p0, p1}, Lv6/d;->O0(Lv6/d$c;)Z

    .line 30
    .line 31
    .line 32
    move-result p1

    .line 33
    if-eqz p1, :cond_1

    .line 34
    .line 35
    iget-wide v1, p0, Lv6/d;->i:J

    .line 36
    .line 37
    iget-wide v3, p0, Lv6/d;->e:J

    .line 38
    .line 39
    cmp-long v1, v1, v3

    .line 40
    .line 41
    if-gtz v1, :cond_1

    .line 42
    .line 43
    iput-boolean v0, p0, Lv6/d;->q:Z
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 44
    .line 45
    goto :goto_0

    .line 46
    :catchall_0
    move-exception p1

    .line 47
    goto :goto_1

    .line 48
    :cond_1
    :goto_0
    monitor-exit p0

    .line 49
    return p1

    .line 50
    :goto_1
    monitor-exit p0

    .line 51
    throw p1
.end method

.method public final declared-synchronized K(Ljava/lang/String;)Lv6/d$d;
    .locals 7

    .line 1
    monitor-enter p0

    .line 2
    :try_start_0
    const-string v0, "key"

    .line 3
    .line 4
    invoke-static {p1, v0}, Lkotlin/jvm/internal/r;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 5
    .line 6
    .line 7
    invoke-virtual {p0}, Lv6/d;->j0()V

    .line 8
    .line 9
    .line 10
    invoke-virtual {p0}, Lv6/d;->t()V

    .line 11
    .line 12
    .line 13
    invoke-virtual {p0, p1}, Lv6/d;->R0(Ljava/lang/String;)V

    .line 14
    .line 15
    .line 16
    iget-object v0, p0, Lv6/d;->k:Ljava/util/LinkedHashMap;

    .line 17
    .line 18
    invoke-virtual {v0, p1}, Ljava/util/LinkedHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;

    .line 19
    .line 20
    .line 21
    move-result-object v0

    .line 22
    check-cast v0, Lv6/d$c;
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 23
    .line 24
    const/4 v1, 0x0

    .line 25
    if-nez v0, :cond_0

    .line 26
    .line 27
    monitor-exit p0

    .line 28
    return-object v1

    .line 29
    :cond_0
    :try_start_1
    invoke-virtual {v0}, Lv6/d$c;->r()Lv6/d$d;

    .line 30
    .line 31
    .line 32
    move-result-object v0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 33
    if-nez v0, :cond_1

    .line 34
    .line 35
    monitor-exit p0

    .line 36
    return-object v1

    .line 37
    :cond_1
    :try_start_2
    iget v1, p0, Lv6/d;->l:I

    .line 38
    .line 39
    add-int/lit8 v1, v1, 0x1

    .line 40
    .line 41
    iput v1, p0, Lv6/d;->l:I

    .line 42
    .line 43
    iget-object v1, p0, Lv6/d;->j:LG6/f;

    .line 44
    .line 45
    invoke-static {v1}, Lkotlin/jvm/internal/r;->c(Ljava/lang/Object;)V

    .line 46
    .line 47
    .line 48
    sget-object v2, Lv6/d;->G:Ljava/lang/String;

    .line 49
    .line 50
    invoke-interface {v1, v2}, LG6/f;->W(Ljava/lang/String;)LG6/f;

    .line 51
    .line 52
    .line 53
    move-result-object v1

    .line 54
    const/16 v2, 0x20

    .line 55
    .line 56
    invoke-interface {v1, v2}, LG6/f;->F(I)LG6/f;

    .line 57
    .line 58
    .line 59
    move-result-object v1

    .line 60
    invoke-interface {v1, p1}, LG6/f;->W(Ljava/lang/String;)LG6/f;

    .line 61
    .line 62
    .line 63
    move-result-object p1

    .line 64
    const/16 v1, 0xa

    .line 65
    .line 66
    invoke-interface {p1, v1}, LG6/f;->F(I)LG6/f;

    .line 67
    .line 68
    .line 69
    invoke-virtual {p0}, Lv6/d;->m0()Z

    .line 70
    .line 71
    .line 72
    move-result p1

    .line 73
    if-eqz p1, :cond_2

    .line 74
    .line 75
    iget-object v1, p0, Lv6/d;->t:Lw6/d;

    .line 76
    .line 77
    iget-object v2, p0, Lv6/d;->u:Lv6/d$e;

    .line 78
    .line 79
    const/4 v5, 0x2

    .line 80
    const/4 v6, 0x0

    .line 81
    const-wide/16 v3, 0x0

    .line 82
    .line 83
    invoke-static/range {v1 .. v6}, Lw6/d;->j(Lw6/d;Lw6/a;JILjava/lang/Object;)V
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    .line 84
    .line 85
    .line 86
    goto :goto_0

    .line 87
    :catchall_0
    move-exception p1

    .line 88
    goto :goto_1

    .line 89
    :cond_2
    :goto_0
    monitor-exit p0

    .line 90
    return-object v0

    .line 91
    :goto_1
    monitor-exit p0

    .line 92
    throw p1
.end method

.method public final O0(Lv6/d$c;)Z
    .locals 11

    .line 1
    const-string v0, "entry"

    .line 2
    .line 3
    invoke-static {p1, v0}, Lkotlin/jvm/internal/r;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    iget-boolean v0, p0, Lv6/d;->n:Z

    .line 7
    .line 8
    const/16 v1, 0xa

    .line 9
    .line 10
    const/16 v2, 0x20

    .line 11
    .line 12
    const/4 v3, 0x1

    .line 13
    if-nez v0, :cond_3

    .line 14
    .line 15
    invoke-virtual {p1}, Lv6/d$c;->f()I

    .line 16
    .line 17
    .line 18
    move-result v0

    .line 19
    if-lez v0, :cond_1

    .line 20
    .line 21
    iget-object v0, p0, Lv6/d;->j:LG6/f;

    .line 22
    .line 23
    if-nez v0, :cond_0

    .line 24
    .line 25
    goto :goto_0

    .line 26
    :cond_0
    sget-object v4, Lv6/d;->E:Ljava/lang/String;

    .line 27
    .line 28
    invoke-interface {v0, v4}, LG6/f;->W(Ljava/lang/String;)LG6/f;

    .line 29
    .line 30
    .line 31
    invoke-interface {v0, v2}, LG6/f;->F(I)LG6/f;

    .line 32
    .line 33
    .line 34
    invoke-virtual {p1}, Lv6/d$c;->d()Ljava/lang/String;

    .line 35
    .line 36
    .line 37
    move-result-object v4

    .line 38
    invoke-interface {v0, v4}, LG6/f;->W(Ljava/lang/String;)LG6/f;

    .line 39
    .line 40
    .line 41
    invoke-interface {v0, v1}, LG6/f;->F(I)LG6/f;

    .line 42
    .line 43
    .line 44
    invoke-interface {v0}, LG6/f;->flush()V

    .line 45
    .line 46
    .line 47
    :cond_1
    :goto_0
    invoke-virtual {p1}, Lv6/d$c;->f()I

    .line 48
    .line 49
    .line 50
    move-result v0

    .line 51
    if-gtz v0, :cond_2

    .line 52
    .line 53
    invoke-virtual {p1}, Lv6/d$c;->b()Lv6/d$b;

    .line 54
    .line 55
    .line 56
    move-result-object v0

    .line 57
    if-eqz v0, :cond_3

    .line 58
    .line 59
    :cond_2
    invoke-virtual {p1, v3}, Lv6/d$c;->q(Z)V

    .line 60
    .line 61
    .line 62
    return v3

    .line 63
    :cond_3
    invoke-virtual {p1}, Lv6/d$c;->b()Lv6/d$b;

    .line 64
    .line 65
    .line 66
    move-result-object v0

    .line 67
    if-nez v0, :cond_4

    .line 68
    .line 69
    goto :goto_1

    .line 70
    :cond_4
    invoke-virtual {v0}, Lv6/d$b;->c()V

    .line 71
    .line 72
    .line 73
    :goto_1
    iget v0, p0, Lv6/d;->d:I

    .line 74
    .line 75
    const/4 v4, 0x0

    .line 76
    :goto_2
    if-ge v4, v0, :cond_5

    .line 77
    .line 78
    add-int/lit8 v5, v4, 0x1

    .line 79
    .line 80
    iget-object v6, p0, Lv6/d;->a:LB6/a;

    .line 81
    .line 82
    invoke-virtual {p1}, Lv6/d$c;->a()Ljava/util/List;

    .line 83
    .line 84
    .line 85
    move-result-object v7

    .line 86
    invoke-interface {v7, v4}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 87
    .line 88
    .line 89
    move-result-object v7

    .line 90
    check-cast v7, Ljava/io/File;

    .line 91
    .line 92
    invoke-interface {v6, v7}, LB6/a;->f(Ljava/io/File;)V

    .line 93
    .line 94
    .line 95
    iget-wide v6, p0, Lv6/d;->i:J

    .line 96
    .line 97
    invoke-virtual {p1}, Lv6/d$c;->e()[J

    .line 98
    .line 99
    .line 100
    move-result-object v8

    .line 101
    aget-wide v9, v8, v4

    .line 102
    .line 103
    sub-long/2addr v6, v9

    .line 104
    iput-wide v6, p0, Lv6/d;->i:J

    .line 105
    .line 106
    invoke-virtual {p1}, Lv6/d$c;->e()[J

    .line 107
    .line 108
    .line 109
    move-result-object v6

    .line 110
    const-wide/16 v7, 0x0

    .line 111
    .line 112
    aput-wide v7, v6, v4

    .line 113
    .line 114
    move v4, v5

    .line 115
    goto :goto_2

    .line 116
    :cond_5
    iget v0, p0, Lv6/d;->l:I

    .line 117
    .line 118
    add-int/2addr v0, v3

    .line 119
    iput v0, p0, Lv6/d;->l:I

    .line 120
    .line 121
    iget-object v0, p0, Lv6/d;->j:LG6/f;

    .line 122
    .line 123
    if-nez v0, :cond_6

    .line 124
    .line 125
    goto :goto_3

    .line 126
    :cond_6
    sget-object v4, Lv6/d;->F:Ljava/lang/String;

    .line 127
    .line 128
    invoke-interface {v0, v4}, LG6/f;->W(Ljava/lang/String;)LG6/f;

    .line 129
    .line 130
    .line 131
    invoke-interface {v0, v2}, LG6/f;->F(I)LG6/f;

    .line 132
    .line 133
    .line 134
    invoke-virtual {p1}, Lv6/d$c;->d()Ljava/lang/String;

    .line 135
    .line 136
    .line 137
    move-result-object v2

    .line 138
    invoke-interface {v0, v2}, LG6/f;->W(Ljava/lang/String;)LG6/f;

    .line 139
    .line 140
    .line 141
    invoke-interface {v0, v1}, LG6/f;->F(I)LG6/f;

    .line 142
    .line 143
    .line 144
    :goto_3
    iget-object v0, p0, Lv6/d;->k:Ljava/util/LinkedHashMap;

    .line 145
    .line 146
    invoke-virtual {p1}, Lv6/d$c;->d()Ljava/lang/String;

    .line 147
    .line 148
    .line 149
    move-result-object p1

    .line 150
    invoke-virtual {v0, p1}, Ljava/util/AbstractMap;->remove(Ljava/lang/Object;)Ljava/lang/Object;

    .line 151
    .line 152
    .line 153
    invoke-virtual {p0}, Lv6/d;->m0()Z

    .line 154
    .line 155
    .line 156
    move-result p1

    .line 157
    if-eqz p1, :cond_7

    .line 158
    .line 159
    iget-object v4, p0, Lv6/d;->t:Lw6/d;

    .line 160
    .line 161
    iget-object v5, p0, Lv6/d;->u:Lv6/d$e;

    .line 162
    .line 163
    const/4 v8, 0x2

    .line 164
    const/4 v9, 0x0

    .line 165
    const-wide/16 v6, 0x0

    .line 166
    .line 167
    invoke-static/range {v4 .. v9}, Lw6/d;->j(Lw6/d;Lw6/a;JILjava/lang/Object;)V

    .line 168
    .line 169
    .line 170
    :cond_7
    return v3
.end method

.method public final P()Z
    .locals 1

    .line 1
    iget-boolean v0, p0, Lv6/d;->p:Z

    .line 2
    .line 3
    return v0
.end method

.method public final P0()Z
    .locals 3

    .line 1
    iget-object v0, p0, Lv6/d;->k:Ljava/util/LinkedHashMap;

    .line 2
    .line 3
    invoke-virtual {v0}, Ljava/util/LinkedHashMap;->values()Ljava/util/Collection;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-interface {v0}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    :cond_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 12
    .line 13
    .line 14
    move-result v1

    .line 15
    if-eqz v1, :cond_1

    .line 16
    .line 17
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 18
    .line 19
    .line 20
    move-result-object v1

    .line 21
    check-cast v1, Lv6/d$c;

    .line 22
    .line 23
    invoke-virtual {v1}, Lv6/d$c;->i()Z

    .line 24
    .line 25
    .line 26
    move-result v2

    .line 27
    if-nez v2, :cond_0

    .line 28
    .line 29
    const-string v0, "toEvict"

    .line 30
    .line 31
    invoke-static {v1, v0}, Lkotlin/jvm/internal/r;->e(Ljava/lang/Object;Ljava/lang/String;)V

    .line 32
    .line 33
    .line 34
    invoke-virtual {p0, v1}, Lv6/d;->O0(Lv6/d$c;)Z

    .line 35
    .line 36
    .line 37
    const/4 v0, 0x1

    .line 38
    return v0

    .line 39
    :cond_1
    const/4 v0, 0x0

    .line 40
    return v0
.end method

.method public final Q()Ljava/io/File;
    .locals 1

    .line 1
    iget-object v0, p0, Lv6/d;->b:Ljava/io/File;

    .line 2
    .line 3
    return-object v0
.end method

.method public final Q0()V
    .locals 4

    .line 1
    :cond_0
    iget-wide v0, p0, Lv6/d;->i:J

    .line 2
    .line 3
    iget-wide v2, p0, Lv6/d;->e:J

    .line 4
    .line 5
    cmp-long v0, v0, v2

    .line 6
    .line 7
    if-lez v0, :cond_1

    .line 8
    .line 9
    invoke-virtual {p0}, Lv6/d;->P0()Z

    .line 10
    .line 11
    .line 12
    move-result v0

    .line 13
    if-nez v0, :cond_0

    .line 14
    .line 15
    return-void

    .line 16
    :cond_1
    const/4 v0, 0x0

    .line 17
    iput-boolean v0, p0, Lv6/d;->q:Z

    .line 18
    .line 19
    return-void
.end method

.method public final R0(Ljava/lang/String;)V
    .locals 2

    .line 1
    sget-object v0, Lv6/d;->C:LV5/j;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, LV5/j;->f(Ljava/lang/CharSequence;)Z

    .line 4
    .line 5
    .line 6
    move-result v0

    .line 7
    if-eqz v0, :cond_0

    .line 8
    .line 9
    return-void

    .line 10
    :cond_0
    new-instance v0, Ljava/lang/StringBuilder;

    .line 11
    .line 12
    invoke-direct {v0}, Ljava/lang/StringBuilder;-><init>()V

    .line 13
    .line 14
    .line 15
    const-string v1, "keys must match regex [a-z0-9_-]{1,120}: \""

    .line 16
    .line 17
    invoke-virtual {v0, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 18
    .line 19
    .line 20
    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 21
    .line 22
    .line 23
    const/16 p1, 0x22

    .line 24
    .line 25
    invoke-virtual {v0, p1}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 26
    .line 27
    .line 28
    invoke-virtual {v0}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 29
    .line 30
    .line 31
    move-result-object p1

    .line 32
    new-instance v0, Ljava/lang/IllegalArgumentException;

    .line 33
    .line 34
    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 35
    .line 36
    .line 37
    move-result-object p1

    .line 38
    invoke-direct {v0, p1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    .line 39
    .line 40
    .line 41
    throw v0
.end method

.method public final X()LB6/a;
    .locals 1

    .line 1
    iget-object v0, p0, Lv6/d;->a:LB6/a;

    .line 2
    .line 3
    return-object v0
.end method

.method public declared-synchronized close()V
    .locals 6

    .line 1
    monitor-enter p0

    .line 2
    :try_start_0
    iget-boolean v0, p0, Lv6/d;->o:Z

    .line 3
    .line 4
    const/4 v1, 0x1

    .line 5
    if-eqz v0, :cond_5

    .line 6
    .line 7
    iget-boolean v0, p0, Lv6/d;->p:Z

    .line 8
    .line 9
    if-eqz v0, :cond_0

    .line 10
    .line 11
    goto :goto_1

    .line 12
    :cond_0
    iget-object v0, p0, Lv6/d;->k:Ljava/util/LinkedHashMap;

    .line 13
    .line 14
    invoke-virtual {v0}, Ljava/util/LinkedHashMap;->values()Ljava/util/Collection;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    const-string v2, "lruEntries.values"

    .line 19
    .line 20
    invoke-static {v0, v2}, Lkotlin/jvm/internal/r;->e(Ljava/lang/Object;Ljava/lang/String;)V

    .line 21
    .line 22
    .line 23
    const/4 v2, 0x0

    .line 24
    new-array v3, v2, [Lv6/d$c;

    .line 25
    .line 26
    invoke-interface {v0, v3}, Ljava/util/Collection;->toArray([Ljava/lang/Object;)[Ljava/lang/Object;

    .line 27
    .line 28
    .line 29
    move-result-object v0

    .line 30
    if-eqz v0, :cond_4

    .line 31
    .line 32
    check-cast v0, [Lv6/d$c;

    .line 33
    .line 34
    array-length v3, v0

    .line 35
    :cond_1
    :goto_0
    if-ge v2, v3, :cond_3

    .line 36
    .line 37
    aget-object v4, v0, v2

    .line 38
    .line 39
    add-int/lit8 v2, v2, 0x1

    .line 40
    .line 41
    invoke-virtual {v4}, Lv6/d$c;->b()Lv6/d$b;

    .line 42
    .line 43
    .line 44
    move-result-object v5

    .line 45
    if-eqz v5, :cond_1

    .line 46
    .line 47
    invoke-virtual {v4}, Lv6/d$c;->b()Lv6/d$b;

    .line 48
    .line 49
    .line 50
    move-result-object v4

    .line 51
    if-nez v4, :cond_2

    .line 52
    .line 53
    goto :goto_0

    .line 54
    :cond_2
    invoke-virtual {v4}, Lv6/d$b;->c()V

    .line 55
    .line 56
    .line 57
    goto :goto_0

    .line 58
    :catchall_0
    move-exception v0

    .line 59
    goto :goto_2

    .line 60
    :cond_3
    invoke-virtual {p0}, Lv6/d;->Q0()V

    .line 61
    .line 62
    .line 63
    iget-object v0, p0, Lv6/d;->j:LG6/f;

    .line 64
    .line 65
    invoke-static {v0}, Lkotlin/jvm/internal/r;->c(Ljava/lang/Object;)V

    .line 66
    .line 67
    .line 68
    invoke-interface {v0}, LG6/X;->close()V

    .line 69
    .line 70
    .line 71
    const/4 v0, 0x0

    .line 72
    iput-object v0, p0, Lv6/d;->j:LG6/f;

    .line 73
    .line 74
    iput-boolean v1, p0, Lv6/d;->p:Z
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 75
    .line 76
    monitor-exit p0

    .line 77
    return-void

    .line 78
    :cond_4
    :try_start_1
    new-instance v0, Ljava/lang/NullPointerException;

    .line 79
    .line 80
    const-string v1, "null cannot be cast to non-null type kotlin.Array<T of kotlin.collections.ArraysKt__ArraysJVMKt.toTypedArray>"

    .line 81
    .line 82
    invoke-direct {v0, v1}, Ljava/lang/NullPointerException;-><init>(Ljava/lang/String;)V

    .line 83
    .line 84
    .line 85
    throw v0

    .line 86
    :cond_5
    :goto_1
    iput-boolean v1, p0, Lv6/d;->p:Z
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 87
    .line 88
    monitor-exit p0

    .line 89
    return-void

    .line 90
    :goto_2
    monitor-exit p0

    .line 91
    throw v0
.end method

.method public declared-synchronized flush()V
    .locals 1

    .line 1
    monitor-enter p0

    .line 2
    :try_start_0
    iget-boolean v0, p0, Lv6/d;->o:Z
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 3
    .line 4
    if-nez v0, :cond_0

    .line 5
    .line 6
    monitor-exit p0

    .line 7
    return-void

    .line 8
    :cond_0
    :try_start_1
    invoke-virtual {p0}, Lv6/d;->t()V

    .line 9
    .line 10
    .line 11
    invoke-virtual {p0}, Lv6/d;->Q0()V

    .line 12
    .line 13
    .line 14
    iget-object v0, p0, Lv6/d;->j:LG6/f;

    .line 15
    .line 16
    invoke-static {v0}, Lkotlin/jvm/internal/r;->c(Ljava/lang/Object;)V

    .line 17
    .line 18
    .line 19
    invoke-interface {v0}, LG6/f;->flush()V
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 20
    .line 21
    .line 22
    monitor-exit p0

    .line 23
    return-void

    .line 24
    :catchall_0
    move-exception v0

    .line 25
    monitor-exit p0

    .line 26
    throw v0
.end method

.method public final h0()Ljava/util/LinkedHashMap;
    .locals 1

    .line 1
    iget-object v0, p0, Lv6/d;->k:Ljava/util/LinkedHashMap;

    .line 2
    .line 3
    return-object v0
.end method

.method public final i0()I
    .locals 1

    .line 1
    iget v0, p0, Lv6/d;->d:I

    .line 2
    .line 3
    return v0
.end method

.method public final declared-synchronized j0()V
    .locals 5

    .line 1
    monitor-enter p0

    .line 2
    :try_start_0
    sget-boolean v0, Lt6/d;->h:Z

    .line 3
    .line 4
    if-eqz v0, :cond_1

    .line 5
    .line 6
    invoke-static {p0}, Ljava/lang/Thread;->holdsLock(Ljava/lang/Object;)Z

    .line 7
    .line 8
    .line 9
    move-result v0

    .line 10
    if-eqz v0, :cond_0

    .line 11
    .line 12
    goto :goto_0

    .line 13
    :cond_0
    new-instance v0, Ljava/lang/AssertionError;

    .line 14
    .line 15
    new-instance v1, Ljava/lang/StringBuilder;

    .line 16
    .line 17
    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    .line 18
    .line 19
    .line 20
    const-string v2, "Thread "

    .line 21
    .line 22
    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 23
    .line 24
    .line 25
    invoke-static {}, Ljava/lang/Thread;->currentThread()Ljava/lang/Thread;

    .line 26
    .line 27
    .line 28
    move-result-object v2

    .line 29
    invoke-virtual {v2}, Ljava/lang/Thread;->getName()Ljava/lang/String;

    .line 30
    .line 31
    .line 32
    move-result-object v2

    .line 33
    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 34
    .line 35
    .line 36
    const-string v2, " MUST hold lock on "

    .line 37
    .line 38
    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 39
    .line 40
    .line 41
    invoke-virtual {v1, p0}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 42
    .line 43
    .line 44
    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 45
    .line 46
    .line 47
    move-result-object v1

    .line 48
    invoke-direct {v0, v1}, Ljava/lang/AssertionError;-><init>(Ljava/lang/Object;)V

    .line 49
    .line 50
    .line 51
    throw v0

    .line 52
    :catchall_0
    move-exception v0

    .line 53
    goto/16 :goto_3

    .line 54
    .line 55
    :cond_1
    :goto_0
    iget-boolean v0, p0, Lv6/d;->o:Z
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 56
    .line 57
    if-eqz v0, :cond_2

    .line 58
    .line 59
    monitor-exit p0

    .line 60
    return-void

    .line 61
    :cond_2
    :try_start_1
    iget-object v0, p0, Lv6/d;->a:LB6/a;

    .line 62
    .line 63
    iget-object v1, p0, Lv6/d;->h:Ljava/io/File;

    .line 64
    .line 65
    invoke-interface {v0, v1}, LB6/a;->d(Ljava/io/File;)Z

    .line 66
    .line 67
    .line 68
    move-result v0

    .line 69
    if-eqz v0, :cond_4

    .line 70
    .line 71
    iget-object v0, p0, Lv6/d;->a:LB6/a;

    .line 72
    .line 73
    iget-object v1, p0, Lv6/d;->f:Ljava/io/File;

    .line 74
    .line 75
    invoke-interface {v0, v1}, LB6/a;->d(Ljava/io/File;)Z

    .line 76
    .line 77
    .line 78
    move-result v0

    .line 79
    if-eqz v0, :cond_3

    .line 80
    .line 81
    iget-object v0, p0, Lv6/d;->a:LB6/a;

    .line 82
    .line 83
    iget-object v1, p0, Lv6/d;->h:Ljava/io/File;

    .line 84
    .line 85
    invoke-interface {v0, v1}, LB6/a;->f(Ljava/io/File;)V

    .line 86
    .line 87
    .line 88
    goto :goto_1

    .line 89
    :cond_3
    iget-object v0, p0, Lv6/d;->a:LB6/a;

    .line 90
    .line 91
    iget-object v1, p0, Lv6/d;->h:Ljava/io/File;

    .line 92
    .line 93
    iget-object v2, p0, Lv6/d;->f:Ljava/io/File;

    .line 94
    .line 95
    invoke-interface {v0, v1, v2}, LB6/a;->e(Ljava/io/File;Ljava/io/File;)V

    .line 96
    .line 97
    .line 98
    :cond_4
    :goto_1
    iget-object v0, p0, Lv6/d;->a:LB6/a;

    .line 99
    .line 100
    iget-object v1, p0, Lv6/d;->h:Ljava/io/File;

    .line 101
    .line 102
    invoke-static {v0, v1}, Lt6/d;->F(LB6/a;Ljava/io/File;)Z

    .line 103
    .line 104
    .line 105
    move-result v0

    .line 106
    iput-boolean v0, p0, Lv6/d;->n:Z

    .line 107
    .line 108
    iget-object v0, p0, Lv6/d;->a:LB6/a;

    .line 109
    .line 110
    iget-object v1, p0, Lv6/d;->f:Ljava/io/File;

    .line 111
    .line 112
    invoke-interface {v0, v1}, LB6/a;->d(Ljava/io/File;)Z

    .line 113
    .line 114
    .line 115
    move-result v0
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 116
    const/4 v1, 0x1

    .line 117
    if-eqz v0, :cond_5

    .line 118
    .line 119
    :try_start_2
    invoke-virtual {p0}, Lv6/d;->y0()V

    .line 120
    .line 121
    .line 122
    invoke-virtual {p0}, Lv6/d;->v0()V

    .line 123
    .line 124
    .line 125
    iput-boolean v1, p0, Lv6/d;->o:Z
    :try_end_2
    .catch Ljava/io/IOException; {:try_start_2 .. :try_end_2} :catch_0
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    .line 126
    .line 127
    monitor-exit p0

    .line 128
    return-void

    .line 129
    :catch_0
    move-exception v0

    .line 130
    :try_start_3
    sget-object v2, LC6/j;->a:LC6/j$a;

    .line 131
    .line 132
    invoke-virtual {v2}, LC6/j$a;->g()LC6/j;

    .line 133
    .line 134
    .line 135
    move-result-object v2

    .line 136
    new-instance v3, Ljava/lang/StringBuilder;

    .line 137
    .line 138
    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    .line 139
    .line 140
    .line 141
    const-string v4, "DiskLruCache "

    .line 142
    .line 143
    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 144
    .line 145
    .line 146
    iget-object v4, p0, Lv6/d;->b:Ljava/io/File;

    .line 147
    .line 148
    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 149
    .line 150
    .line 151
    const-string v4, " is corrupt: "

    .line 152
    .line 153
    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 154
    .line 155
    .line 156
    invoke-virtual {v0}, Ljava/lang/Throwable;->getMessage()Ljava/lang/String;

    .line 157
    .line 158
    .line 159
    move-result-object v4

    .line 160
    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/Object;)Ljava/lang/StringBuilder;

    .line 161
    .line 162
    .line 163
    const-string v4, ", removing"

    .line 164
    .line 165
    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 166
    .line 167
    .line 168
    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 169
    .line 170
    .line 171
    move-result-object v3

    .line 172
    const/4 v4, 0x5

    .line 173
    invoke-virtual {v2, v3, v4, v0}, LC6/j;->k(Ljava/lang/String;ILjava/lang/Throwable;)V
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_0

    .line 174
    .line 175
    .line 176
    const/4 v0, 0x0

    .line 177
    :try_start_4
    invoke-virtual {p0}, Lv6/d;->z()V
    :try_end_4
    .catchall {:try_start_4 .. :try_end_4} :catchall_1

    .line 178
    .line 179
    .line 180
    :try_start_5
    iput-boolean v0, p0, Lv6/d;->p:Z

    .line 181
    .line 182
    goto :goto_2

    .line 183
    :catchall_1
    move-exception v1

    .line 184
    iput-boolean v0, p0, Lv6/d;->p:Z

    .line 185
    .line 186
    throw v1

    .line 187
    :cond_5
    :goto_2
    invoke-virtual {p0}, Lv6/d;->E0()V

    .line 188
    .line 189
    .line 190
    iput-boolean v1, p0, Lv6/d;->o:Z
    :try_end_5
    .catchall {:try_start_5 .. :try_end_5} :catchall_0

    .line 191
    .line 192
    monitor-exit p0

    .line 193
    return-void

    .line 194
    :goto_3
    monitor-exit p0

    .line 195
    throw v0
.end method

.method public final m0()Z
    .locals 2

    .line 1
    iget v0, p0, Lv6/d;->l:I

    .line 2
    .line 3
    const/16 v1, 0x7d0

    .line 4
    .line 5
    if-lt v0, v1, :cond_0

    .line 6
    .line 7
    iget-object v1, p0, Lv6/d;->k:Ljava/util/LinkedHashMap;

    .line 8
    .line 9
    invoke-virtual {v1}, Ljava/util/AbstractMap;->size()I

    .line 10
    .line 11
    .line 12
    move-result v1

    .line 13
    if-lt v0, v1, :cond_0

    .line 14
    .line 15
    const/4 v0, 0x1

    .line 16
    goto :goto_0

    .line 17
    :cond_0
    const/4 v0, 0x0

    .line 18
    :goto_0
    return v0
.end method

.method public final q0()LG6/f;
    .locals 3

    .line 1
    iget-object v0, p0, Lv6/d;->a:LB6/a;

    .line 2
    .line 3
    iget-object v1, p0, Lv6/d;->f:Ljava/io/File;

    .line 4
    .line 5
    invoke-interface {v0, v1}, LB6/a;->g(Ljava/io/File;)LG6/X;

    .line 6
    .line 7
    .line 8
    move-result-object v0

    .line 9
    new-instance v1, Lv6/e;

    .line 10
    .line 11
    new-instance v2, Lv6/d$f;

    .line 12
    .line 13
    invoke-direct {v2, p0}, Lv6/d$f;-><init>(Lv6/d;)V

    .line 14
    .line 15
    .line 16
    invoke-direct {v1, v0, v2}, Lv6/e;-><init>(LG6/X;LM5/k;)V

    .line 17
    .line 18
    .line 19
    invoke-static {v1}, LG6/L;->c(LG6/X;)LG6/f;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    return-object v0
.end method

.method public final declared-synchronized t()V
    .locals 2

    .line 1
    monitor-enter p0

    .line 2
    :try_start_0
    iget-boolean v0, p0, Lv6/d;->p:Z
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 3
    .line 4
    xor-int/lit8 v0, v0, 0x1

    .line 5
    .line 6
    if-eqz v0, :cond_0

    .line 7
    .line 8
    monitor-exit p0

    .line 9
    return-void

    .line 10
    :cond_0
    :try_start_1
    const-string v0, "cache is closed"

    .line 11
    .line 12
    new-instance v1, Ljava/lang/IllegalStateException;

    .line 13
    .line 14
    invoke-virtual {v0}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    invoke-direct {v1, v0}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 19
    .line 20
    .line 21
    throw v1
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 22
    :catchall_0
    move-exception v0

    .line 23
    monitor-exit p0

    .line 24
    throw v0
.end method

.method public final declared-synchronized v(Lv6/d$b;Z)V
    .locals 9

    .line 1
    monitor-enter p0

    .line 2
    :try_start_0
    const-string v0, "editor"

    .line 3
    .line 4
    invoke-static {p1, v0}, Lkotlin/jvm/internal/r;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 5
    .line 6
    .line 7
    invoke-virtual {p1}, Lv6/d$b;->d()Lv6/d$c;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-virtual {v0}, Lv6/d$c;->b()Lv6/d$b;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    invoke-static {v1, p1}, Lkotlin/jvm/internal/r;->b(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 16
    .line 17
    .line 18
    move-result v1

    .line 19
    if-eqz v1, :cond_c

    .line 20
    .line 21
    const/4 v1, 0x0

    .line 22
    if-eqz p2, :cond_2

    .line 23
    .line 24
    invoke-virtual {v0}, Lv6/d$c;->g()Z

    .line 25
    .line 26
    .line 27
    move-result v2

    .line 28
    if-nez v2, :cond_2

    .line 29
    .line 30
    iget v2, p0, Lv6/d;->d:I

    .line 31
    .line 32
    move v3, v1

    .line 33
    :goto_0
    if-ge v3, v2, :cond_2

    .line 34
    .line 35
    add-int/lit8 v4, v3, 0x1

    .line 36
    .line 37
    invoke-virtual {p1}, Lv6/d$b;->e()[Z

    .line 38
    .line 39
    .line 40
    move-result-object v5

    .line 41
    invoke-static {v5}, Lkotlin/jvm/internal/r;->c(Ljava/lang/Object;)V

    .line 42
    .line 43
    .line 44
    aget-boolean v5, v5, v3

    .line 45
    .line 46
    if-eqz v5, :cond_1

    .line 47
    .line 48
    iget-object v5, p0, Lv6/d;->a:LB6/a;

    .line 49
    .line 50
    invoke-virtual {v0}, Lv6/d$c;->c()Ljava/util/List;

    .line 51
    .line 52
    .line 53
    move-result-object v6

    .line 54
    invoke-interface {v6, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 55
    .line 56
    .line 57
    move-result-object v3

    .line 58
    check-cast v3, Ljava/io/File;

    .line 59
    .line 60
    invoke-interface {v5, v3}, LB6/a;->d(Ljava/io/File;)Z

    .line 61
    .line 62
    .line 63
    move-result v3

    .line 64
    if-nez v3, :cond_0

    .line 65
    .line 66
    invoke-virtual {p1}, Lv6/d$b;->a()V
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 67
    .line 68
    .line 69
    monitor-exit p0

    .line 70
    return-void

    .line 71
    :catchall_0
    move-exception p1

    .line 72
    goto/16 :goto_5

    .line 73
    .line 74
    :cond_0
    move v3, v4

    .line 75
    goto :goto_0

    .line 76
    :cond_1
    :try_start_1
    invoke-virtual {p1}, Lv6/d$b;->a()V

    .line 77
    .line 78
    .line 79
    new-instance p1, Ljava/lang/IllegalStateException;

    .line 80
    .line 81
    const-string p2, "Newly created entry didn\'t create value for index "

    .line 82
    .line 83
    invoke-static {v3}, Ljava/lang/Integer;->valueOf(I)Ljava/lang/Integer;

    .line 84
    .line 85
    .line 86
    move-result-object v0

    .line 87
    invoke-static {p2, v0}, Lkotlin/jvm/internal/r;->n(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/String;

    .line 88
    .line 89
    .line 90
    move-result-object p2

    .line 91
    invoke-direct {p1, p2}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 92
    .line 93
    .line 94
    throw p1

    .line 95
    :cond_2
    iget p1, p0, Lv6/d;->d:I

    .line 96
    .line 97
    :goto_1
    if-ge v1, p1, :cond_5

    .line 98
    .line 99
    add-int/lit8 v2, v1, 0x1

    .line 100
    .line 101
    invoke-virtual {v0}, Lv6/d$c;->c()Ljava/util/List;

    .line 102
    .line 103
    .line 104
    move-result-object v3

    .line 105
    invoke-interface {v3, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 106
    .line 107
    .line 108
    move-result-object v3

    .line 109
    check-cast v3, Ljava/io/File;

    .line 110
    .line 111
    if-eqz p2, :cond_3

    .line 112
    .line 113
    invoke-virtual {v0}, Lv6/d$c;->i()Z

    .line 114
    .line 115
    .line 116
    move-result v4

    .line 117
    if-nez v4, :cond_3

    .line 118
    .line 119
    iget-object v4, p0, Lv6/d;->a:LB6/a;

    .line 120
    .line 121
    invoke-interface {v4, v3}, LB6/a;->d(Ljava/io/File;)Z

    .line 122
    .line 123
    .line 124
    move-result v4

    .line 125
    if-eqz v4, :cond_4

    .line 126
    .line 127
    invoke-virtual {v0}, Lv6/d$c;->a()Ljava/util/List;

    .line 128
    .line 129
    .line 130
    move-result-object v4

    .line 131
    invoke-interface {v4, v1}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 132
    .line 133
    .line 134
    move-result-object v4

    .line 135
    check-cast v4, Ljava/io/File;

    .line 136
    .line 137
    iget-object v5, p0, Lv6/d;->a:LB6/a;

    .line 138
    .line 139
    invoke-interface {v5, v3, v4}, LB6/a;->e(Ljava/io/File;Ljava/io/File;)V

    .line 140
    .line 141
    .line 142
    invoke-virtual {v0}, Lv6/d$c;->e()[J

    .line 143
    .line 144
    .line 145
    move-result-object v3

    .line 146
    aget-wide v5, v3, v1

    .line 147
    .line 148
    iget-object v3, p0, Lv6/d;->a:LB6/a;

    .line 149
    .line 150
    invoke-interface {v3, v4}, LB6/a;->h(Ljava/io/File;)J

    .line 151
    .line 152
    .line 153
    move-result-wide v3

    .line 154
    invoke-virtual {v0}, Lv6/d$c;->e()[J

    .line 155
    .line 156
    .line 157
    move-result-object v7

    .line 158
    aput-wide v3, v7, v1

    .line 159
    .line 160
    iget-wide v7, p0, Lv6/d;->i:J

    .line 161
    .line 162
    sub-long/2addr v7, v5

    .line 163
    add-long/2addr v7, v3

    .line 164
    iput-wide v7, p0, Lv6/d;->i:J

    .line 165
    .line 166
    goto :goto_2

    .line 167
    :cond_3
    iget-object v1, p0, Lv6/d;->a:LB6/a;

    .line 168
    .line 169
    invoke-interface {v1, v3}, LB6/a;->f(Ljava/io/File;)V

    .line 170
    .line 171
    .line 172
    :cond_4
    :goto_2
    move v1, v2

    .line 173
    goto :goto_1

    .line 174
    :cond_5
    const/4 p1, 0x0

    .line 175
    invoke-virtual {v0, p1}, Lv6/d$c;->l(Lv6/d$b;)V

    .line 176
    .line 177
    .line 178
    invoke-virtual {v0}, Lv6/d$c;->i()Z

    .line 179
    .line 180
    .line 181
    move-result p1

    .line 182
    if-eqz p1, :cond_6

    .line 183
    .line 184
    invoke-virtual {p0, v0}, Lv6/d;->O0(Lv6/d$c;)Z
    :try_end_1
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 185
    .line 186
    .line 187
    monitor-exit p0

    .line 188
    return-void

    .line 189
    :cond_6
    :try_start_2
    iget p1, p0, Lv6/d;->l:I

    .line 190
    .line 191
    const/4 v1, 0x1

    .line 192
    add-int/2addr p1, v1

    .line 193
    iput p1, p0, Lv6/d;->l:I

    .line 194
    .line 195
    iget-object p1, p0, Lv6/d;->j:LG6/f;

    .line 196
    .line 197
    invoke-static {p1}, Lkotlin/jvm/internal/r;->c(Ljava/lang/Object;)V

    .line 198
    .line 199
    .line 200
    invoke-virtual {v0}, Lv6/d$c;->g()Z

    .line 201
    .line 202
    .line 203
    move-result v2

    .line 204
    const/16 v3, 0xa

    .line 205
    .line 206
    const/16 v4, 0x20

    .line 207
    .line 208
    if-nez v2, :cond_8

    .line 209
    .line 210
    if-eqz p2, :cond_7

    .line 211
    .line 212
    goto :goto_3

    .line 213
    :cond_7
    invoke-virtual {p0}, Lv6/d;->h0()Ljava/util/LinkedHashMap;

    .line 214
    .line 215
    .line 216
    move-result-object p2

    .line 217
    invoke-virtual {v0}, Lv6/d$c;->d()Ljava/lang/String;

    .line 218
    .line 219
    .line 220
    move-result-object v1

    .line 221
    invoke-virtual {p2, v1}, Ljava/util/AbstractMap;->remove(Ljava/lang/Object;)Ljava/lang/Object;

    .line 222
    .line 223
    .line 224
    sget-object p2, Lv6/d;->F:Ljava/lang/String;

    .line 225
    .line 226
    invoke-interface {p1, p2}, LG6/f;->W(Ljava/lang/String;)LG6/f;

    .line 227
    .line 228
    .line 229
    move-result-object p2

    .line 230
    invoke-interface {p2, v4}, LG6/f;->F(I)LG6/f;

    .line 231
    .line 232
    .line 233
    invoke-virtual {v0}, Lv6/d$c;->d()Ljava/lang/String;

    .line 234
    .line 235
    .line 236
    move-result-object p2

    .line 237
    invoke-interface {p1, p2}, LG6/f;->W(Ljava/lang/String;)LG6/f;

    .line 238
    .line 239
    .line 240
    invoke-interface {p1, v3}, LG6/f;->F(I)LG6/f;

    .line 241
    .line 242
    .line 243
    goto :goto_4

    .line 244
    :cond_8
    :goto_3
    invoke-virtual {v0, v1}, Lv6/d$c;->o(Z)V

    .line 245
    .line 246
    .line 247
    sget-object v1, Lv6/d;->D:Ljava/lang/String;

    .line 248
    .line 249
    invoke-interface {p1, v1}, LG6/f;->W(Ljava/lang/String;)LG6/f;

    .line 250
    .line 251
    .line 252
    move-result-object v1

    .line 253
    invoke-interface {v1, v4}, LG6/f;->F(I)LG6/f;

    .line 254
    .line 255
    .line 256
    invoke-virtual {v0}, Lv6/d$c;->d()Ljava/lang/String;

    .line 257
    .line 258
    .line 259
    move-result-object v1

    .line 260
    invoke-interface {p1, v1}, LG6/f;->W(Ljava/lang/String;)LG6/f;

    .line 261
    .line 262
    .line 263
    invoke-virtual {v0, p1}, Lv6/d$c;->s(LG6/f;)V

    .line 264
    .line 265
    .line 266
    invoke-interface {p1, v3}, LG6/f;->F(I)LG6/f;

    .line 267
    .line 268
    .line 269
    if-eqz p2, :cond_9

    .line 270
    .line 271
    iget-wide v1, p0, Lv6/d;->s:J

    .line 272
    .line 273
    const-wide/16 v3, 0x1

    .line 274
    .line 275
    add-long/2addr v3, v1

    .line 276
    iput-wide v3, p0, Lv6/d;->s:J

    .line 277
    .line 278
    invoke-virtual {v0, v1, v2}, Lv6/d$c;->p(J)V

    .line 279
    .line 280
    .line 281
    :cond_9
    :goto_4
    invoke-interface {p1}, LG6/f;->flush()V

    .line 282
    .line 283
    .line 284
    iget-wide p1, p0, Lv6/d;->i:J

    .line 285
    .line 286
    iget-wide v0, p0, Lv6/d;->e:J

    .line 287
    .line 288
    cmp-long p1, p1, v0

    .line 289
    .line 290
    if-gtz p1, :cond_a

    .line 291
    .line 292
    invoke-virtual {p0}, Lv6/d;->m0()Z

    .line 293
    .line 294
    .line 295
    move-result p1

    .line 296
    if-eqz p1, :cond_b

    .line 297
    .line 298
    :cond_a
    iget-object v0, p0, Lv6/d;->t:Lw6/d;

    .line 299
    .line 300
    iget-object v1, p0, Lv6/d;->u:Lv6/d$e;

    .line 301
    .line 302
    const/4 v4, 0x2

    .line 303
    const/4 v5, 0x0

    .line 304
    const-wide/16 v2, 0x0

    .line 305
    .line 306
    invoke-static/range {v0 .. v5}, Lw6/d;->j(Lw6/d;Lw6/a;JILjava/lang/Object;)V
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    .line 307
    .line 308
    .line 309
    :cond_b
    monitor-exit p0

    .line 310
    return-void

    .line 311
    :cond_c
    :try_start_3
    const-string p1, "Check failed."

    .line 312
    .line 313
    new-instance p2, Ljava/lang/IllegalStateException;

    .line 314
    .line 315
    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 316
    .line 317
    .line 318
    move-result-object p1

    .line 319
    invoke-direct {p2, p1}, Ljava/lang/IllegalStateException;-><init>(Ljava/lang/String;)V

    .line 320
    .line 321
    .line 322
    throw p2
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_0

    .line 323
    :goto_5
    monitor-exit p0

    .line 324
    throw p1
.end method

.method public final v0()V
    .locals 10

    .line 1
    iget-object v0, p0, Lv6/d;->a:LB6/a;

    .line 2
    .line 3
    iget-object v1, p0, Lv6/d;->g:Ljava/io/File;

    .line 4
    .line 5
    invoke-interface {v0, v1}, LB6/a;->f(Ljava/io/File;)V

    .line 6
    .line 7
    .line 8
    iget-object v0, p0, Lv6/d;->k:Ljava/util/LinkedHashMap;

    .line 9
    .line 10
    invoke-virtual {v0}, Ljava/util/LinkedHashMap;->values()Ljava/util/Collection;

    .line 11
    .line 12
    .line 13
    move-result-object v0

    .line 14
    invoke-interface {v0}, Ljava/util/Collection;->iterator()Ljava/util/Iterator;

    .line 15
    .line 16
    .line 17
    move-result-object v0

    .line 18
    :cond_0
    :goto_0
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 19
    .line 20
    .line 21
    move-result v1

    .line 22
    if-eqz v1, :cond_3

    .line 23
    .line 24
    invoke-interface {v0}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    .line 25
    .line 26
    .line 27
    move-result-object v1

    .line 28
    const-string v2, "i.next()"

    .line 29
    .line 30
    invoke-static {v1, v2}, Lkotlin/jvm/internal/r;->e(Ljava/lang/Object;Ljava/lang/String;)V

    .line 31
    .line 32
    .line 33
    check-cast v1, Lv6/d$c;

    .line 34
    .line 35
    invoke-virtual {v1}, Lv6/d$c;->b()Lv6/d$b;

    .line 36
    .line 37
    .line 38
    move-result-object v2

    .line 39
    const/4 v3, 0x0

    .line 40
    if-nez v2, :cond_1

    .line 41
    .line 42
    iget v2, p0, Lv6/d;->d:I

    .line 43
    .line 44
    :goto_1
    if-ge v3, v2, :cond_0

    .line 45
    .line 46
    add-int/lit8 v4, v3, 0x1

    .line 47
    .line 48
    iget-wide v5, p0, Lv6/d;->i:J

    .line 49
    .line 50
    invoke-virtual {v1}, Lv6/d$c;->e()[J

    .line 51
    .line 52
    .line 53
    move-result-object v7

    .line 54
    aget-wide v8, v7, v3

    .line 55
    .line 56
    add-long/2addr v5, v8

    .line 57
    iput-wide v5, p0, Lv6/d;->i:J

    .line 58
    .line 59
    move v3, v4

    .line 60
    goto :goto_1

    .line 61
    :cond_1
    const/4 v2, 0x0

    .line 62
    invoke-virtual {v1, v2}, Lv6/d$c;->l(Lv6/d$b;)V

    .line 63
    .line 64
    .line 65
    iget v2, p0, Lv6/d;->d:I

    .line 66
    .line 67
    :goto_2
    if-ge v3, v2, :cond_2

    .line 68
    .line 69
    add-int/lit8 v4, v3, 0x1

    .line 70
    .line 71
    iget-object v5, p0, Lv6/d;->a:LB6/a;

    .line 72
    .line 73
    invoke-virtual {v1}, Lv6/d$c;->a()Ljava/util/List;

    .line 74
    .line 75
    .line 76
    move-result-object v6

    .line 77
    invoke-interface {v6, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 78
    .line 79
    .line 80
    move-result-object v6

    .line 81
    check-cast v6, Ljava/io/File;

    .line 82
    .line 83
    invoke-interface {v5, v6}, LB6/a;->f(Ljava/io/File;)V

    .line 84
    .line 85
    .line 86
    iget-object v5, p0, Lv6/d;->a:LB6/a;

    .line 87
    .line 88
    invoke-virtual {v1}, Lv6/d$c;->c()Ljava/util/List;

    .line 89
    .line 90
    .line 91
    move-result-object v6

    .line 92
    invoke-interface {v6, v3}, Ljava/util/List;->get(I)Ljava/lang/Object;

    .line 93
    .line 94
    .line 95
    move-result-object v3

    .line 96
    check-cast v3, Ljava/io/File;

    .line 97
    .line 98
    invoke-interface {v5, v3}, LB6/a;->f(Ljava/io/File;)V

    .line 99
    .line 100
    .line 101
    move v3, v4

    .line 102
    goto :goto_2

    .line 103
    :cond_2
    invoke-interface {v0}, Ljava/util/Iterator;->remove()V

    .line 104
    .line 105
    .line 106
    goto :goto_0

    .line 107
    :cond_3
    return-void
.end method

.method public final y0()V
    .locals 9

    .line 1
    const-string v0, ", "

    .line 2
    .line 3
    iget-object v1, p0, Lv6/d;->a:LB6/a;

    .line 4
    .line 5
    iget-object v2, p0, Lv6/d;->f:Ljava/io/File;

    .line 6
    .line 7
    invoke-interface {v1, v2}, LB6/a;->a(Ljava/io/File;)LG6/Z;

    .line 8
    .line 9
    .line 10
    move-result-object v1

    .line 11
    invoke-static {v1}, LG6/L;->d(LG6/Z;)LG6/g;

    .line 12
    .line 13
    .line 14
    move-result-object v1

    .line 15
    :try_start_0
    invoke-interface {v1}, LG6/g;->l0()Ljava/lang/String;

    .line 16
    .line 17
    .line 18
    move-result-object v2

    .line 19
    invoke-interface {v1}, LG6/g;->l0()Ljava/lang/String;

    .line 20
    .line 21
    .line 22
    move-result-object v3

    .line 23
    invoke-interface {v1}, LG6/g;->l0()Ljava/lang/String;

    .line 24
    .line 25
    .line 26
    move-result-object v4

    .line 27
    invoke-interface {v1}, LG6/g;->l0()Ljava/lang/String;

    .line 28
    .line 29
    .line 30
    move-result-object v5

    .line 31
    invoke-interface {v1}, LG6/g;->l0()Ljava/lang/String;

    .line 32
    .line 33
    .line 34
    move-result-object v6

    .line 35
    sget-object v7, Lv6/d;->z:Ljava/lang/String;

    .line 36
    .line 37
    invoke-static {v7, v2}, Lkotlin/jvm/internal/r;->b(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 38
    .line 39
    .line 40
    move-result v7

    .line 41
    if-eqz v7, :cond_1

    .line 42
    .line 43
    sget-object v7, Lv6/d;->A:Ljava/lang/String;

    .line 44
    .line 45
    invoke-static {v7, v3}, Lkotlin/jvm/internal/r;->b(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 46
    .line 47
    .line 48
    move-result v7

    .line 49
    if-eqz v7, :cond_1

    .line 50
    .line 51
    iget v7, p0, Lv6/d;->c:I

    .line 52
    .line 53
    invoke-static {v7}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 54
    .line 55
    .line 56
    move-result-object v7

    .line 57
    invoke-static {v7, v4}, Lkotlin/jvm/internal/r;->b(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 58
    .line 59
    .line 60
    move-result v4

    .line 61
    if-eqz v4, :cond_1

    .line 62
    .line 63
    invoke-virtual {p0}, Lv6/d;->i0()I

    .line 64
    .line 65
    .line 66
    move-result v4

    .line 67
    invoke-static {v4}, Ljava/lang/String;->valueOf(I)Ljava/lang/String;

    .line 68
    .line 69
    .line 70
    move-result-object v4

    .line 71
    invoke-static {v4, v5}, Lkotlin/jvm/internal/r;->b(Ljava/lang/Object;Ljava/lang/Object;)Z

    .line 72
    .line 73
    .line 74
    move-result v4

    .line 75
    if-eqz v4, :cond_1

    .line 76
    .line 77
    invoke-interface {v6}, Ljava/lang/CharSequence;->length()I

    .line 78
    .line 79
    .line 80
    move-result v4
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 81
    if-gtz v4, :cond_1

    .line 82
    .line 83
    const/4 v0, 0x0

    .line 84
    :goto_0
    :try_start_1
    invoke-interface {v1}, LG6/g;->l0()Ljava/lang/String;

    .line 85
    .line 86
    .line 87
    move-result-object v2

    .line 88
    invoke-virtual {p0, v2}, Lv6/d;->D0(Ljava/lang/String;)V
    :try_end_1
    .catch Ljava/io/EOFException; {:try_start_1 .. :try_end_1} :catch_0
    .catchall {:try_start_1 .. :try_end_1} :catchall_0

    .line 89
    .line 90
    .line 91
    add-int/lit8 v0, v0, 0x1

    .line 92
    .line 93
    goto :goto_0

    .line 94
    :catchall_0
    move-exception v0

    .line 95
    goto :goto_2

    .line 96
    :catch_0
    :try_start_2
    invoke-virtual {p0}, Lv6/d;->h0()Ljava/util/LinkedHashMap;

    .line 97
    .line 98
    .line 99
    move-result-object v2

    .line 100
    invoke-virtual {v2}, Ljava/util/AbstractMap;->size()I

    .line 101
    .line 102
    .line 103
    move-result v2

    .line 104
    sub-int/2addr v0, v2

    .line 105
    iput v0, p0, Lv6/d;->l:I

    .line 106
    .line 107
    invoke-interface {v1}, LG6/g;->E()Z

    .line 108
    .line 109
    .line 110
    move-result v0

    .line 111
    if-nez v0, :cond_0

    .line 112
    .line 113
    invoke-virtual {p0}, Lv6/d;->E0()V

    .line 114
    .line 115
    .line 116
    goto :goto_1

    .line 117
    :cond_0
    invoke-virtual {p0}, Lv6/d;->q0()LG6/f;

    .line 118
    .line 119
    .line 120
    move-result-object v0

    .line 121
    iput-object v0, p0, Lv6/d;->j:LG6/f;

    .line 122
    .line 123
    :goto_1
    sget-object v0, Ly5/I;->a:Ly5/I;
    :try_end_2
    .catchall {:try_start_2 .. :try_end_2} :catchall_0

    .line 124
    .line 125
    const/4 v0, 0x0

    .line 126
    invoke-static {v1, v0}, LK5/b;->a(Ljava/io/Closeable;Ljava/lang/Throwable;)V

    .line 127
    .line 128
    .line 129
    return-void

    .line 130
    :cond_1
    :try_start_3
    new-instance v4, Ljava/io/IOException;

    .line 131
    .line 132
    new-instance v7, Ljava/lang/StringBuilder;

    .line 133
    .line 134
    invoke-direct {v7}, Ljava/lang/StringBuilder;-><init>()V

    .line 135
    .line 136
    .line 137
    const-string v8, "unexpected journal header: ["

    .line 138
    .line 139
    invoke-virtual {v7, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 140
    .line 141
    .line 142
    invoke-virtual {v7, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 143
    .line 144
    .line 145
    invoke-virtual {v7, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 146
    .line 147
    .line 148
    invoke-virtual {v7, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 149
    .line 150
    .line 151
    invoke-virtual {v7, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 152
    .line 153
    .line 154
    invoke-virtual {v7, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 155
    .line 156
    .line 157
    invoke-virtual {v7, v0}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 158
    .line 159
    .line 160
    invoke-virtual {v7, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 161
    .line 162
    .line 163
    const/16 v0, 0x5d

    .line 164
    .line 165
    invoke-virtual {v7, v0}, Ljava/lang/StringBuilder;->append(C)Ljava/lang/StringBuilder;

    .line 166
    .line 167
    .line 168
    invoke-virtual {v7}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 169
    .line 170
    .line 171
    move-result-object v0

    .line 172
    invoke-direct {v4, v0}, Ljava/io/IOException;-><init>(Ljava/lang/String;)V

    .line 173
    .line 174
    .line 175
    throw v4
    :try_end_3
    .catchall {:try_start_3 .. :try_end_3} :catchall_0

    .line 176
    :goto_2
    :try_start_4
    throw v0
    :try_end_4
    .catchall {:try_start_4 .. :try_end_4} :catchall_1

    .line 177
    :catchall_1
    move-exception v2

    .line 178
    invoke-static {v1, v0}, LK5/b;->a(Ljava/io/Closeable;Ljava/lang/Throwable;)V

    .line 179
    .line 180
    .line 181
    throw v2
.end method

.method public final z()V
    .locals 2

    .line 1
    invoke-virtual {p0}, Lv6/d;->close()V

    .line 2
    .line 3
    .line 4
    iget-object v0, p0, Lv6/d;->a:LB6/a;

    .line 5
    .line 6
    iget-object v1, p0, Lv6/d;->b:Ljava/io/File;

    .line 7
    .line 8
    invoke-interface {v0, v1}, LB6/a;->c(Ljava/io/File;)V

    .line 9
    .line 10
    .line 11
    return-void
.end method
