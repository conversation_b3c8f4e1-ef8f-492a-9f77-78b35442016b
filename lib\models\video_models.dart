class Chapter {
  final String title;
  final Duration time;
  final String? description;

  Chapter({
    required this.title,
    required this.time,
    this.description,
  });
}

class VideoQuality {
  final String label;
  final String url;
  final int height;

  VideoQuality({
    required this.label,
    required this.url,
    required this.height,
  });
}

class AudioTrack {
  final int id;
  final String language;
  final String name;

  AudioTrack({
    required this.id,
    required this.language,
    required this.name,
  });
}

class SubtitleTrack {
  final int id;
  final String language;
  final String name;

  SubtitleTrack({
    required this.id,
    required this.language,
    required this.name,
  });
}

class VideoInfo {
  final String title;
  final Duration duration;
  final String resolution;
  final String codec;
  final double frameRate;
  final int bitrate;

  VideoInfo({
    required this.title,
    required this.duration,
    required this.resolution,
    required this.codec,
    required this.frameRate,
    required this.bitrate,
  });
}

class PlaylistItem {
  final String id;
  final String title;
  final String url;
  final String? thumbnail;
  final Duration? duration;

  PlaylistItem({
    required this.id,
    required this.title,
    required this.url,
    this.thumbnail,
    this.duration,
  });
}
