.class public final Lx6/e$c;
.super LG6/c;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lx6/e;-><init>(Ls6/x;Ls6/z;Z)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation


# instance fields
.field public final synthetic o:Lx6/e;


# direct methods
.method public constructor <init>(Lx6/e;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lx6/e$c;->o:Lx6/e;

    .line 2
    .line 3
    invoke-direct {p0}, LG6/c;-><init>()V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public B()V
    .locals 1

    .line 1
    iget-object v0, p0, Lx6/e$c;->o:Lx6/e;

    .line 2
    .line 3
    invoke-virtual {v0}, Lx6/e;->cancel()V

    .line 4
    .line 5
    .line 6
    return-void
.end method
