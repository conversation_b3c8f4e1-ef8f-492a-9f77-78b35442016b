.class public final LX5/A0$b;
.super LX5/z0;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LX5/A0;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "b"
.end annotation


# instance fields
.field public final e:LX5/A0;

.field public final f:LX5/A0$c;

.field public final g:LX5/t;

.field public final h:Ljava/lang/Object;


# direct methods
.method public constructor <init>(LX5/A0;LX5/A0$c;LX5/t;Ljava/lang/Object;)V
    .locals 0

    .line 1
    invoke-direct {p0}, LX5/z0;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LX5/A0$b;->e:LX5/A0;

    .line 5
    .line 6
    iput-object p2, p0, LX5/A0$b;->f:LX5/A0$c;

    .line 7
    .line 8
    iput-object p3, p0, LX5/A0$b;->g:LX5/t;

    .line 9
    .line 10
    iput-object p4, p0, LX5/A0$b;->h:Ljava/lang/Object;

    .line 11
    .line 12
    return-void
.end method


# virtual methods
.method public bridge synthetic invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Ljava/lang/Throwable;

    .line 2
    .line 3
    invoke-virtual {p0, p1}, LX5/A0$b;->v(Ljava/lang/Throwable;)V

    .line 4
    .line 5
    .line 6
    sget-object p1, Ly5/I;->a:Ly5/I;

    .line 7
    .line 8
    return-object p1
.end method

.method public v(Ljava/lang/Throwable;)V
    .locals 3

    .line 1
    iget-object p1, p0, LX5/A0$b;->e:LX5/A0;

    .line 2
    .line 3
    iget-object v0, p0, LX5/A0$b;->f:LX5/A0$c;

    .line 4
    .line 5
    iget-object v1, p0, LX5/A0$b;->g:LX5/t;

    .line 6
    .line 7
    iget-object v2, p0, LX5/A0$b;->h:Ljava/lang/Object;

    .line 8
    .line 9
    invoke-static {p1, v0, v1, v2}, LX5/A0;->C(LX5/A0;LX5/A0$c;LX5/t;Ljava/lang/Object;)V

    .line 10
    .line 11
    .line 12
    return-void
.end method
