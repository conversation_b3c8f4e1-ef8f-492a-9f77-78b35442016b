.class public final Lv6/a;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ls6/v;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lv6/a$a;
    }
.end annotation


# static fields
.field public static final b:Lv6/a$a;


# instance fields
.field public final a:Ls6/c;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 1
    new-instance v0, Lv6/a$a;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, v1}, Lv6/a$a;-><init>(Lkotlin/jvm/internal/j;)V

    .line 5
    .line 6
    .line 7
    sput-object v0, Lv6/a;->b:Lv6/a$a;

    .line 8
    .line 9
    return-void
.end method

.method public constructor <init>(Ls6/c;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lv6/a;->a:Ls6/c;

    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final a(Lv6/b;Ls6/B;)Ls6/B;
    .locals 4

    .line 1
    if-nez p1, :cond_0

    .line 2
    .line 3
    return-object p2

    .line 4
    :cond_0
    invoke-interface {p1}, Lv6/b;->b()LG6/X;

    .line 5
    .line 6
    .line 7
    move-result-object v0

    .line 8
    invoke-virtual {p2}, Ls6/B;->a()Ls6/C;

    .line 9
    .line 10
    .line 11
    move-result-object v1

    .line 12
    invoke-static {v1}, Lkotlin/jvm/internal/r;->c(Ljava/lang/Object;)V

    .line 13
    .line 14
    .line 15
    invoke-virtual {v1}, Ls6/C;->source()LG6/g;

    .line 16
    .line 17
    .line 18
    move-result-object v1

    .line 19
    invoke-static {v0}, LG6/L;->c(LG6/X;)LG6/f;

    .line 20
    .line 21
    .line 22
    move-result-object v0

    .line 23
    new-instance v2, Lv6/a$b;

    .line 24
    .line 25
    invoke-direct {v2, v1, p1, v0}, Lv6/a$b;-><init>(LG6/g;Lv6/b;LG6/f;)V

    .line 26
    .line 27
    .line 28
    const-string p1, "Content-Type"

    .line 29
    .line 30
    const/4 v0, 0x2

    .line 31
    const/4 v1, 0x0

    .line 32
    invoke-static {p2, p1, v1, v0, v1}, Ls6/B;->t(Ls6/B;Ljava/lang/String;Ljava/lang/String;ILjava/lang/Object;)Ljava/lang/String;

    .line 33
    .line 34
    .line 35
    move-result-object p1

    .line 36
    invoke-virtual {p2}, Ls6/B;->a()Ls6/C;

    .line 37
    .line 38
    .line 39
    move-result-object v0

    .line 40
    invoke-virtual {v0}, Ls6/C;->contentLength()J

    .line 41
    .line 42
    .line 43
    move-result-wide v0

    .line 44
    invoke-virtual {p2}, Ls6/B;->K()Ls6/B$a;

    .line 45
    .line 46
    .line 47
    move-result-object p2

    .line 48
    new-instance v3, Ly6/h;

    .line 49
    .line 50
    invoke-static {v2}, LG6/L;->d(LG6/Z;)LG6/g;

    .line 51
    .line 52
    .line 53
    move-result-object v2

    .line 54
    invoke-direct {v3, p1, v0, v1, v2}, Ly6/h;-><init>(Ljava/lang/String;JLG6/g;)V

    .line 55
    .line 56
    .line 57
    invoke-virtual {p2, v3}, Ls6/B$a;->b(Ls6/C;)Ls6/B$a;

    .line 58
    .line 59
    .line 60
    move-result-object p1

    .line 61
    invoke-virtual {p1}, Ls6/B$a;->c()Ls6/B;

    .line 62
    .line 63
    .line 64
    move-result-object p1

    .line 65
    return-object p1
.end method

.method public intercept(Ls6/v$a;)Ls6/B;
    .locals 8

    .line 1
    const-string v0, "chain"

    .line 2
    .line 3
    invoke-static {p1, v0}, Lkotlin/jvm/internal/r;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    invoke-interface {p1}, Ls6/v$a;->call()Ls6/e;

    .line 7
    .line 8
    .line 9
    move-result-object v0

    .line 10
    iget-object v1, p0, Lv6/a;->a:Ls6/c;

    .line 11
    .line 12
    const/4 v2, 0x0

    .line 13
    if-nez v1, :cond_0

    .line 14
    .line 15
    move-object v1, v2

    .line 16
    goto :goto_0

    .line 17
    :cond_0
    invoke-interface {p1}, Ls6/v$a;->a()Ls6/z;

    .line 18
    .line 19
    .line 20
    move-result-object v3

    .line 21
    invoke-virtual {v1, v3}, Ls6/c;->b(Ls6/z;)Ls6/B;

    .line 22
    .line 23
    .line 24
    move-result-object v1

    .line 25
    :goto_0
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    .line 26
    .line 27
    .line 28
    move-result-wide v3

    .line 29
    new-instance v5, Lv6/c$b;

    .line 30
    .line 31
    invoke-interface {p1}, Ls6/v$a;->a()Ls6/z;

    .line 32
    .line 33
    .line 34
    move-result-object v6

    .line 35
    invoke-direct {v5, v3, v4, v6, v1}, Lv6/c$b;-><init>(JLs6/z;Ls6/B;)V

    .line 36
    .line 37
    .line 38
    invoke-virtual {v5}, Lv6/c$b;->b()Lv6/c;

    .line 39
    .line 40
    .line 41
    move-result-object v3

    .line 42
    invoke-virtual {v3}, Lv6/c;->b()Ls6/z;

    .line 43
    .line 44
    .line 45
    move-result-object v4

    .line 46
    invoke-virtual {v3}, Lv6/c;->a()Ls6/B;

    .line 47
    .line 48
    .line 49
    move-result-object v5

    .line 50
    iget-object v6, p0, Lv6/a;->a:Ls6/c;

    .line 51
    .line 52
    if-nez v6, :cond_1

    .line 53
    .line 54
    goto :goto_1

    .line 55
    :cond_1
    invoke-virtual {v6, v3}, Ls6/c;->v(Lv6/c;)V

    .line 56
    .line 57
    .line 58
    :goto_1
    instance-of v3, v0, Lx6/e;

    .line 59
    .line 60
    if-eqz v3, :cond_2

    .line 61
    .line 62
    move-object v3, v0

    .line 63
    check-cast v3, Lx6/e;

    .line 64
    .line 65
    goto :goto_2

    .line 66
    :cond_2
    move-object v3, v2

    .line 67
    :goto_2
    if-nez v3, :cond_3

    .line 68
    .line 69
    goto :goto_3

    .line 70
    :cond_3
    invoke-virtual {v3}, Lx6/e;->n()Ls6/r;

    .line 71
    .line 72
    .line 73
    move-result-object v2

    .line 74
    :goto_3
    if-nez v2, :cond_4

    .line 75
    .line 76
    sget-object v2, Ls6/r;->b:Ls6/r;

    .line 77
    .line 78
    :cond_4
    if-eqz v1, :cond_6

    .line 79
    .line 80
    if-nez v5, :cond_6

    .line 81
    .line 82
    invoke-virtual {v1}, Ls6/B;->a()Ls6/C;

    .line 83
    .line 84
    .line 85
    move-result-object v3

    .line 86
    if-nez v3, :cond_5

    .line 87
    .line 88
    goto :goto_4

    .line 89
    :cond_5
    invoke-static {v3}, Lt6/d;->m(Ljava/io/Closeable;)V

    .line 90
    .line 91
    .line 92
    :cond_6
    :goto_4
    if-nez v4, :cond_7

    .line 93
    .line 94
    if-nez v5, :cond_7

    .line 95
    .line 96
    new-instance v1, Ls6/B$a;

    .line 97
    .line 98
    invoke-direct {v1}, Ls6/B$a;-><init>()V

    .line 99
    .line 100
    .line 101
    invoke-interface {p1}, Ls6/v$a;->a()Ls6/z;

    .line 102
    .line 103
    .line 104
    move-result-object p1

    .line 105
    invoke-virtual {v1, p1}, Ls6/B$a;->s(Ls6/z;)Ls6/B$a;

    .line 106
    .line 107
    .line 108
    move-result-object p1

    .line 109
    sget-object v1, Ls6/y;->d:Ls6/y;

    .line 110
    .line 111
    invoke-virtual {p1, v1}, Ls6/B$a;->q(Ls6/y;)Ls6/B$a;

    .line 112
    .line 113
    .line 114
    move-result-object p1

    .line 115
    const/16 v1, 0x1f8

    .line 116
    .line 117
    invoke-virtual {p1, v1}, Ls6/B$a;->g(I)Ls6/B$a;

    .line 118
    .line 119
    .line 120
    move-result-object p1

    .line 121
    const-string v1, "Unsatisfiable Request (only-if-cached)"

    .line 122
    .line 123
    invoke-virtual {p1, v1}, Ls6/B$a;->n(Ljava/lang/String;)Ls6/B$a;

    .line 124
    .line 125
    .line 126
    move-result-object p1

    .line 127
    sget-object v1, Lt6/d;->c:Ls6/C;

    .line 128
    .line 129
    invoke-virtual {p1, v1}, Ls6/B$a;->b(Ls6/C;)Ls6/B$a;

    .line 130
    .line 131
    .line 132
    move-result-object p1

    .line 133
    const-wide/16 v3, -0x1

    .line 134
    .line 135
    invoke-virtual {p1, v3, v4}, Ls6/B$a;->t(J)Ls6/B$a;

    .line 136
    .line 137
    .line 138
    move-result-object p1

    .line 139
    invoke-static {}, Ljava/lang/System;->currentTimeMillis()J

    .line 140
    .line 141
    .line 142
    move-result-wide v3

    .line 143
    invoke-virtual {p1, v3, v4}, Ls6/B$a;->r(J)Ls6/B$a;

    .line 144
    .line 145
    .line 146
    move-result-object p1

    .line 147
    invoke-virtual {p1}, Ls6/B$a;->c()Ls6/B;

    .line 148
    .line 149
    .line 150
    move-result-object p1

    .line 151
    invoke-virtual {v2, v0, p1}, Ls6/r;->A(Ls6/e;Ls6/B;)V

    .line 152
    .line 153
    .line 154
    return-object p1

    .line 155
    :cond_7
    if-nez v4, :cond_8

    .line 156
    .line 157
    invoke-static {v5}, Lkotlin/jvm/internal/r;->c(Ljava/lang/Object;)V

    .line 158
    .line 159
    .line 160
    invoke-virtual {v5}, Ls6/B;->K()Ls6/B$a;

    .line 161
    .line 162
    .line 163
    move-result-object p1

    .line 164
    sget-object v1, Lv6/a;->b:Lv6/a$a;

    .line 165
    .line 166
    invoke-static {v1, v5}, Lv6/a$a;->b(Lv6/a$a;Ls6/B;)Ls6/B;

    .line 167
    .line 168
    .line 169
    move-result-object v1

    .line 170
    invoke-virtual {p1, v1}, Ls6/B$a;->d(Ls6/B;)Ls6/B$a;

    .line 171
    .line 172
    .line 173
    move-result-object p1

    .line 174
    invoke-virtual {p1}, Ls6/B$a;->c()Ls6/B;

    .line 175
    .line 176
    .line 177
    move-result-object p1

    .line 178
    invoke-virtual {v2, v0, p1}, Ls6/r;->b(Ls6/e;Ls6/B;)V

    .line 179
    .line 180
    .line 181
    return-object p1

    .line 182
    :cond_8
    if-eqz v5, :cond_9

    .line 183
    .line 184
    invoke-virtual {v2, v0, v5}, Ls6/r;->a(Ls6/e;Ls6/B;)V

    .line 185
    .line 186
    .line 187
    goto :goto_5

    .line 188
    :cond_9
    iget-object v3, p0, Lv6/a;->a:Ls6/c;

    .line 189
    .line 190
    if-eqz v3, :cond_a

    .line 191
    .line 192
    invoke-virtual {v2, v0}, Ls6/r;->c(Ls6/e;)V

    .line 193
    .line 194
    .line 195
    :cond_a
    :goto_5
    :try_start_0
    invoke-interface {p1, v4}, Ls6/v$a;->b(Ls6/z;)Ls6/B;

    .line 196
    .line 197
    .line 198
    move-result-object p1
    :try_end_0
    .catchall {:try_start_0 .. :try_end_0} :catchall_0

    .line 199
    if-nez p1, :cond_c

    .line 200
    .line 201
    if-eqz v1, :cond_c

    .line 202
    .line 203
    invoke-virtual {v1}, Ls6/B;->a()Ls6/C;

    .line 204
    .line 205
    .line 206
    move-result-object v1

    .line 207
    if-nez v1, :cond_b

    .line 208
    .line 209
    goto :goto_6

    .line 210
    :cond_b
    invoke-static {v1}, Lt6/d;->m(Ljava/io/Closeable;)V

    .line 211
    .line 212
    .line 213
    :cond_c
    :goto_6
    if-eqz v5, :cond_10

    .line 214
    .line 215
    if-nez p1, :cond_d

    .line 216
    .line 217
    goto :goto_7

    .line 218
    :cond_d
    invoke-virtual {p1}, Ls6/B;->h()I

    .line 219
    .line 220
    .line 221
    move-result v1

    .line 222
    const/16 v3, 0x130

    .line 223
    .line 224
    if-ne v1, v3, :cond_e

    .line 225
    .line 226
    invoke-virtual {v5}, Ls6/B;->K()Ls6/B$a;

    .line 227
    .line 228
    .line 229
    move-result-object v1

    .line 230
    sget-object v3, Lv6/a;->b:Lv6/a$a;

    .line 231
    .line 232
    invoke-virtual {v5}, Ls6/B;->v()Ls6/t;

    .line 233
    .line 234
    .line 235
    move-result-object v4

    .line 236
    invoke-virtual {p1}, Ls6/B;->v()Ls6/t;

    .line 237
    .line 238
    .line 239
    move-result-object v6

    .line 240
    invoke-static {v3, v4, v6}, Lv6/a$a;->a(Lv6/a$a;Ls6/t;Ls6/t;)Ls6/t;

    .line 241
    .line 242
    .line 243
    move-result-object v4

    .line 244
    invoke-virtual {v1, v4}, Ls6/B$a;->l(Ls6/t;)Ls6/B$a;

    .line 245
    .line 246
    .line 247
    move-result-object v1

    .line 248
    invoke-virtual {p1}, Ls6/B;->i0()J

    .line 249
    .line 250
    .line 251
    move-result-wide v6

    .line 252
    invoke-virtual {v1, v6, v7}, Ls6/B$a;->t(J)Ls6/B$a;

    .line 253
    .line 254
    .line 255
    move-result-object v1

    .line 256
    invoke-virtual {p1}, Ls6/B;->X()J

    .line 257
    .line 258
    .line 259
    move-result-wide v6

    .line 260
    invoke-virtual {v1, v6, v7}, Ls6/B$a;->r(J)Ls6/B$a;

    .line 261
    .line 262
    .line 263
    move-result-object v1

    .line 264
    invoke-static {v3, v5}, Lv6/a$a;->b(Lv6/a$a;Ls6/B;)Ls6/B;

    .line 265
    .line 266
    .line 267
    move-result-object v4

    .line 268
    invoke-virtual {v1, v4}, Ls6/B$a;->d(Ls6/B;)Ls6/B$a;

    .line 269
    .line 270
    .line 271
    move-result-object v1

    .line 272
    invoke-static {v3, p1}, Lv6/a$a;->b(Lv6/a$a;Ls6/B;)Ls6/B;

    .line 273
    .line 274
    .line 275
    move-result-object v3

    .line 276
    invoke-virtual {v1, v3}, Ls6/B$a;->o(Ls6/B;)Ls6/B$a;

    .line 277
    .line 278
    .line 279
    move-result-object v1

    .line 280
    invoke-virtual {v1}, Ls6/B$a;->c()Ls6/B;

    .line 281
    .line 282
    .line 283
    move-result-object v1

    .line 284
    invoke-virtual {p1}, Ls6/B;->a()Ls6/C;

    .line 285
    .line 286
    .line 287
    move-result-object p1

    .line 288
    invoke-static {p1}, Lkotlin/jvm/internal/r;->c(Ljava/lang/Object;)V

    .line 289
    .line 290
    .line 291
    invoke-virtual {p1}, Ls6/C;->close()V

    .line 292
    .line 293
    .line 294
    iget-object p1, p0, Lv6/a;->a:Ls6/c;

    .line 295
    .line 296
    invoke-static {p1}, Lkotlin/jvm/internal/r;->c(Ljava/lang/Object;)V

    .line 297
    .line 298
    .line 299
    invoke-virtual {p1}, Ls6/c;->t()V

    .line 300
    .line 301
    .line 302
    iget-object p1, p0, Lv6/a;->a:Ls6/c;

    .line 303
    .line 304
    invoke-virtual {p1, v5, v1}, Ls6/c;->z(Ls6/B;Ls6/B;)V

    .line 305
    .line 306
    .line 307
    invoke-virtual {v2, v0, v1}, Ls6/r;->b(Ls6/e;Ls6/B;)V

    .line 308
    .line 309
    .line 310
    return-object v1

    .line 311
    :cond_e
    :goto_7
    invoke-virtual {v5}, Ls6/B;->a()Ls6/C;

    .line 312
    .line 313
    .line 314
    move-result-object v1

    .line 315
    if-nez v1, :cond_f

    .line 316
    .line 317
    goto :goto_8

    .line 318
    :cond_f
    invoke-static {v1}, Lt6/d;->m(Ljava/io/Closeable;)V

    .line 319
    .line 320
    .line 321
    :cond_10
    :goto_8
    invoke-static {p1}, Lkotlin/jvm/internal/r;->c(Ljava/lang/Object;)V

    .line 322
    .line 323
    .line 324
    invoke-virtual {p1}, Ls6/B;->K()Ls6/B$a;

    .line 325
    .line 326
    .line 327
    move-result-object v1

    .line 328
    sget-object v3, Lv6/a;->b:Lv6/a$a;

    .line 329
    .line 330
    invoke-static {v3, v5}, Lv6/a$a;->b(Lv6/a$a;Ls6/B;)Ls6/B;

    .line 331
    .line 332
    .line 333
    move-result-object v6

    .line 334
    invoke-virtual {v1, v6}, Ls6/B$a;->d(Ls6/B;)Ls6/B$a;

    .line 335
    .line 336
    .line 337
    move-result-object v1

    .line 338
    invoke-static {v3, p1}, Lv6/a$a;->b(Lv6/a$a;Ls6/B;)Ls6/B;

    .line 339
    .line 340
    .line 341
    move-result-object p1

    .line 342
    invoke-virtual {v1, p1}, Ls6/B$a;->o(Ls6/B;)Ls6/B$a;

    .line 343
    .line 344
    .line 345
    move-result-object p1

    .line 346
    invoke-virtual {p1}, Ls6/B$a;->c()Ls6/B;

    .line 347
    .line 348
    .line 349
    move-result-object p1

    .line 350
    iget-object v1, p0, Lv6/a;->a:Ls6/c;

    .line 351
    .line 352
    if-eqz v1, :cond_13

    .line 353
    .line 354
    invoke-static {p1}, Ly6/e;->b(Ls6/B;)Z

    .line 355
    .line 356
    .line 357
    move-result v1

    .line 358
    if-eqz v1, :cond_12

    .line 359
    .line 360
    sget-object v1, Lv6/c;->c:Lv6/c$a;

    .line 361
    .line 362
    invoke-virtual {v1, p1, v4}, Lv6/c$a;->a(Ls6/B;Ls6/z;)Z

    .line 363
    .line 364
    .line 365
    move-result v1

    .line 366
    if-eqz v1, :cond_12

    .line 367
    .line 368
    iget-object v1, p0, Lv6/a;->a:Ls6/c;

    .line 369
    .line 370
    invoke-virtual {v1, p1}, Ls6/c;->h(Ls6/B;)Lv6/b;

    .line 371
    .line 372
    .line 373
    move-result-object v1

    .line 374
    invoke-virtual {p0, v1, p1}, Lv6/a;->a(Lv6/b;Ls6/B;)Ls6/B;

    .line 375
    .line 376
    .line 377
    move-result-object p1

    .line 378
    if-eqz v5, :cond_11

    .line 379
    .line 380
    invoke-virtual {v2, v0}, Ls6/r;->c(Ls6/e;)V

    .line 381
    .line 382
    .line 383
    :cond_11
    return-object p1

    .line 384
    :cond_12
    sget-object v0, Ly6/f;->a:Ly6/f;

    .line 385
    .line 386
    invoke-virtual {v4}, Ls6/z;->h()Ljava/lang/String;

    .line 387
    .line 388
    .line 389
    move-result-object v1

    .line 390
    invoke-virtual {v0, v1}, Ly6/f;->a(Ljava/lang/String;)Z

    .line 391
    .line 392
    .line 393
    move-result v0

    .line 394
    if-eqz v0, :cond_13

    .line 395
    .line 396
    :try_start_1
    iget-object v0, p0, Lv6/a;->a:Ls6/c;

    .line 397
    .line 398
    invoke-virtual {v0, v4}, Ls6/c;->j(Ls6/z;)V
    :try_end_1
    .catch Ljava/io/IOException; {:try_start_1 .. :try_end_1} :catch_0

    .line 399
    .line 400
    .line 401
    :catch_0
    :cond_13
    return-object p1

    .line 402
    :catchall_0
    move-exception p1

    .line 403
    if-eqz v1, :cond_15

    .line 404
    .line 405
    invoke-virtual {v1}, Ls6/B;->a()Ls6/C;

    .line 406
    .line 407
    .line 408
    move-result-object v0

    .line 409
    if-nez v0, :cond_14

    .line 410
    .line 411
    goto :goto_9

    .line 412
    :cond_14
    invoke-static {v0}, Lt6/d;->m(Ljava/io/Closeable;)V

    .line 413
    .line 414
    .line 415
    :cond_15
    :goto_9
    throw p1
.end method
