Stack trace:
Frame         Function      Args
0007FFFFA0E0  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFA0E0, 0007FFFF8FE0) msys-2.0.dll+0x1FE8E
0007FFFFA0E0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA3B8) msys-2.0.dll+0x67F9
0007FFFFA0E0  000210046832 (000210286019, 0007FFFF9F98, 0007FFFFA0E0, 000000000000) msys-2.0.dll+0x6832
0007FFFFA0E0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFA0E0  000210068E24 (0007FFFFA0F0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFA3C0  00021006A225 (0007FFFFA0F0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFEB5B00000 ntdll.dll
7FFE84A60000 aswhook.dll
7FFEB4EE0000 KERNEL32.DLL
7FFEB2CF0000 KERNELBASE.dll
7FFEB4D10000 USER32.dll
7FFEB3260000 win32u.dll
7FFEB3DC0000 GDI32.dll
7FFEB3760000 gdi32full.dll
7FFEB36B0000 msvcp_win.dll
7FFEB34D0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFEB3A00000 advapi32.dll
7FFEB38A0000 msvcrt.dll
7FFEB3B40000 sechost.dll
7FFEB5320000 RPCRT4.dll
7FFEB21D0000 CRYPTBASE.DLL
7FFEB2C50000 bcryptPrimitives.dll
7FFEB3C80000 IMM32.DLL
