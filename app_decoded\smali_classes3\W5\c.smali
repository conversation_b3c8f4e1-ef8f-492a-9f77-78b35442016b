.class public abstract LW5/c;
.super Ljava/lang/Object;
.source "SourceFile"


# direct methods
.method public static final synthetic a(JI)J
    .locals 0

    .line 1
    invoke-static {p0, p1, p2}, LW5/c;->i(JI)J

    .line 2
    .line 3
    .line 4
    move-result-wide p0

    .line 5
    return-wide p0
.end method

.method public static final synthetic b(J)J
    .locals 0

    .line 1
    invoke-static {p0, p1}, LW5/c;->j(J)J

    .line 2
    .line 3
    .line 4
    move-result-wide p0

    .line 5
    return-wide p0
.end method

.method public static final synthetic c(J)J
    .locals 0

    .line 1
    invoke-static {p0, p1}, LW5/c;->k(J)J

    .line 2
    .line 3
    .line 4
    move-result-wide p0

    .line 5
    return-wide p0
.end method

.method public static final synthetic d(J)J
    .locals 0

    .line 1
    invoke-static {p0, p1}, LW5/c;->l(J)J

    .line 2
    .line 3
    .line 4
    move-result-wide p0

    .line 5
    return-wide p0
.end method

.method public static final synthetic e(J)<PERSON>
    <PERSON>locals 0

    .line 1
    invoke-static {p0, p1}, LW5/c;->m(J)J

    .line 2
    .line 3
    .line 4
    move-result-wide p0

    .line 5
    return-wide p0
.end method

.method public static final synthetic f(J)J
    .locals 0

    .line 1
    invoke-static {p0, p1}, LW5/c;->n(J)J

    .line 2
    .line 3
    .line 4
    move-result-wide p0

    .line 5
    return-wide p0
.end method

.method public static final synthetic g(J)J
    .locals 0

    .line 1
    invoke-static {p0, p1}, LW5/c;->o(J)J

    .line 2
    .line 3
    .line 4
    move-result-wide p0

    .line 5
    return-wide p0
.end method

.method public static final synthetic h(Ljava/lang/String;Z)J
    .locals 0

    .line 1
    invoke-static {p0, p1}, LW5/c;->p(Ljava/lang/String;Z)J

    .line 2
    .line 3
    .line 4
    move-result-wide p0

    .line 5
    return-wide p0
.end method

.method public static final i(JI)J
    .locals 2

    .line 1
    const/4 v0, 0x1

    .line 2
    shl-long/2addr p0, v0

    .line 3
    int-to-long v0, p2

    .line 4
    add-long/2addr p0, v0

    .line 5
    invoke-static {p0, p1}, LW5/a;->h(J)J

    .line 6
    .line 7
    .line 8
    move-result-wide p0

    .line 9
    return-wide p0
.end method

.method public static final j(J)J
    .locals 2

    .line 1
    const/4 v0, 0x1

    .line 2
    shl-long/2addr p0, v0

    .line 3
    const-wide/16 v0, 0x1

    .line 4
    .line 5
    add-long/2addr p0, v0

    .line 6
    invoke-static {p0, p1}, LW5/a;->h(J)J

    .line 7
    .line 8
    .line 9
    move-result-wide p0

    .line 10
    return-wide p0
.end method

.method public static final k(J)J
    .locals 6

    .line 1
    new-instance v0, LS5/j;

    .line 2
    .line 3
    const-wide v1, -0x431bde82d7aL

    .line 4
    .line 5
    .line 6
    .line 7
    .line 8
    const-wide v3, 0x431bde82d7aL

    .line 9
    .line 10
    .line 11
    .line 12
    .line 13
    invoke-direct {v0, v1, v2, v3, v4}, LS5/j;-><init>(JJ)V

    .line 14
    .line 15
    .line 16
    invoke-virtual {v0, p0, p1}, LS5/j;->i(J)Z

    .line 17
    .line 18
    .line 19
    move-result v0

    .line 20
    if-eqz v0, :cond_0

    .line 21
    .line 22
    invoke-static {p0, p1}, LW5/c;->n(J)J

    .line 23
    .line 24
    .line 25
    move-result-wide p0

    .line 26
    invoke-static {p0, p1}, LW5/c;->l(J)J

    .line 27
    .line 28
    .line 29
    move-result-wide p0

    .line 30
    goto :goto_0

    .line 31
    :cond_0
    const-wide v2, -0x3fffffffffffffffL    # -2.0000000000000004

    .line 32
    .line 33
    .line 34
    .line 35
    .line 36
    const-wide v4, 0x3fffffffffffffffL    # 1.9999999999999998

    .line 37
    .line 38
    .line 39
    .line 40
    .line 41
    move-wide v0, p0

    .line 42
    invoke-static/range {v0 .. v5}, LS5/k;->g(JJJ)J

    .line 43
    .line 44
    .line 45
    move-result-wide p0

    .line 46
    invoke-static {p0, p1}, LW5/c;->j(J)J

    .line 47
    .line 48
    .line 49
    move-result-wide p0

    .line 50
    :goto_0
    return-wide p0
.end method

.method public static final l(J)J
    .locals 1

    .line 1
    const/4 v0, 0x1

    .line 2
    shl-long/2addr p0, v0

    .line 3
    invoke-static {p0, p1}, LW5/a;->h(J)J

    .line 4
    .line 5
    .line 6
    move-result-wide p0

    .line 7
    return-wide p0
.end method

.method public static final m(J)J
    .locals 5

    .line 1
    new-instance v0, LS5/j;

    .line 2
    .line 3
    const-wide v1, -0x3ffffffffffa14bfL    # -2.0000000001722644

    .line 4
    .line 5
    .line 6
    .line 7
    .line 8
    const-wide v3, 0x3ffffffffffa14bfL    # 1.9999999999138678

    .line 9
    .line 10
    .line 11
    .line 12
    .line 13
    invoke-direct {v0, v1, v2, v3, v4}, LS5/j;-><init>(JJ)V

    .line 14
    .line 15
    .line 16
    invoke-virtual {v0, p0, p1}, LS5/j;->i(J)Z

    .line 17
    .line 18
    .line 19
    move-result v0

    .line 20
    if-eqz v0, :cond_0

    .line 21
    .line 22
    invoke-static {p0, p1}, LW5/c;->l(J)J

    .line 23
    .line 24
    .line 25
    move-result-wide p0

    .line 26
    goto :goto_0

    .line 27
    :cond_0
    invoke-static {p0, p1}, LW5/c;->o(J)J

    .line 28
    .line 29
    .line 30
    move-result-wide p0

    .line 31
    invoke-static {p0, p1}, LW5/c;->j(J)J

    .line 32
    .line 33
    .line 34
    move-result-wide p0

    .line 35
    :goto_0
    return-wide p0
.end method

.method public static final n(J)J
    .locals 2

    .line 1
    const v0, 0xf4240

    .line 2
    .line 3
    .line 4
    int-to-long v0, v0

    .line 5
    mul-long/2addr p0, v0

    .line 6
    return-wide p0
.end method

.method public static final o(J)J
    .locals 2

    .line 1
    const v0, 0xf4240

    .line 2
    .line 3
    .line 4
    int-to-long v0, v0

    .line 5
    div-long/2addr p0, v0

    .line 6
    return-wide p0
.end method

.method public static final p(Ljava/lang/String;Z)J
    .locals 27

    .line 1
    move-object/from16 v6, p0

    .line 2
    .line 3
    invoke-virtual/range {p0 .. p0}, Ljava/lang/String;->length()I

    .line 4
    .line 5
    .line 6
    move-result v7

    .line 7
    if-eqz v7, :cond_22

    .line 8
    .line 9
    sget-object v8, LW5/a;->b:LW5/a$a;

    .line 10
    .line 11
    invoke-virtual {v8}, LW5/a$a;->b()J

    .line 12
    .line 13
    .line 14
    move-result-wide v9

    .line 15
    const/4 v11, 0x0

    .line 16
    invoke-virtual {v6, v11}, Ljava/lang/String;->charAt(I)C

    .line 17
    .line 18
    .line 19
    move-result v0

    .line 20
    const/16 v1, 0x2b

    .line 21
    .line 22
    const/16 v2, 0x2d

    .line 23
    .line 24
    const/4 v12, 0x1

    .line 25
    if-ne v0, v1, :cond_0

    .line 26
    .line 27
    goto :goto_0

    .line 28
    :cond_0
    if-ne v0, v2, :cond_1

    .line 29
    .line 30
    :goto_0
    move v13, v12

    .line 31
    goto :goto_1

    .line 32
    :cond_1
    move v13, v11

    .line 33
    :goto_1
    if-lez v13, :cond_2

    .line 34
    .line 35
    move v14, v12

    .line 36
    goto :goto_2

    .line 37
    :cond_2
    move v14, v11

    .line 38
    :goto_2
    const/4 v0, 0x2

    .line 39
    const/4 v15, 0x0

    .line 40
    if-eqz v14, :cond_3

    .line 41
    .line 42
    invoke-static {v6, v2, v11, v0, v15}, LV5/n;->D0(Ljava/lang/CharSequence;CZILjava/lang/Object;)Z

    .line 43
    .line 44
    .line 45
    move-result v1

    .line 46
    if-eqz v1, :cond_3

    .line 47
    .line 48
    move/from16 v16, v12

    .line 49
    .line 50
    goto :goto_3

    .line 51
    :cond_3
    move/from16 v16, v11

    .line 52
    .line 53
    :goto_3
    const-string v5, "No components"

    .line 54
    .line 55
    if-le v7, v13, :cond_21

    .line 56
    .line 57
    invoke-virtual {v6, v13}, Ljava/lang/String;->charAt(I)C

    .line 58
    .line 59
    .line 60
    move-result v1

    .line 61
    const/16 v2, 0x50

    .line 62
    .line 63
    const-string v4, "Unexpected order of duration components"

    .line 64
    .line 65
    const/16 v3, 0x39

    .line 66
    .line 67
    move-object/from16 v17, v5

    .line 68
    .line 69
    const/16 v5, 0x30

    .line 70
    .line 71
    const-string v0, "substring(...)"

    .line 72
    .line 73
    const-string v11, "null cannot be cast to non-null type java.lang.String"

    .line 74
    .line 75
    if-ne v1, v2, :cond_f

    .line 76
    .line 77
    add-int/2addr v13, v12

    .line 78
    if-eq v13, v7, :cond_e

    .line 79
    .line 80
    move-object v2, v15

    .line 81
    const/4 v1, 0x0

    .line 82
    :goto_4
    if-ge v13, v7, :cond_1e

    .line 83
    .line 84
    invoke-virtual {v6, v13}, Ljava/lang/String;->charAt(I)C

    .line 85
    .line 86
    .line 87
    move-result v8

    .line 88
    const/16 v14, 0x54

    .line 89
    .line 90
    if-ne v8, v14, :cond_5

    .line 91
    .line 92
    if-nez v1, :cond_4

    .line 93
    .line 94
    add-int/lit8 v13, v13, 0x1

    .line 95
    .line 96
    if-eq v13, v7, :cond_4

    .line 97
    .line 98
    move v1, v12

    .line 99
    goto :goto_4

    .line 100
    :cond_4
    new-instance v0, Ljava/lang/IllegalArgumentException;

    .line 101
    .line 102
    invoke-direct {v0}, Ljava/lang/IllegalArgumentException;-><init>()V

    .line 103
    .line 104
    .line 105
    throw v0

    .line 106
    :cond_5
    move v8, v13

    .line 107
    :goto_5
    invoke-virtual/range {p0 .. p0}, Ljava/lang/String;->length()I

    .line 108
    .line 109
    .line 110
    move-result v14

    .line 111
    if-ge v8, v14, :cond_7

    .line 112
    .line 113
    invoke-virtual {v6, v8}, Ljava/lang/String;->charAt(I)C

    .line 114
    .line 115
    .line 116
    move-result v14

    .line 117
    new-instance v12, LS5/c;

    .line 118
    .line 119
    invoke-direct {v12, v5, v3}, LS5/c;-><init>(CC)V

    .line 120
    .line 121
    .line 122
    invoke-virtual {v12, v14}, LS5/c;->i(C)Z

    .line 123
    .line 124
    .line 125
    move-result v12

    .line 126
    if-nez v12, :cond_6

    .line 127
    .line 128
    const-string v12, "+-."

    .line 129
    .line 130
    const/4 v3, 0x0

    .line 131
    const/4 v5, 0x2

    .line 132
    invoke-static {v12, v14, v3, v5, v15}, LV5/n;->M(Ljava/lang/CharSequence;CZILjava/lang/Object;)Z

    .line 133
    .line 134
    .line 135
    move-result v12

    .line 136
    if-eqz v12, :cond_8

    .line 137
    .line 138
    goto :goto_6

    .line 139
    :cond_6
    const/4 v5, 0x2

    .line 140
    :goto_6
    add-int/lit8 v8, v8, 0x1

    .line 141
    .line 142
    const/16 v3, 0x39

    .line 143
    .line 144
    const/16 v5, 0x30

    .line 145
    .line 146
    const/4 v12, 0x1

    .line 147
    goto :goto_5

    .line 148
    :cond_7
    const/4 v5, 0x2

    .line 149
    :cond_8
    invoke-static {v6, v11}, Lkotlin/jvm/internal/r;->d(Ljava/lang/Object;Ljava/lang/String;)V

    .line 150
    .line 151
    .line 152
    invoke-virtual {v6, v13, v8}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    .line 153
    .line 154
    .line 155
    move-result-object v3

    .line 156
    invoke-static {v3, v0}, Lkotlin/jvm/internal/r;->e(Ljava/lang/Object;Ljava/lang/String;)V

    .line 157
    .line 158
    .line 159
    invoke-interface {v3}, Ljava/lang/CharSequence;->length()I

    .line 160
    .line 161
    .line 162
    move-result v8

    .line 163
    if-eqz v8, :cond_d

    .line 164
    .line 165
    invoke-virtual {v3}, Ljava/lang/String;->length()I

    .line 166
    .line 167
    .line 168
    move-result v8

    .line 169
    add-int/2addr v13, v8

    .line 170
    if-ltz v13, :cond_c

    .line 171
    .line 172
    invoke-static/range {p0 .. p0}, LV5/n;->U(Ljava/lang/CharSequence;)I

    .line 173
    .line 174
    .line 175
    move-result v8

    .line 176
    if-gt v13, v8, :cond_c

    .line 177
    .line 178
    invoke-interface {v6, v13}, Ljava/lang/CharSequence;->charAt(I)C

    .line 179
    .line 180
    .line 181
    move-result v8

    .line 182
    add-int/lit8 v13, v13, 0x1

    .line 183
    .line 184
    invoke-static {v8, v1}, LW5/f;->d(CZ)LW5/d;

    .line 185
    .line 186
    .line 187
    move-result-object v8

    .line 188
    if-eqz v2, :cond_a

    .line 189
    .line 190
    invoke-virtual {v2, v8}, Ljava/lang/Enum;->compareTo(Ljava/lang/Enum;)I

    .line 191
    .line 192
    .line 193
    move-result v2

    .line 194
    if-lez v2, :cond_9

    .line 195
    .line 196
    goto :goto_7

    .line 197
    :cond_9
    new-instance v0, Ljava/lang/IllegalArgumentException;

    .line 198
    .line 199
    invoke-direct {v0, v4}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    .line 200
    .line 201
    .line 202
    throw v0

    .line 203
    :cond_a
    :goto_7
    const/16 v24, 0x6

    .line 204
    .line 205
    const/16 v25, 0x0

    .line 206
    .line 207
    const/16 v21, 0x2e

    .line 208
    .line 209
    const/16 v22, 0x0

    .line 210
    .line 211
    const/16 v23, 0x0

    .line 212
    .line 213
    move-object/from16 v20, v3

    .line 214
    .line 215
    invoke-static/range {v20 .. v25}, LV5/n;->Z(Ljava/lang/CharSequence;CIZILjava/lang/Object;)I

    .line 216
    .line 217
    .line 218
    move-result v2

    .line 219
    sget-object v12, LW5/d;->e:LW5/d;

    .line 220
    .line 221
    if-ne v8, v12, :cond_b

    .line 222
    .line 223
    if-lez v2, :cond_b

    .line 224
    .line 225
    invoke-static {v3, v11}, Lkotlin/jvm/internal/r;->d(Ljava/lang/Object;Ljava/lang/String;)V

    .line 226
    .line 227
    .line 228
    const/4 v12, 0x0

    .line 229
    invoke-virtual {v3, v12, v2}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    .line 230
    .line 231
    .line 232
    move-result-object v14

    .line 233
    invoke-static {v14, v0}, Lkotlin/jvm/internal/r;->e(Ljava/lang/Object;Ljava/lang/String;)V

    .line 234
    .line 235
    .line 236
    invoke-static {v14}, LW5/c;->q(Ljava/lang/String;)J

    .line 237
    .line 238
    .line 239
    move-result-wide v5

    .line 240
    invoke-static {v5, v6, v8}, LW5/c;->t(JLW5/d;)J

    .line 241
    .line 242
    .line 243
    move-result-wide v5

    .line 244
    invoke-static {v9, v10, v5, v6}, LW5/a;->C(JJ)J

    .line 245
    .line 246
    .line 247
    move-result-wide v5

    .line 248
    invoke-static {v3, v11}, Lkotlin/jvm/internal/r;->d(Ljava/lang/Object;Ljava/lang/String;)V

    .line 249
    .line 250
    .line 251
    invoke-virtual {v3, v2}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    .line 252
    .line 253
    .line 254
    move-result-object v2

    .line 255
    invoke-static {v2, v0}, Lkotlin/jvm/internal/r;->e(Ljava/lang/Object;Ljava/lang/String;)V

    .line 256
    .line 257
    .line 258
    invoke-static {v2}, Ljava/lang/Double;->parseDouble(Ljava/lang/String;)D

    .line 259
    .line 260
    .line 261
    move-result-wide v2

    .line 262
    invoke-static {v2, v3, v8}, LW5/c;->r(DLW5/d;)J

    .line 263
    .line 264
    .line 265
    move-result-wide v2

    .line 266
    invoke-static {v5, v6, v2, v3}, LW5/a;->C(JJ)J

    .line 267
    .line 268
    .line 269
    move-result-wide v9

    .line 270
    :goto_8
    move-object v2, v8

    .line 271
    const/16 v3, 0x39

    .line 272
    .line 273
    const/16 v5, 0x30

    .line 274
    .line 275
    const/4 v12, 0x1

    .line 276
    move-object/from16 v6, p0

    .line 277
    .line 278
    goto/16 :goto_4

    .line 279
    .line 280
    :cond_b
    invoke-static {v3}, LW5/c;->q(Ljava/lang/String;)J

    .line 281
    .line 282
    .line 283
    move-result-wide v2

    .line 284
    invoke-static {v2, v3, v8}, LW5/c;->t(JLW5/d;)J

    .line 285
    .line 286
    .line 287
    move-result-wide v2

    .line 288
    invoke-static {v9, v10, v2, v3}, LW5/a;->C(JJ)J

    .line 289
    .line 290
    .line 291
    move-result-wide v9

    .line 292
    goto :goto_8

    .line 293
    :cond_c
    new-instance v0, Ljava/lang/IllegalArgumentException;

    .line 294
    .line 295
    new-instance v1, Ljava/lang/StringBuilder;

    .line 296
    .line 297
    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    .line 298
    .line 299
    .line 300
    const-string v2, "Missing unit for value "

    .line 301
    .line 302
    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 303
    .line 304
    .line 305
    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    .line 306
    .line 307
    .line 308
    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    .line 309
    .line 310
    .line 311
    move-result-object v1

    .line 312
    invoke-direct {v0, v1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    .line 313
    .line 314
    .line 315
    throw v0

    .line 316
    :cond_d
    new-instance v0, Ljava/lang/IllegalArgumentException;

    .line 317
    .line 318
    invoke-direct {v0}, Ljava/lang/IllegalArgumentException;-><init>()V

    .line 319
    .line 320
    .line 321
    throw v0

    .line 322
    :cond_e
    new-instance v0, Ljava/lang/IllegalArgumentException;

    .line 323
    .line 324
    invoke-direct {v0}, Ljava/lang/IllegalArgumentException;-><init>()V

    .line 325
    .line 326
    .line 327
    throw v0

    .line 328
    :cond_f
    if-nez p1, :cond_20

    .line 329
    .line 330
    sub-int v1, v7, v13

    .line 331
    .line 332
    const/16 v2, 0x8

    .line 333
    .line 334
    invoke-static {v1, v2}, Ljava/lang/Math;->max(II)I

    .line 335
    .line 336
    .line 337
    move-result v5

    .line 338
    const/4 v6, 0x1

    .line 339
    const-string v2, "Infinity"

    .line 340
    .line 341
    const/4 v3, 0x0

    .line 342
    move-object v12, v0

    .line 343
    move-object/from16 v0, p0

    .line 344
    .line 345
    move v1, v13

    .line 346
    const/16 v15, 0x39

    .line 347
    .line 348
    move-object/from16 v26, v4

    .line 349
    .line 350
    move v4, v5

    .line 351
    move-object/from16 v15, v17

    .line 352
    .line 353
    move-wide/from16 v17, v9

    .line 354
    .line 355
    const/16 v9, 0x30

    .line 356
    .line 357
    move v5, v6

    .line 358
    invoke-static/range {v0 .. v5}, LV5/n;->x(Ljava/lang/String;ILjava/lang/String;IIZ)Z

    .line 359
    .line 360
    .line 361
    move-result v0

    .line 362
    if-eqz v0, :cond_10

    .line 363
    .line 364
    invoke-virtual {v8}, LW5/a$a;->a()J

    .line 365
    .line 366
    .line 367
    move-result-wide v9

    .line 368
    goto/16 :goto_10

    .line 369
    .line 370
    :cond_10
    xor-int/lit8 v0, v14, 0x1

    .line 371
    .line 372
    move-object/from16 v1, p0

    .line 373
    .line 374
    if-eqz v14, :cond_12

    .line 375
    .line 376
    invoke-virtual {v1, v13}, Ljava/lang/String;->charAt(I)C

    .line 377
    .line 378
    .line 379
    move-result v2

    .line 380
    const/16 v3, 0x28

    .line 381
    .line 382
    if-ne v2, v3, :cond_12

    .line 383
    .line 384
    invoke-static/range {p0 .. p0}, LV5/n;->Z0(Ljava/lang/CharSequence;)C

    .line 385
    .line 386
    .line 387
    move-result v2

    .line 388
    const/16 v3, 0x29

    .line 389
    .line 390
    if-ne v2, v3, :cond_12

    .line 391
    .line 392
    add-int/lit8 v13, v13, 0x1

    .line 393
    .line 394
    add-int/lit8 v7, v7, -0x1

    .line 395
    .line 396
    if-eq v13, v7, :cond_11

    .line 397
    .line 398
    move-wide/from16 v4, v17

    .line 399
    .line 400
    const/4 v0, 0x1

    .line 401
    :goto_9
    const/4 v3, 0x0

    .line 402
    const/4 v15, 0x0

    .line 403
    goto :goto_a

    .line 404
    :cond_11
    new-instance v0, Ljava/lang/IllegalArgumentException;

    .line 405
    .line 406
    invoke-direct {v0, v15}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    .line 407
    .line 408
    .line 409
    throw v0

    .line 410
    :cond_12
    move-wide/from16 v4, v17

    .line 411
    .line 412
    goto :goto_9

    .line 413
    :goto_a
    if-ge v13, v7, :cond_1d

    .line 414
    .line 415
    if-eqz v3, :cond_13

    .line 416
    .line 417
    if-eqz v0, :cond_13

    .line 418
    .line 419
    :goto_b
    invoke-virtual/range {p0 .. p0}, Ljava/lang/String;->length()I

    .line 420
    .line 421
    .line 422
    move-result v2

    .line 423
    if-ge v13, v2, :cond_13

    .line 424
    .line 425
    invoke-virtual {v1, v13}, Ljava/lang/String;->charAt(I)C

    .line 426
    .line 427
    .line 428
    move-result v2

    .line 429
    const/16 v3, 0x20

    .line 430
    .line 431
    if-ne v2, v3, :cond_13

    .line 432
    .line 433
    add-int/lit8 v13, v13, 0x1

    .line 434
    .line 435
    goto :goto_b

    .line 436
    :cond_13
    move v2, v13

    .line 437
    :goto_c
    invoke-virtual/range {p0 .. p0}, Ljava/lang/String;->length()I

    .line 438
    .line 439
    .line 440
    move-result v3

    .line 441
    if-ge v2, v3, :cond_15

    .line 442
    .line 443
    invoke-virtual {v1, v2}, Ljava/lang/String;->charAt(I)C

    .line 444
    .line 445
    .line 446
    move-result v3

    .line 447
    new-instance v6, LS5/c;

    .line 448
    .line 449
    const/16 v8, 0x39

    .line 450
    .line 451
    invoke-direct {v6, v9, v8}, LS5/c;-><init>(CC)V

    .line 452
    .line 453
    .line 454
    invoke-virtual {v6, v3}, LS5/c;->i(C)Z

    .line 455
    .line 456
    .line 457
    move-result v6

    .line 458
    if-nez v6, :cond_14

    .line 459
    .line 460
    const/16 v6, 0x2e

    .line 461
    .line 462
    if-ne v3, v6, :cond_16

    .line 463
    .line 464
    :cond_14
    add-int/lit8 v2, v2, 0x1

    .line 465
    .line 466
    goto :goto_c

    .line 467
    :cond_15
    const/16 v8, 0x39

    .line 468
    .line 469
    :cond_16
    invoke-static {v1, v11}, Lkotlin/jvm/internal/r;->d(Ljava/lang/Object;Ljava/lang/String;)V

    .line 470
    .line 471
    .line 472
    invoke-virtual {v1, v13, v2}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    .line 473
    .line 474
    .line 475
    move-result-object v2

    .line 476
    invoke-static {v2, v12}, Lkotlin/jvm/internal/r;->e(Ljava/lang/Object;Ljava/lang/String;)V

    .line 477
    .line 478
    .line 479
    invoke-interface {v2}, Ljava/lang/CharSequence;->length()I

    .line 480
    .line 481
    .line 482
    move-result v3

    .line 483
    if-eqz v3, :cond_1c

    .line 484
    .line 485
    invoke-virtual {v2}, Ljava/lang/String;->length()I

    .line 486
    .line 487
    .line 488
    move-result v3

    .line 489
    add-int/2addr v13, v3

    .line 490
    move v3, v13

    .line 491
    :goto_d
    invoke-virtual/range {p0 .. p0}, Ljava/lang/String;->length()I

    .line 492
    .line 493
    .line 494
    move-result v6

    .line 495
    if-ge v3, v6, :cond_17

    .line 496
    .line 497
    invoke-virtual {v1, v3}, Ljava/lang/String;->charAt(I)C

    .line 498
    .line 499
    .line 500
    move-result v6

    .line 501
    new-instance v10, LS5/c;

    .line 502
    .line 503
    const/16 v14, 0x61

    .line 504
    .line 505
    const/16 v8, 0x7a

    .line 506
    .line 507
    invoke-direct {v10, v14, v8}, LS5/c;-><init>(CC)V

    .line 508
    .line 509
    .line 510
    invoke-virtual {v10, v6}, LS5/c;->i(C)Z

    .line 511
    .line 512
    .line 513
    move-result v6

    .line 514
    if-eqz v6, :cond_17

    .line 515
    .line 516
    add-int/lit8 v3, v3, 0x1

    .line 517
    .line 518
    const/16 v8, 0x39

    .line 519
    .line 520
    goto :goto_d

    .line 521
    :cond_17
    invoke-static {v1, v11}, Lkotlin/jvm/internal/r;->d(Ljava/lang/Object;Ljava/lang/String;)V

    .line 522
    .line 523
    .line 524
    invoke-virtual {v1, v13, v3}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    .line 525
    .line 526
    .line 527
    move-result-object v3

    .line 528
    invoke-static {v3, v12}, Lkotlin/jvm/internal/r;->e(Ljava/lang/Object;Ljava/lang/String;)V

    .line 529
    .line 530
    .line 531
    invoke-virtual {v3}, Ljava/lang/String;->length()I

    .line 532
    .line 533
    .line 534
    move-result v6

    .line 535
    add-int/2addr v13, v6

    .line 536
    invoke-static {v3}, LW5/f;->e(Ljava/lang/String;)LW5/d;

    .line 537
    .line 538
    .line 539
    move-result-object v3

    .line 540
    if-eqz v15, :cond_18

    .line 541
    .line 542
    invoke-virtual {v15, v3}, Ljava/lang/Enum;->compareTo(Ljava/lang/Enum;)I

    .line 543
    .line 544
    .line 545
    move-result v6

    .line 546
    if-lez v6, :cond_19

    .line 547
    .line 548
    :cond_18
    move-object/from16 v6, v26

    .line 549
    .line 550
    goto :goto_e

    .line 551
    :cond_19
    new-instance v0, Ljava/lang/IllegalArgumentException;

    .line 552
    .line 553
    move-object/from16 v6, v26

    .line 554
    .line 555
    invoke-direct {v0, v6}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    .line 556
    .line 557
    .line 558
    throw v0

    .line 559
    :goto_e
    const/16 v23, 0x6

    .line 560
    .line 561
    const/16 v24, 0x0

    .line 562
    .line 563
    const/16 v20, 0x2e

    .line 564
    .line 565
    const/16 v21, 0x0

    .line 566
    .line 567
    const/16 v22, 0x0

    .line 568
    .line 569
    move-object/from16 v19, v2

    .line 570
    .line 571
    invoke-static/range {v19 .. v24}, LV5/n;->Z(Ljava/lang/CharSequence;CIZILjava/lang/Object;)I

    .line 572
    .line 573
    .line 574
    move-result v8

    .line 575
    if-lez v8, :cond_1b

    .line 576
    .line 577
    invoke-static {v2, v11}, Lkotlin/jvm/internal/r;->d(Ljava/lang/Object;Ljava/lang/String;)V

    .line 578
    .line 579
    .line 580
    const/4 v10, 0x0

    .line 581
    invoke-virtual {v2, v10, v8}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    .line 582
    .line 583
    .line 584
    move-result-object v14

    .line 585
    invoke-static {v14, v12}, Lkotlin/jvm/internal/r;->e(Ljava/lang/Object;Ljava/lang/String;)V

    .line 586
    .line 587
    .line 588
    invoke-static {v14}, Ljava/lang/Long;->parseLong(Ljava/lang/String;)J

    .line 589
    .line 590
    .line 591
    move-result-wide v14

    .line 592
    invoke-static {v14, v15, v3}, LW5/c;->t(JLW5/d;)J

    .line 593
    .line 594
    .line 595
    move-result-wide v14

    .line 596
    invoke-static {v4, v5, v14, v15}, LW5/a;->C(JJ)J

    .line 597
    .line 598
    .line 599
    move-result-wide v4

    .line 600
    invoke-static {v2, v11}, Lkotlin/jvm/internal/r;->d(Ljava/lang/Object;Ljava/lang/String;)V

    .line 601
    .line 602
    .line 603
    invoke-virtual {v2, v8}, Ljava/lang/String;->substring(I)Ljava/lang/String;

    .line 604
    .line 605
    .line 606
    move-result-object v2

    .line 607
    invoke-static {v2, v12}, Lkotlin/jvm/internal/r;->e(Ljava/lang/Object;Ljava/lang/String;)V

    .line 608
    .line 609
    .line 610
    invoke-static {v2}, Ljava/lang/Double;->parseDouble(Ljava/lang/String;)D

    .line 611
    .line 612
    .line 613
    move-result-wide v14

    .line 614
    invoke-static {v14, v15, v3}, LW5/c;->r(DLW5/d;)J

    .line 615
    .line 616
    .line 617
    move-result-wide v14

    .line 618
    invoke-static {v4, v5, v14, v15}, LW5/a;->C(JJ)J

    .line 619
    .line 620
    .line 621
    move-result-wide v4

    .line 622
    if-lt v13, v7, :cond_1a

    .line 623
    .line 624
    :goto_f
    move-object v15, v3

    .line 625
    move-object/from16 v26, v6

    .line 626
    .line 627
    const/4 v3, 0x1

    .line 628
    goto/16 :goto_a

    .line 629
    .line 630
    :cond_1a
    new-instance v0, Ljava/lang/IllegalArgumentException;

    .line 631
    .line 632
    const-string v1, "Fractional component must be last"

    .line 633
    .line 634
    invoke-direct {v0, v1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    .line 635
    .line 636
    .line 637
    throw v0

    .line 638
    :cond_1b
    const/4 v10, 0x0

    .line 639
    invoke-static {v2}, Ljava/lang/Long;->parseLong(Ljava/lang/String;)J

    .line 640
    .line 641
    .line 642
    move-result-wide v14

    .line 643
    invoke-static {v14, v15, v3}, LW5/c;->t(JLW5/d;)J

    .line 644
    .line 645
    .line 646
    move-result-wide v14

    .line 647
    invoke-static {v4, v5, v14, v15}, LW5/a;->C(JJ)J

    .line 648
    .line 649
    .line 650
    move-result-wide v4

    .line 651
    goto :goto_f

    .line 652
    :cond_1c
    new-instance v0, Ljava/lang/IllegalArgumentException;

    .line 653
    .line 654
    invoke-direct {v0}, Ljava/lang/IllegalArgumentException;-><init>()V

    .line 655
    .line 656
    .line 657
    throw v0

    .line 658
    :cond_1d
    move-wide v9, v4

    .line 659
    :cond_1e
    :goto_10
    if-eqz v16, :cond_1f

    .line 660
    .line 661
    invoke-static {v9, v10}, LW5/a;->H(J)J

    .line 662
    .line 663
    .line 664
    move-result-wide v9

    .line 665
    :cond_1f
    return-wide v9

    .line 666
    :cond_20
    new-instance v0, Ljava/lang/IllegalArgumentException;

    .line 667
    .line 668
    invoke-direct {v0}, Ljava/lang/IllegalArgumentException;-><init>()V

    .line 669
    .line 670
    .line 671
    throw v0

    .line 672
    :cond_21
    move-object v15, v5

    .line 673
    new-instance v0, Ljava/lang/IllegalArgumentException;

    .line 674
    .line 675
    invoke-direct {v0, v15}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    .line 676
    .line 677
    .line 678
    throw v0

    .line 679
    :cond_22
    new-instance v0, Ljava/lang/IllegalArgumentException;

    .line 680
    .line 681
    const-string v1, "The string is empty"

    .line 682
    .line 683
    invoke-direct {v0, v1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    .line 684
    .line 685
    .line 686
    throw v0
.end method

.method public static final q(Ljava/lang/String;)J
    .locals 9

    .line 1
    invoke-virtual {p0}, Ljava/lang/String;->length()I

    .line 2
    .line 3
    .line 4
    move-result v0

    .line 5
    const/4 v1, 0x1

    .line 6
    const/4 v2, 0x0

    .line 7
    const/4 v3, 0x2

    .line 8
    const/4 v4, 0x0

    .line 9
    if-lez v0, :cond_0

    .line 10
    .line 11
    const-string v5, "+-"

    .line 12
    .line 13
    invoke-virtual {p0, v4}, Ljava/lang/String;->charAt(I)C

    .line 14
    .line 15
    .line 16
    move-result v6

    .line 17
    invoke-static {v5, v6, v4, v3, v2}, LV5/n;->M(Ljava/lang/CharSequence;CZILjava/lang/Object;)Z

    .line 18
    .line 19
    .line 20
    move-result v5

    .line 21
    if-eqz v5, :cond_0

    .line 22
    .line 23
    move v5, v1

    .line 24
    goto :goto_0

    .line 25
    :cond_0
    move v5, v4

    .line 26
    :goto_0
    sub-int/2addr v0, v5

    .line 27
    const/16 v6, 0x10

    .line 28
    .line 29
    if-le v0, v6, :cond_5

    .line 30
    .line 31
    new-instance v0, LS5/g;

    .line 32
    .line 33
    invoke-static {p0}, LV5/n;->U(Ljava/lang/CharSequence;)I

    .line 34
    .line 35
    .line 36
    move-result v6

    .line 37
    invoke-direct {v0, v5, v6}, LS5/g;-><init>(II)V

    .line 38
    .line 39
    .line 40
    instance-of v5, v0, Ljava/util/Collection;

    .line 41
    .line 42
    if-eqz v5, :cond_1

    .line 43
    .line 44
    move-object v5, v0

    .line 45
    check-cast v5, Ljava/util/Collection;

    .line 46
    .line 47
    invoke-interface {v5}, Ljava/util/Collection;->isEmpty()Z

    .line 48
    .line 49
    .line 50
    move-result v5

    .line 51
    if-eqz v5, :cond_1

    .line 52
    .line 53
    goto :goto_1

    .line 54
    :cond_1
    invoke-interface {v0}, Ljava/lang/Iterable;->iterator()Ljava/util/Iterator;

    .line 55
    .line 56
    .line 57
    move-result-object v0

    .line 58
    :cond_2
    invoke-interface {v0}, Ljava/util/Iterator;->hasNext()Z

    .line 59
    .line 60
    .line 61
    move-result v5

    .line 62
    if-eqz v5, :cond_3

    .line 63
    .line 64
    move-object v5, v0

    .line 65
    check-cast v5, Lz5/H;

    .line 66
    .line 67
    invoke-virtual {v5}, Lz5/H;->a()I

    .line 68
    .line 69
    .line 70
    move-result v5

    .line 71
    new-instance v6, LS5/c;

    .line 72
    .line 73
    const/16 v7, 0x30

    .line 74
    .line 75
    const/16 v8, 0x39

    .line 76
    .line 77
    invoke-direct {v6, v7, v8}, LS5/c;-><init>(CC)V

    .line 78
    .line 79
    .line 80
    invoke-virtual {p0, v5}, Ljava/lang/String;->charAt(I)C

    .line 81
    .line 82
    .line 83
    move-result v5

    .line 84
    invoke-virtual {v6, v5}, LS5/c;->i(C)Z

    .line 85
    .line 86
    .line 87
    move-result v5

    .line 88
    if-nez v5, :cond_2

    .line 89
    .line 90
    goto :goto_3

    .line 91
    :cond_3
    :goto_1
    invoke-virtual {p0, v4}, Ljava/lang/String;->charAt(I)C

    .line 92
    .line 93
    .line 94
    move-result p0

    .line 95
    const/16 v0, 0x2d

    .line 96
    .line 97
    if-ne p0, v0, :cond_4

    .line 98
    .line 99
    const-wide/high16 v0, -0x8000000000000000L

    .line 100
    .line 101
    goto :goto_2

    .line 102
    :cond_4
    const-wide v0, 0x7fffffffffffffffL

    .line 103
    .line 104
    .line 105
    .line 106
    .line 107
    :goto_2
    return-wide v0

    .line 108
    :cond_5
    :goto_3
    const-string v0, "+"

    .line 109
    .line 110
    invoke-static {p0, v0, v4, v3, v2}, LV5/n;->I(Ljava/lang/String;Ljava/lang/String;ZILjava/lang/Object;)Z

    .line 111
    .line 112
    .line 113
    move-result v0

    .line 114
    if-eqz v0, :cond_6

    .line 115
    .line 116
    invoke-static {p0, v1}, LV5/n;->W0(Ljava/lang/String;I)Ljava/lang/String;

    .line 117
    .line 118
    .line 119
    move-result-object p0

    .line 120
    :cond_6
    invoke-static {p0}, Ljava/lang/Long;->parseLong(Ljava/lang/String;)J

    .line 121
    .line 122
    .line 123
    move-result-wide v0

    .line 124
    return-wide v0
.end method

.method public static final r(DLW5/d;)J
    .locals 7

    .line 1
    const-string v0, "unit"

    .line 2
    .line 3
    invoke-static {p2, v0}, Lkotlin/jvm/internal/r;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    sget-object v0, LW5/d;->b:LW5/d;

    .line 7
    .line 8
    invoke-static {p0, p1, p2, v0}, LW5/e;->a(DLW5/d;LW5/d;)D

    .line 9
    .line 10
    .line 11
    move-result-wide v0

    .line 12
    invoke-static {v0, v1}, Ljava/lang/Double;->isNaN(D)Z

    .line 13
    .line 14
    .line 15
    move-result v2

    .line 16
    xor-int/lit8 v2, v2, 0x1

    .line 17
    .line 18
    if-eqz v2, :cond_1

    .line 19
    .line 20
    invoke-static {v0, v1}, LO5/a;->d(D)J

    .line 21
    .line 22
    .line 23
    move-result-wide v0

    .line 24
    new-instance v2, LS5/j;

    .line 25
    .line 26
    const-wide v3, -0x3ffffffffffa14bfL    # -2.0000000001722644

    .line 27
    .line 28
    .line 29
    .line 30
    .line 31
    const-wide v5, 0x3ffffffffffa14bfL    # 1.9999999999138678

    .line 32
    .line 33
    .line 34
    .line 35
    .line 36
    invoke-direct {v2, v3, v4, v5, v6}, LS5/j;-><init>(JJ)V

    .line 37
    .line 38
    .line 39
    invoke-virtual {v2, v0, v1}, LS5/j;->i(J)Z

    .line 40
    .line 41
    .line 42
    move-result v2

    .line 43
    if-eqz v2, :cond_0

    .line 44
    .line 45
    invoke-static {v0, v1}, LW5/c;->l(J)J

    .line 46
    .line 47
    .line 48
    move-result-wide p0

    .line 49
    goto :goto_0

    .line 50
    :cond_0
    sget-object v0, LW5/d;->d:LW5/d;

    .line 51
    .line 52
    invoke-static {p0, p1, p2, v0}, LW5/e;->a(DLW5/d;LW5/d;)D

    .line 53
    .line 54
    .line 55
    move-result-wide p0

    .line 56
    invoke-static {p0, p1}, LO5/a;->d(D)J

    .line 57
    .line 58
    .line 59
    move-result-wide p0

    .line 60
    invoke-static {p0, p1}, LW5/c;->k(J)J

    .line 61
    .line 62
    .line 63
    move-result-wide p0

    .line 64
    :goto_0
    return-wide p0

    .line 65
    :cond_1
    new-instance p0, Ljava/lang/IllegalArgumentException;

    .line 66
    .line 67
    const-string p1, "Duration value cannot be NaN."

    .line 68
    .line 69
    invoke-virtual {p1}, Ljava/lang/Object;->toString()Ljava/lang/String;

    .line 70
    .line 71
    .line 72
    move-result-object p1

    .line 73
    invoke-direct {p0, p1}, Ljava/lang/IllegalArgumentException;-><init>(Ljava/lang/String;)V

    .line 74
    .line 75
    .line 76
    throw p0
.end method

.method public static final s(ILW5/d;)J
    .locals 2

    .line 1
    const-string v0, "unit"

    .line 2
    .line 3
    invoke-static {p1, v0}, Lkotlin/jvm/internal/r;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    sget-object v0, LW5/d;->e:LW5/d;

    .line 7
    .line 8
    invoke-virtual {p1, v0}, Ljava/lang/Enum;->compareTo(Ljava/lang/Enum;)I

    .line 9
    .line 10
    .line 11
    move-result v0

    .line 12
    if-gtz v0, :cond_0

    .line 13
    .line 14
    int-to-long v0, p0

    .line 15
    sget-object p0, LW5/d;->b:LW5/d;

    .line 16
    .line 17
    invoke-static {v0, v1, p1, p0}, LW5/e;->c(JLW5/d;LW5/d;)J

    .line 18
    .line 19
    .line 20
    move-result-wide p0

    .line 21
    invoke-static {p0, p1}, LW5/c;->l(J)J

    .line 22
    .line 23
    .line 24
    move-result-wide p0

    .line 25
    goto :goto_0

    .line 26
    :cond_0
    int-to-long v0, p0

    .line 27
    invoke-static {v0, v1, p1}, LW5/c;->t(JLW5/d;)J

    .line 28
    .line 29
    .line 30
    move-result-wide p0

    .line 31
    :goto_0
    return-wide p0
.end method

.method public static final t(JLW5/d;)J
    .locals 7

    .line 1
    const-string v0, "unit"

    .line 2
    .line 3
    invoke-static {p2, v0}, Lkotlin/jvm/internal/r;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    sget-object v0, LW5/d;->b:LW5/d;

    .line 7
    .line 8
    const-wide v1, 0x3ffffffffffa14bfL    # 1.9999999999138678

    .line 9
    .line 10
    .line 11
    .line 12
    .line 13
    invoke-static {v1, v2, v0, p2}, LW5/e;->c(JLW5/d;LW5/d;)J

    .line 14
    .line 15
    .line 16
    move-result-wide v1

    .line 17
    new-instance v3, LS5/j;

    .line 18
    .line 19
    neg-long v4, v1

    .line 20
    invoke-direct {v3, v4, v5, v1, v2}, LS5/j;-><init>(JJ)V

    .line 21
    .line 22
    .line 23
    invoke-virtual {v3, p0, p1}, LS5/j;->i(J)Z

    .line 24
    .line 25
    .line 26
    move-result v1

    .line 27
    if-eqz v1, :cond_0

    .line 28
    .line 29
    invoke-static {p0, p1, p2, v0}, LW5/e;->c(JLW5/d;LW5/d;)J

    .line 30
    .line 31
    .line 32
    move-result-wide p0

    .line 33
    invoke-static {p0, p1}, LW5/c;->l(J)J

    .line 34
    .line 35
    .line 36
    move-result-wide p0

    .line 37
    return-wide p0

    .line 38
    :cond_0
    sget-object v0, LW5/d;->d:LW5/d;

    .line 39
    .line 40
    invoke-static {p0, p1, p2, v0}, LW5/e;->b(JLW5/d;LW5/d;)J

    .line 41
    .line 42
    .line 43
    move-result-wide v1

    .line 44
    const-wide v3, -0x3fffffffffffffffL    # -2.0000000000000004

    .line 45
    .line 46
    .line 47
    .line 48
    .line 49
    const-wide v5, 0x3fffffffffffffffL    # 1.9999999999999998

    .line 50
    .line 51
    .line 52
    .line 53
    .line 54
    invoke-static/range {v1 .. v6}, LS5/k;->g(JJJ)J

    .line 55
    .line 56
    .line 57
    move-result-wide p0

    .line 58
    invoke-static {p0, p1}, LW5/c;->j(J)J

    .line 59
    .line 60
    .line 61
    move-result-wide p0

    .line 62
    return-wide p0
.end method
