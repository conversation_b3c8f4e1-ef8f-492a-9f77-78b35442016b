.class public abstract LU5/g;
.super LU5/l;
.source "SourceFile"


# direct methods
.method public static bridge synthetic a(Ljava/util/Iterator;)LU5/f;
    .locals 0

    .line 1
    invoke-static {p0}, LU5/j;->a(Ljava/util/Iterator;)LU5/f;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static bridge synthetic b(LU5/f;)LU5/f;
    .locals 0

    .line 1
    invoke-static {p0}, LU5/j;->b(LU5/f;)LU5/f;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static bridge synthetic c()LU5/f;
    .locals 1

    .line 1
    invoke-static {}, LU5/j;->c()LU5/f;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method

.method public static bridge synthetic d(Lkotlin/jvm/functions/Function0;LM5/k;)LU5/f;
    .locals 0

    .line 1
    invoke-static {p0, p1}, LU5/j;->d(<PERSON><PERSON><PERSON>/jvm/functions/Function0;LM5/k;)LU5/f;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static bridge synthetic e(LU5/f;)Ljava/lang/Iterable;
    .locals 0

    .line 1
    invoke-static {p0}, LU5/l;->e(LU5/f;)Ljava/lang/Iterable;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static bridge synthetic f(LU5/f;I)LU5/f;
    .locals 0

    .line 1
    invoke-static {p0, p1}, LU5/l;->f(LU5/f;I)LU5/f;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static bridge synthetic i(LU5/f;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;ILjava/lang/CharSequence;LM5/k;ILjava/lang/Object;)Ljava/lang/String;
    .locals 0

    .line 1
    invoke-static/range {p0 .. p8}, LU5/l;->i(LU5/f;Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;ILjava/lang/CharSequence;LM5/k;ILjava/lang/Object;)Ljava/lang/String;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static bridge synthetic j(LU5/f;LM5/k;)LU5/f;
    .locals 0

    .line 1
    invoke-static {p0, p1}, LU5/l;->j(LU5/f;LM5/k;)LU5/f;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static bridge synthetic k(LU5/f;I)LU5/f;
    .locals 0

    .line 1
    invoke-static {p0, p1}, LU5/l;->k(LU5/f;I)LU5/f;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static bridge synthetic l(LU5/f;)Ljava/util/List;
    .locals 0

    .line 1
    invoke-static {p0}, LU5/l;->l(LU5/f;)Ljava/util/List;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method
