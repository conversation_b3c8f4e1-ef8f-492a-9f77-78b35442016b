.class public final LZ5/b$c;
.super LF5/d;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = LZ5/b;->t0(LZ5/b;LD5/d;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation


# instance fields
.field public synthetic a:Ljava/lang/Object;

.field public final synthetic b:LZ5/b;

.field public c:I


# direct methods
.method public constructor <init>(LZ5/b;LD5/d;)V
    .locals 0

    .line 1
    iput-object p1, p0, LZ5/b$c;->b:LZ5/b;

    .line 2
    .line 3
    invoke-direct {p0, p2}, LF5/d;-><init>(LD5/d;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 1

    .line 1
    iput-object p1, p0, LZ5/b$c;->a:Ljava/lang/Object;

    .line 2
    .line 3
    iget p1, p0, LZ5/b$c;->c:I

    .line 4
    .line 5
    const/high16 v0, -0x80000000

    .line 6
    .line 7
    or-int/2addr p1, v0

    .line 8
    iput p1, p0, LZ5/b$c;->c:I

    .line 9
    .line 10
    iget-object p1, p0, LZ5/b$c;->b:LZ5/b;

    .line 11
    .line 12
    invoke-static {p1, p0}, LZ5/b;->t0(LZ5/b;LD5/d;)Ljava/lang/Object;

    .line 13
    .line 14
    .line 15
    move-result-object p1

    .line 16
    invoke-static {}, LE5/b;->e()Ljava/lang/Object;

    .line 17
    .line 18
    .line 19
    move-result-object v0

    .line 20
    if-ne p1, v0, :cond_0

    .line 21
    .line 22
    return-object p1

    .line 23
    :cond_0
    invoke-static {p1}, LZ5/h;->b(Ljava/lang/Object;)LZ5/h;

    .line 24
    .line 25
    .line 26
    move-result-object p1

    .line 27
    return-object p1
.end method
