part of 'helpers.dart';

const String kAppName = "AZUL IPTV";

//TODO: SHow Ads ( true / false )
const bool showAds = true;

const String kIconLive = "assets/images/live-stream.png";
const String kIconSeries = "assets/images/clapperboard.png";
const String kIconMovies = "assets/images/film-reel.png";
const String kIconSplash = "assets/images/icon.png";
const String kImageIntro = "assets/images/intro h.jpeg";

const String kPrivacy = "https://www.whmcssmarters.com/terms-of-service/";
const String kContact = "https://mouadzizi.me";

const double sizeTablet = 950;

enum TypeCategory {
  all,
  live,
  movies,
  series,
}

Size getSize(BuildContext context) => MediaQuery.of(context).size;

bool isTv(BuildContext context) {
  return MediaQuery.of(context).size.width > sizeTablet;
}
