.class public LX5/S;
.super LX5/a;
.source "SourceFile"

# interfaces
.implements LX5/Q;


# direct methods
.method public constructor <init>(LD5/g;Z)V
    .locals 1

    .line 1
    const/4 v0, 0x1

    .line 2
    invoke-direct {p0, p1, v0, p2}, LX5/a;-><init>(LD5/g;ZZ)V

    .line 3
    .line 4
    .line 5
    return-void
.end method


# virtual methods
.method public l()Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, LX5/A0;->X()Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method
