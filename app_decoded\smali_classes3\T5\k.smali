.class public interface abstract LT5/k;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LT5/l;
.implements LM5/o;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LT5/k$a;
    }
.end annotation


# virtual methods
.method public abstract b()LT5/k$a;
.end method

.method public abstract l(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
.end method
