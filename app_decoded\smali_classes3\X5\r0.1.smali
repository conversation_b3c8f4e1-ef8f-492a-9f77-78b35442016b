.class public final LX5/r0;
.super LX5/z0;
.source "SourceFile"


# instance fields
.field public final e:LM5/k;


# direct methods
.method public constructor <init>(LM5/k;)V
    .locals 0

    .line 1
    invoke-direct {p0}, LX5/z0;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LX5/r0;->e:LM5/k;

    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public bridge synthetic invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Ljava/lang/Throwable;

    .line 2
    .line 3
    invoke-virtual {p0, p1}, LX5/r0;->v(Ljava/lang/Throwable;)V

    .line 4
    .line 5
    .line 6
    sget-object p1, Ly5/I;->a:Ly5/I;

    .line 7
    .line 8
    return-object p1
.end method

.method public v(Ljava/lang/Throwable;)V
    .locals 1

    .line 1
    iget-object v0, p0, LX5/r0;->e:LM5/k;

    .line 2
    .line 3
    invoke-interface {v0, p1}, LM5/k;->invoke(Ljava/lang/Object;)Lja<PERSON>/lang/Object;

    .line 4
    .line 5
    .line 6
    return-void
.end method
