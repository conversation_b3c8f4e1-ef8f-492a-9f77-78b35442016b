.class public abstract LV5/a;
.super LV5/c;
.source "SourceFile"


# direct methods
.method public static bridge synthetic a(I)I
    .locals 0

    .line 1
    invoke-static {p0}, LV5/b;->a(I)I

    .line 2
    .line 3
    .line 4
    move-result p0

    .line 5
    return p0
.end method

.method public static bridge synthetic e(C)Ljava/lang/String;
    .locals 0

    .line 1
    invoke-static {p0}, LV5/c;->e(C)Ljava/lang/String;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method
