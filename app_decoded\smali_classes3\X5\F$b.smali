.class public final LX5/F$b;
.super Lkotlin/jvm/internal/s;
.source "SourceFile"

# interfaces
.implements LM5/o;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = LX5/F;->a(LD5/g;LD5/g;Z)LD5/g;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation


# instance fields
.field public final synthetic a:Lkotlin/jvm/internal/I;

.field public final synthetic b:Z


# direct methods
.method public constructor <init>(Lkotlin/jvm/internal/I;Z)V
    .locals 0

    .line 1
    iput-object p1, p0, LX5/F$b;->a:Lkotlin/jvm/internal/I;

    .line 2
    .line 3
    iput-boolean p2, p0, LX5/F$b;->b:Z

    .line 4
    .line 5
    const/4 p1, 0x2

    .line 6
    invoke-direct {p0, p1}, Lkotlin/jvm/internal/s;-><init>(I)V

    .line 7
    .line 8
    .line 9
    return-void
.end method


# virtual methods
.method public final a(LD5/g;LD5/g$b;)LD5/g;
    .locals 0

    .line 1
    invoke-interface {p1, p2}, LD5/g;->D0(LD5/g;)LD5/g;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, LD5/g;

    .line 2
    .line 3
    check-cast p2, LD5/g$b;

    .line 4
    .line 5
    invoke-virtual {p0, p1, p2}, LX5/F$b;->a(LD5/g;LD5/g$b;)LD5/g;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    return-object p1
.end method
