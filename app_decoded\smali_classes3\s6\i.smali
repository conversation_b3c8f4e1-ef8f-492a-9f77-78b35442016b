.class public final Ls6/i;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Ls6/i$b;
    }
.end annotation


# static fields
.field public static final A:Ls6/i;

.field public static final A0:Ls6/i;

.field public static final B:Ls6/i;

.field public static final B0:Ls6/i;

.field public static final C:Ls6/i;

.field public static final C0:Ls6/i;

.field public static final D:Ls6/i;

.field public static final D0:Ls6/i;

.field public static final E:Ls6/i;

.field public static final E0:Ls6/i;

.field public static final F:Ls6/i;

.field public static final F0:Ls6/i;

.field public static final G:Ls6/i;

.field public static final G0:Ls6/i;

.field public static final H:Ls6/i;

.field public static final H0:Ls6/i;

.field public static final I:Ls6/i;

.field public static final I0:Ls6/i;

.field public static final J:Ls6/i;

.field public static final J0:Ls6/i;

.field public static final K:Ls6/i;

.field public static final K0:Ls6/i;

.field public static final L:Ls6/i;

.field public static final L0:Ls6/i;

.field public static final M:Ls6/i;

.field public static final M0:Ls6/i;

.field public static final N:Ls6/i;

.field public static final N0:Ls6/i;

.field public static final O:Ls6/i;

.field public static final O0:Ls6/i;

.field public static final P:Ls6/i;

.field public static final P0:Ls6/i;

.field public static final Q:Ls6/i;

.field public static final Q0:Ls6/i;

.field public static final R:Ls6/i;

.field public static final R0:Ls6/i;

.field public static final S:Ls6/i;

.field public static final S0:Ls6/i;

.field public static final T:Ls6/i;

.field public static final T0:Ls6/i;

.field public static final U:Ls6/i;

.field public static final U0:Ls6/i;

.field public static final V:Ls6/i;

.field public static final V0:Ls6/i;

.field public static final W:Ls6/i;

.field public static final W0:Ls6/i;

.field public static final X:Ls6/i;

.field public static final X0:Ls6/i;

.field public static final Y:Ls6/i;

.field public static final Y0:Ls6/i;

.field public static final Z:Ls6/i;

.field public static final Z0:Ls6/i;

.field public static final a0:Ls6/i;

.field public static final a1:Ls6/i;

.field public static final b:Ls6/i$b;

.field public static final b0:Ls6/i;

.field public static final b1:Ls6/i;

.field public static final c:Ljava/util/Comparator;

.field public static final c0:Ls6/i;

.field public static final c1:Ls6/i;

.field public static final d:Ljava/util/Map;

.field public static final d0:Ls6/i;

.field public static final d1:Ls6/i;

.field public static final e:Ls6/i;

.field public static final e0:Ls6/i;

.field public static final e1:Ls6/i;

.field public static final f:Ls6/i;

.field public static final f0:Ls6/i;

.field public static final f1:Ls6/i;

.field public static final g:Ls6/i;

.field public static final g0:Ls6/i;

.field public static final g1:Ls6/i;

.field public static final h:Ls6/i;

.field public static final h0:Ls6/i;

.field public static final h1:Ls6/i;

.field public static final i:Ls6/i;

.field public static final i0:Ls6/i;

.field public static final i1:Ls6/i;

.field public static final j:Ls6/i;

.field public static final j0:Ls6/i;

.field public static final j1:Ls6/i;

.field public static final k:Ls6/i;

.field public static final k0:Ls6/i;

.field public static final k1:Ls6/i;

.field public static final l:Ls6/i;

.field public static final l0:Ls6/i;

.field public static final l1:Ls6/i;

.field public static final m:Ls6/i;

.field public static final m0:Ls6/i;

.field public static final m1:Ls6/i;

.field public static final n:Ls6/i;

.field public static final n0:Ls6/i;

.field public static final n1:Ls6/i;

.field public static final o:Ls6/i;

.field public static final o0:Ls6/i;

.field public static final o1:Ls6/i;

.field public static final p:Ls6/i;

.field public static final p0:Ls6/i;

.field public static final p1:Ls6/i;

.field public static final q:Ls6/i;

.field public static final q0:Ls6/i;

.field public static final q1:Ls6/i;

.field public static final r:Ls6/i;

.field public static final r0:Ls6/i;

.field public static final r1:Ls6/i;

.field public static final s:Ls6/i;

.field public static final s0:Ls6/i;

.field public static final s1:Ls6/i;

.field public static final t:Ls6/i;

.field public static final t0:Ls6/i;

.field public static final u:Ls6/i;

.field public static final u0:Ls6/i;

.field public static final v:Ls6/i;

.field public static final v0:Ls6/i;

.field public static final w:Ls6/i;

.field public static final w0:Ls6/i;

.field public static final x:Ls6/i;

.field public static final x0:Ls6/i;

.field public static final y:Ls6/i;

.field public static final y0:Ls6/i;

.field public static final z:Ls6/i;

.field public static final z0:Ls6/i;


# instance fields
.field public final a:Ljava/lang/String;


# direct methods
.method static constructor <clinit>()V
    .locals 3

    .line 1
    new-instance v0, Ls6/i$b;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, v1}, Ls6/i$b;-><init>(Lkotlin/jvm/internal/j;)V

    .line 5
    .line 6
    .line 7
    sput-object v0, Ls6/i;->b:Ls6/i$b;

    .line 8
    .line 9
    new-instance v1, Ls6/i$a;

    .line 10
    .line 11
    invoke-direct {v1}, Ls6/i$a;-><init>()V

    .line 12
    .line 13
    .line 14
    sput-object v1, Ls6/i;->c:Ljava/util/Comparator;

    .line 15
    .line 16
    new-instance v1, Ljava/util/LinkedHashMap;

    .line 17
    .line 18
    invoke-direct {v1}, Ljava/util/LinkedHashMap;-><init>()V

    .line 19
    .line 20
    .line 21
    sput-object v1, Ls6/i;->d:Ljava/util/Map;

    .line 22
    .line 23
    const-string v1, "SSL_RSA_WITH_NULL_MD5"

    .line 24
    .line 25
    const/4 v2, 0x1

    .line 26
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 27
    .line 28
    .line 29
    move-result-object v1

    .line 30
    sput-object v1, Ls6/i;->e:Ls6/i;

    .line 31
    .line 32
    const-string v1, "SSL_RSA_WITH_NULL_SHA"

    .line 33
    .line 34
    const/4 v2, 0x2

    .line 35
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 36
    .line 37
    .line 38
    move-result-object v1

    .line 39
    sput-object v1, Ls6/i;->f:Ls6/i;

    .line 40
    .line 41
    const-string v1, "SSL_RSA_EXPORT_WITH_RC4_40_MD5"

    .line 42
    .line 43
    const/4 v2, 0x3

    .line 44
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 45
    .line 46
    .line 47
    move-result-object v1

    .line 48
    sput-object v1, Ls6/i;->g:Ls6/i;

    .line 49
    .line 50
    const-string v1, "SSL_RSA_WITH_RC4_128_MD5"

    .line 51
    .line 52
    const/4 v2, 0x4

    .line 53
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 54
    .line 55
    .line 56
    move-result-object v1

    .line 57
    sput-object v1, Ls6/i;->h:Ls6/i;

    .line 58
    .line 59
    const-string v1, "SSL_RSA_WITH_RC4_128_SHA"

    .line 60
    .line 61
    const/4 v2, 0x5

    .line 62
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 63
    .line 64
    .line 65
    move-result-object v1

    .line 66
    sput-object v1, Ls6/i;->i:Ls6/i;

    .line 67
    .line 68
    const-string v1, "SSL_RSA_EXPORT_WITH_DES40_CBC_SHA"

    .line 69
    .line 70
    const/16 v2, 0x8

    .line 71
    .line 72
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 73
    .line 74
    .line 75
    move-result-object v1

    .line 76
    sput-object v1, Ls6/i;->j:Ls6/i;

    .line 77
    .line 78
    const-string v1, "SSL_RSA_WITH_DES_CBC_SHA"

    .line 79
    .line 80
    const/16 v2, 0x9

    .line 81
    .line 82
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 83
    .line 84
    .line 85
    move-result-object v1

    .line 86
    sput-object v1, Ls6/i;->k:Ls6/i;

    .line 87
    .line 88
    const-string v1, "SSL_RSA_WITH_3DES_EDE_CBC_SHA"

    .line 89
    .line 90
    const/16 v2, 0xa

    .line 91
    .line 92
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 93
    .line 94
    .line 95
    move-result-object v1

    .line 96
    sput-object v1, Ls6/i;->l:Ls6/i;

    .line 97
    .line 98
    const-string v1, "SSL_DHE_DSS_EXPORT_WITH_DES40_CBC_SHA"

    .line 99
    .line 100
    const/16 v2, 0x11

    .line 101
    .line 102
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 103
    .line 104
    .line 105
    move-result-object v1

    .line 106
    sput-object v1, Ls6/i;->m:Ls6/i;

    .line 107
    .line 108
    const-string v1, "SSL_DHE_DSS_WITH_DES_CBC_SHA"

    .line 109
    .line 110
    const/16 v2, 0x12

    .line 111
    .line 112
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 113
    .line 114
    .line 115
    move-result-object v1

    .line 116
    sput-object v1, Ls6/i;->n:Ls6/i;

    .line 117
    .line 118
    const-string v1, "SSL_DHE_DSS_WITH_3DES_EDE_CBC_SHA"

    .line 119
    .line 120
    const/16 v2, 0x13

    .line 121
    .line 122
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 123
    .line 124
    .line 125
    move-result-object v1

    .line 126
    sput-object v1, Ls6/i;->o:Ls6/i;

    .line 127
    .line 128
    const-string v1, "SSL_DHE_RSA_EXPORT_WITH_DES40_CBC_SHA"

    .line 129
    .line 130
    const/16 v2, 0x14

    .line 131
    .line 132
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 133
    .line 134
    .line 135
    move-result-object v1

    .line 136
    sput-object v1, Ls6/i;->p:Ls6/i;

    .line 137
    .line 138
    const-string v1, "SSL_DHE_RSA_WITH_DES_CBC_SHA"

    .line 139
    .line 140
    const/16 v2, 0x15

    .line 141
    .line 142
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 143
    .line 144
    .line 145
    move-result-object v1

    .line 146
    sput-object v1, Ls6/i;->q:Ls6/i;

    .line 147
    .line 148
    const-string v1, "SSL_DHE_RSA_WITH_3DES_EDE_CBC_SHA"

    .line 149
    .line 150
    const/16 v2, 0x16

    .line 151
    .line 152
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 153
    .line 154
    .line 155
    move-result-object v1

    .line 156
    sput-object v1, Ls6/i;->r:Ls6/i;

    .line 157
    .line 158
    const-string v1, "SSL_DH_anon_EXPORT_WITH_RC4_40_MD5"

    .line 159
    .line 160
    const/16 v2, 0x17

    .line 161
    .line 162
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 163
    .line 164
    .line 165
    move-result-object v1

    .line 166
    sput-object v1, Ls6/i;->s:Ls6/i;

    .line 167
    .line 168
    const-string v1, "SSL_DH_anon_WITH_RC4_128_MD5"

    .line 169
    .line 170
    const/16 v2, 0x18

    .line 171
    .line 172
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 173
    .line 174
    .line 175
    move-result-object v1

    .line 176
    sput-object v1, Ls6/i;->t:Ls6/i;

    .line 177
    .line 178
    const-string v1, "SSL_DH_anon_EXPORT_WITH_DES40_CBC_SHA"

    .line 179
    .line 180
    const/16 v2, 0x19

    .line 181
    .line 182
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 183
    .line 184
    .line 185
    move-result-object v1

    .line 186
    sput-object v1, Ls6/i;->u:Ls6/i;

    .line 187
    .line 188
    const-string v1, "SSL_DH_anon_WITH_DES_CBC_SHA"

    .line 189
    .line 190
    const/16 v2, 0x1a

    .line 191
    .line 192
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 193
    .line 194
    .line 195
    move-result-object v1

    .line 196
    sput-object v1, Ls6/i;->v:Ls6/i;

    .line 197
    .line 198
    const-string v1, "SSL_DH_anon_WITH_3DES_EDE_CBC_SHA"

    .line 199
    .line 200
    const/16 v2, 0x1b

    .line 201
    .line 202
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 203
    .line 204
    .line 205
    move-result-object v1

    .line 206
    sput-object v1, Ls6/i;->w:Ls6/i;

    .line 207
    .line 208
    const-string v1, "TLS_KRB5_WITH_DES_CBC_SHA"

    .line 209
    .line 210
    const/16 v2, 0x1e

    .line 211
    .line 212
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 213
    .line 214
    .line 215
    move-result-object v1

    .line 216
    sput-object v1, Ls6/i;->x:Ls6/i;

    .line 217
    .line 218
    const-string v1, "TLS_KRB5_WITH_3DES_EDE_CBC_SHA"

    .line 219
    .line 220
    const/16 v2, 0x1f

    .line 221
    .line 222
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 223
    .line 224
    .line 225
    move-result-object v1

    .line 226
    sput-object v1, Ls6/i;->y:Ls6/i;

    .line 227
    .line 228
    const-string v1, "TLS_KRB5_WITH_RC4_128_SHA"

    .line 229
    .line 230
    const/16 v2, 0x20

    .line 231
    .line 232
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 233
    .line 234
    .line 235
    move-result-object v1

    .line 236
    sput-object v1, Ls6/i;->z:Ls6/i;

    .line 237
    .line 238
    const-string v1, "TLS_KRB5_WITH_DES_CBC_MD5"

    .line 239
    .line 240
    const/16 v2, 0x22

    .line 241
    .line 242
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 243
    .line 244
    .line 245
    move-result-object v1

    .line 246
    sput-object v1, Ls6/i;->A:Ls6/i;

    .line 247
    .line 248
    const-string v1, "TLS_KRB5_WITH_3DES_EDE_CBC_MD5"

    .line 249
    .line 250
    const/16 v2, 0x23

    .line 251
    .line 252
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 253
    .line 254
    .line 255
    move-result-object v1

    .line 256
    sput-object v1, Ls6/i;->B:Ls6/i;

    .line 257
    .line 258
    const-string v1, "TLS_KRB5_WITH_RC4_128_MD5"

    .line 259
    .line 260
    const/16 v2, 0x24

    .line 261
    .line 262
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 263
    .line 264
    .line 265
    move-result-object v1

    .line 266
    sput-object v1, Ls6/i;->C:Ls6/i;

    .line 267
    .line 268
    const-string v1, "TLS_KRB5_EXPORT_WITH_DES_CBC_40_SHA"

    .line 269
    .line 270
    const/16 v2, 0x26

    .line 271
    .line 272
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 273
    .line 274
    .line 275
    move-result-object v1

    .line 276
    sput-object v1, Ls6/i;->D:Ls6/i;

    .line 277
    .line 278
    const-string v1, "TLS_KRB5_EXPORT_WITH_RC4_40_SHA"

    .line 279
    .line 280
    const/16 v2, 0x28

    .line 281
    .line 282
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 283
    .line 284
    .line 285
    move-result-object v1

    .line 286
    sput-object v1, Ls6/i;->E:Ls6/i;

    .line 287
    .line 288
    const-string v1, "TLS_KRB5_EXPORT_WITH_DES_CBC_40_MD5"

    .line 289
    .line 290
    const/16 v2, 0x29

    .line 291
    .line 292
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 293
    .line 294
    .line 295
    move-result-object v1

    .line 296
    sput-object v1, Ls6/i;->F:Ls6/i;

    .line 297
    .line 298
    const-string v1, "TLS_KRB5_EXPORT_WITH_RC4_40_MD5"

    .line 299
    .line 300
    const/16 v2, 0x2b

    .line 301
    .line 302
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 303
    .line 304
    .line 305
    move-result-object v1

    .line 306
    sput-object v1, Ls6/i;->G:Ls6/i;

    .line 307
    .line 308
    const-string v1, "TLS_RSA_WITH_AES_128_CBC_SHA"

    .line 309
    .line 310
    const/16 v2, 0x2f

    .line 311
    .line 312
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 313
    .line 314
    .line 315
    move-result-object v1

    .line 316
    sput-object v1, Ls6/i;->H:Ls6/i;

    .line 317
    .line 318
    const-string v1, "TLS_DHE_DSS_WITH_AES_128_CBC_SHA"

    .line 319
    .line 320
    const/16 v2, 0x32

    .line 321
    .line 322
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 323
    .line 324
    .line 325
    move-result-object v1

    .line 326
    sput-object v1, Ls6/i;->I:Ls6/i;

    .line 327
    .line 328
    const-string v1, "TLS_DHE_RSA_WITH_AES_128_CBC_SHA"

    .line 329
    .line 330
    const/16 v2, 0x33

    .line 331
    .line 332
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 333
    .line 334
    .line 335
    move-result-object v1

    .line 336
    sput-object v1, Ls6/i;->J:Ls6/i;

    .line 337
    .line 338
    const-string v1, "TLS_DH_anon_WITH_AES_128_CBC_SHA"

    .line 339
    .line 340
    const/16 v2, 0x34

    .line 341
    .line 342
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 343
    .line 344
    .line 345
    move-result-object v1

    .line 346
    sput-object v1, Ls6/i;->K:Ls6/i;

    .line 347
    .line 348
    const-string v1, "TLS_RSA_WITH_AES_256_CBC_SHA"

    .line 349
    .line 350
    const/16 v2, 0x35

    .line 351
    .line 352
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 353
    .line 354
    .line 355
    move-result-object v1

    .line 356
    sput-object v1, Ls6/i;->L:Ls6/i;

    .line 357
    .line 358
    const-string v1, "TLS_DHE_DSS_WITH_AES_256_CBC_SHA"

    .line 359
    .line 360
    const/16 v2, 0x38

    .line 361
    .line 362
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 363
    .line 364
    .line 365
    move-result-object v1

    .line 366
    sput-object v1, Ls6/i;->M:Ls6/i;

    .line 367
    .line 368
    const-string v1, "TLS_DHE_RSA_WITH_AES_256_CBC_SHA"

    .line 369
    .line 370
    const/16 v2, 0x39

    .line 371
    .line 372
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 373
    .line 374
    .line 375
    move-result-object v1

    .line 376
    sput-object v1, Ls6/i;->N:Ls6/i;

    .line 377
    .line 378
    const-string v1, "TLS_DH_anon_WITH_AES_256_CBC_SHA"

    .line 379
    .line 380
    const/16 v2, 0x3a

    .line 381
    .line 382
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 383
    .line 384
    .line 385
    move-result-object v1

    .line 386
    sput-object v1, Ls6/i;->O:Ls6/i;

    .line 387
    .line 388
    const-string v1, "TLS_RSA_WITH_NULL_SHA256"

    .line 389
    .line 390
    const/16 v2, 0x3b

    .line 391
    .line 392
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 393
    .line 394
    .line 395
    move-result-object v1

    .line 396
    sput-object v1, Ls6/i;->P:Ls6/i;

    .line 397
    .line 398
    const-string v1, "TLS_RSA_WITH_AES_128_CBC_SHA256"

    .line 399
    .line 400
    const/16 v2, 0x3c

    .line 401
    .line 402
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 403
    .line 404
    .line 405
    move-result-object v1

    .line 406
    sput-object v1, Ls6/i;->Q:Ls6/i;

    .line 407
    .line 408
    const-string v1, "TLS_RSA_WITH_AES_256_CBC_SHA256"

    .line 409
    .line 410
    const/16 v2, 0x3d

    .line 411
    .line 412
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 413
    .line 414
    .line 415
    move-result-object v1

    .line 416
    sput-object v1, Ls6/i;->R:Ls6/i;

    .line 417
    .line 418
    const-string v1, "TLS_DHE_DSS_WITH_AES_128_CBC_SHA256"

    .line 419
    .line 420
    const/16 v2, 0x40

    .line 421
    .line 422
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 423
    .line 424
    .line 425
    move-result-object v1

    .line 426
    sput-object v1, Ls6/i;->S:Ls6/i;

    .line 427
    .line 428
    const-string v1, "TLS_RSA_WITH_CAMELLIA_128_CBC_SHA"

    .line 429
    .line 430
    const/16 v2, 0x41

    .line 431
    .line 432
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 433
    .line 434
    .line 435
    move-result-object v1

    .line 436
    sput-object v1, Ls6/i;->T:Ls6/i;

    .line 437
    .line 438
    const-string v1, "TLS_DHE_DSS_WITH_CAMELLIA_128_CBC_SHA"

    .line 439
    .line 440
    const/16 v2, 0x44

    .line 441
    .line 442
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 443
    .line 444
    .line 445
    move-result-object v1

    .line 446
    sput-object v1, Ls6/i;->U:Ls6/i;

    .line 447
    .line 448
    const-string v1, "TLS_DHE_RSA_WITH_CAMELLIA_128_CBC_SHA"

    .line 449
    .line 450
    const/16 v2, 0x45

    .line 451
    .line 452
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 453
    .line 454
    .line 455
    move-result-object v1

    .line 456
    sput-object v1, Ls6/i;->V:Ls6/i;

    .line 457
    .line 458
    const-string v1, "TLS_DHE_RSA_WITH_AES_128_CBC_SHA256"

    .line 459
    .line 460
    const/16 v2, 0x67

    .line 461
    .line 462
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 463
    .line 464
    .line 465
    move-result-object v1

    .line 466
    sput-object v1, Ls6/i;->W:Ls6/i;

    .line 467
    .line 468
    const-string v1, "TLS_DHE_DSS_WITH_AES_256_CBC_SHA256"

    .line 469
    .line 470
    const/16 v2, 0x6a

    .line 471
    .line 472
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 473
    .line 474
    .line 475
    move-result-object v1

    .line 476
    sput-object v1, Ls6/i;->X:Ls6/i;

    .line 477
    .line 478
    const-string v1, "TLS_DHE_RSA_WITH_AES_256_CBC_SHA256"

    .line 479
    .line 480
    const/16 v2, 0x6b

    .line 481
    .line 482
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 483
    .line 484
    .line 485
    move-result-object v1

    .line 486
    sput-object v1, Ls6/i;->Y:Ls6/i;

    .line 487
    .line 488
    const-string v1, "TLS_DH_anon_WITH_AES_128_CBC_SHA256"

    .line 489
    .line 490
    const/16 v2, 0x6c

    .line 491
    .line 492
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 493
    .line 494
    .line 495
    move-result-object v1

    .line 496
    sput-object v1, Ls6/i;->Z:Ls6/i;

    .line 497
    .line 498
    const-string v1, "TLS_DH_anon_WITH_AES_256_CBC_SHA256"

    .line 499
    .line 500
    const/16 v2, 0x6d

    .line 501
    .line 502
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 503
    .line 504
    .line 505
    move-result-object v1

    .line 506
    sput-object v1, Ls6/i;->a0:Ls6/i;

    .line 507
    .line 508
    const-string v1, "TLS_RSA_WITH_CAMELLIA_256_CBC_SHA"

    .line 509
    .line 510
    const/16 v2, 0x84

    .line 511
    .line 512
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 513
    .line 514
    .line 515
    move-result-object v1

    .line 516
    sput-object v1, Ls6/i;->b0:Ls6/i;

    .line 517
    .line 518
    const-string v1, "TLS_DHE_DSS_WITH_CAMELLIA_256_CBC_SHA"

    .line 519
    .line 520
    const/16 v2, 0x87

    .line 521
    .line 522
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 523
    .line 524
    .line 525
    move-result-object v1

    .line 526
    sput-object v1, Ls6/i;->c0:Ls6/i;

    .line 527
    .line 528
    const-string v1, "TLS_DHE_RSA_WITH_CAMELLIA_256_CBC_SHA"

    .line 529
    .line 530
    const/16 v2, 0x88

    .line 531
    .line 532
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 533
    .line 534
    .line 535
    move-result-object v1

    .line 536
    sput-object v1, Ls6/i;->d0:Ls6/i;

    .line 537
    .line 538
    const-string v1, "TLS_PSK_WITH_RC4_128_SHA"

    .line 539
    .line 540
    const/16 v2, 0x8a

    .line 541
    .line 542
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 543
    .line 544
    .line 545
    move-result-object v1

    .line 546
    sput-object v1, Ls6/i;->e0:Ls6/i;

    .line 547
    .line 548
    const-string v1, "TLS_PSK_WITH_3DES_EDE_CBC_SHA"

    .line 549
    .line 550
    const/16 v2, 0x8b

    .line 551
    .line 552
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 553
    .line 554
    .line 555
    move-result-object v1

    .line 556
    sput-object v1, Ls6/i;->f0:Ls6/i;

    .line 557
    .line 558
    const-string v1, "TLS_PSK_WITH_AES_128_CBC_SHA"

    .line 559
    .line 560
    const/16 v2, 0x8c

    .line 561
    .line 562
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 563
    .line 564
    .line 565
    move-result-object v1

    .line 566
    sput-object v1, Ls6/i;->g0:Ls6/i;

    .line 567
    .line 568
    const-string v1, "TLS_PSK_WITH_AES_256_CBC_SHA"

    .line 569
    .line 570
    const/16 v2, 0x8d

    .line 571
    .line 572
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 573
    .line 574
    .line 575
    move-result-object v1

    .line 576
    sput-object v1, Ls6/i;->h0:Ls6/i;

    .line 577
    .line 578
    const-string v1, "TLS_RSA_WITH_SEED_CBC_SHA"

    .line 579
    .line 580
    const/16 v2, 0x96

    .line 581
    .line 582
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 583
    .line 584
    .line 585
    move-result-object v1

    .line 586
    sput-object v1, Ls6/i;->i0:Ls6/i;

    .line 587
    .line 588
    const-string v1, "TLS_RSA_WITH_AES_128_GCM_SHA256"

    .line 589
    .line 590
    const/16 v2, 0x9c

    .line 591
    .line 592
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 593
    .line 594
    .line 595
    move-result-object v1

    .line 596
    sput-object v1, Ls6/i;->j0:Ls6/i;

    .line 597
    .line 598
    const-string v1, "TLS_RSA_WITH_AES_256_GCM_SHA384"

    .line 599
    .line 600
    const/16 v2, 0x9d

    .line 601
    .line 602
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 603
    .line 604
    .line 605
    move-result-object v1

    .line 606
    sput-object v1, Ls6/i;->k0:Ls6/i;

    .line 607
    .line 608
    const-string v1, "TLS_DHE_RSA_WITH_AES_128_GCM_SHA256"

    .line 609
    .line 610
    const/16 v2, 0x9e

    .line 611
    .line 612
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 613
    .line 614
    .line 615
    move-result-object v1

    .line 616
    sput-object v1, Ls6/i;->l0:Ls6/i;

    .line 617
    .line 618
    const-string v1, "TLS_DHE_RSA_WITH_AES_256_GCM_SHA384"

    .line 619
    .line 620
    const/16 v2, 0x9f

    .line 621
    .line 622
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 623
    .line 624
    .line 625
    move-result-object v1

    .line 626
    sput-object v1, Ls6/i;->m0:Ls6/i;

    .line 627
    .line 628
    const-string v1, "TLS_DHE_DSS_WITH_AES_128_GCM_SHA256"

    .line 629
    .line 630
    const/16 v2, 0xa2

    .line 631
    .line 632
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 633
    .line 634
    .line 635
    move-result-object v1

    .line 636
    sput-object v1, Ls6/i;->n0:Ls6/i;

    .line 637
    .line 638
    const-string v1, "TLS_DHE_DSS_WITH_AES_256_GCM_SHA384"

    .line 639
    .line 640
    const/16 v2, 0xa3

    .line 641
    .line 642
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 643
    .line 644
    .line 645
    move-result-object v1

    .line 646
    sput-object v1, Ls6/i;->o0:Ls6/i;

    .line 647
    .line 648
    const-string v1, "TLS_DH_anon_WITH_AES_128_GCM_SHA256"

    .line 649
    .line 650
    const/16 v2, 0xa6

    .line 651
    .line 652
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 653
    .line 654
    .line 655
    move-result-object v1

    .line 656
    sput-object v1, Ls6/i;->p0:Ls6/i;

    .line 657
    .line 658
    const-string v1, "TLS_DH_anon_WITH_AES_256_GCM_SHA384"

    .line 659
    .line 660
    const/16 v2, 0xa7

    .line 661
    .line 662
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 663
    .line 664
    .line 665
    move-result-object v1

    .line 666
    sput-object v1, Ls6/i;->q0:Ls6/i;

    .line 667
    .line 668
    const-string v1, "TLS_EMPTY_RENEGOTIATION_INFO_SCSV"

    .line 669
    .line 670
    const/16 v2, 0xff

    .line 671
    .line 672
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 673
    .line 674
    .line 675
    move-result-object v1

    .line 676
    sput-object v1, Ls6/i;->r0:Ls6/i;

    .line 677
    .line 678
    const-string v1, "TLS_FALLBACK_SCSV"

    .line 679
    .line 680
    const/16 v2, 0x5600

    .line 681
    .line 682
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 683
    .line 684
    .line 685
    move-result-object v1

    .line 686
    sput-object v1, Ls6/i;->s0:Ls6/i;

    .line 687
    .line 688
    const-string v1, "TLS_ECDH_ECDSA_WITH_NULL_SHA"

    .line 689
    .line 690
    const v2, 0xc001

    .line 691
    .line 692
    .line 693
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 694
    .line 695
    .line 696
    move-result-object v1

    .line 697
    sput-object v1, Ls6/i;->t0:Ls6/i;

    .line 698
    .line 699
    const-string v1, "TLS_ECDH_ECDSA_WITH_RC4_128_SHA"

    .line 700
    .line 701
    const v2, 0xc002

    .line 702
    .line 703
    .line 704
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 705
    .line 706
    .line 707
    move-result-object v1

    .line 708
    sput-object v1, Ls6/i;->u0:Ls6/i;

    .line 709
    .line 710
    const-string v1, "TLS_ECDH_ECDSA_WITH_3DES_EDE_CBC_SHA"

    .line 711
    .line 712
    const v2, 0xc003

    .line 713
    .line 714
    .line 715
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 716
    .line 717
    .line 718
    move-result-object v1

    .line 719
    sput-object v1, Ls6/i;->v0:Ls6/i;

    .line 720
    .line 721
    const-string v1, "TLS_ECDH_ECDSA_WITH_AES_128_CBC_SHA"

    .line 722
    .line 723
    const v2, 0xc004

    .line 724
    .line 725
    .line 726
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 727
    .line 728
    .line 729
    move-result-object v1

    .line 730
    sput-object v1, Ls6/i;->w0:Ls6/i;

    .line 731
    .line 732
    const-string v1, "TLS_ECDH_ECDSA_WITH_AES_256_CBC_SHA"

    .line 733
    .line 734
    const v2, 0xc005

    .line 735
    .line 736
    .line 737
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 738
    .line 739
    .line 740
    move-result-object v1

    .line 741
    sput-object v1, Ls6/i;->x0:Ls6/i;

    .line 742
    .line 743
    const-string v1, "TLS_ECDHE_ECDSA_WITH_NULL_SHA"

    .line 744
    .line 745
    const v2, 0xc006

    .line 746
    .line 747
    .line 748
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 749
    .line 750
    .line 751
    move-result-object v1

    .line 752
    sput-object v1, Ls6/i;->y0:Ls6/i;

    .line 753
    .line 754
    const-string v1, "TLS_ECDHE_ECDSA_WITH_RC4_128_SHA"

    .line 755
    .line 756
    const v2, 0xc007

    .line 757
    .line 758
    .line 759
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 760
    .line 761
    .line 762
    move-result-object v1

    .line 763
    sput-object v1, Ls6/i;->z0:Ls6/i;

    .line 764
    .line 765
    const-string v1, "TLS_ECDHE_ECDSA_WITH_3DES_EDE_CBC_SHA"

    .line 766
    .line 767
    const v2, 0xc008

    .line 768
    .line 769
    .line 770
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 771
    .line 772
    .line 773
    move-result-object v1

    .line 774
    sput-object v1, Ls6/i;->A0:Ls6/i;

    .line 775
    .line 776
    const-string v1, "TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA"

    .line 777
    .line 778
    const v2, 0xc009

    .line 779
    .line 780
    .line 781
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 782
    .line 783
    .line 784
    move-result-object v1

    .line 785
    sput-object v1, Ls6/i;->B0:Ls6/i;

    .line 786
    .line 787
    const-string v1, "TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA"

    .line 788
    .line 789
    const v2, 0xc00a

    .line 790
    .line 791
    .line 792
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 793
    .line 794
    .line 795
    move-result-object v1

    .line 796
    sput-object v1, Ls6/i;->C0:Ls6/i;

    .line 797
    .line 798
    const-string v1, "TLS_ECDH_RSA_WITH_NULL_SHA"

    .line 799
    .line 800
    const v2, 0xc00b

    .line 801
    .line 802
    .line 803
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 804
    .line 805
    .line 806
    move-result-object v1

    .line 807
    sput-object v1, Ls6/i;->D0:Ls6/i;

    .line 808
    .line 809
    const-string v1, "TLS_ECDH_RSA_WITH_RC4_128_SHA"

    .line 810
    .line 811
    const v2, 0xc00c

    .line 812
    .line 813
    .line 814
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 815
    .line 816
    .line 817
    move-result-object v1

    .line 818
    sput-object v1, Ls6/i;->E0:Ls6/i;

    .line 819
    .line 820
    const-string v1, "TLS_ECDH_RSA_WITH_3DES_EDE_CBC_SHA"

    .line 821
    .line 822
    const v2, 0xc00d

    .line 823
    .line 824
    .line 825
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 826
    .line 827
    .line 828
    move-result-object v1

    .line 829
    sput-object v1, Ls6/i;->F0:Ls6/i;

    .line 830
    .line 831
    const-string v1, "TLS_ECDH_RSA_WITH_AES_128_CBC_SHA"

    .line 832
    .line 833
    const v2, 0xc00e

    .line 834
    .line 835
    .line 836
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 837
    .line 838
    .line 839
    move-result-object v1

    .line 840
    sput-object v1, Ls6/i;->G0:Ls6/i;

    .line 841
    .line 842
    const-string v1, "TLS_ECDH_RSA_WITH_AES_256_CBC_SHA"

    .line 843
    .line 844
    const v2, 0xc00f

    .line 845
    .line 846
    .line 847
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 848
    .line 849
    .line 850
    move-result-object v1

    .line 851
    sput-object v1, Ls6/i;->H0:Ls6/i;

    .line 852
    .line 853
    const-string v1, "TLS_ECDHE_RSA_WITH_NULL_SHA"

    .line 854
    .line 855
    const v2, 0xc010

    .line 856
    .line 857
    .line 858
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 859
    .line 860
    .line 861
    move-result-object v1

    .line 862
    sput-object v1, Ls6/i;->I0:Ls6/i;

    .line 863
    .line 864
    const-string v1, "TLS_ECDHE_RSA_WITH_RC4_128_SHA"

    .line 865
    .line 866
    const v2, 0xc011

    .line 867
    .line 868
    .line 869
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 870
    .line 871
    .line 872
    move-result-object v1

    .line 873
    sput-object v1, Ls6/i;->J0:Ls6/i;

    .line 874
    .line 875
    const-string v1, "TLS_ECDHE_RSA_WITH_3DES_EDE_CBC_SHA"

    .line 876
    .line 877
    const v2, 0xc012

    .line 878
    .line 879
    .line 880
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 881
    .line 882
    .line 883
    move-result-object v1

    .line 884
    sput-object v1, Ls6/i;->K0:Ls6/i;

    .line 885
    .line 886
    const-string v1, "TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA"

    .line 887
    .line 888
    const v2, 0xc013

    .line 889
    .line 890
    .line 891
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 892
    .line 893
    .line 894
    move-result-object v1

    .line 895
    sput-object v1, Ls6/i;->L0:Ls6/i;

    .line 896
    .line 897
    const-string v1, "TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA"

    .line 898
    .line 899
    const v2, 0xc014

    .line 900
    .line 901
    .line 902
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 903
    .line 904
    .line 905
    move-result-object v1

    .line 906
    sput-object v1, Ls6/i;->M0:Ls6/i;

    .line 907
    .line 908
    const-string v1, "TLS_ECDH_anon_WITH_NULL_SHA"

    .line 909
    .line 910
    const v2, 0xc015

    .line 911
    .line 912
    .line 913
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 914
    .line 915
    .line 916
    move-result-object v1

    .line 917
    sput-object v1, Ls6/i;->N0:Ls6/i;

    .line 918
    .line 919
    const-string v1, "TLS_ECDH_anon_WITH_RC4_128_SHA"

    .line 920
    .line 921
    const v2, 0xc016

    .line 922
    .line 923
    .line 924
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 925
    .line 926
    .line 927
    move-result-object v1

    .line 928
    sput-object v1, Ls6/i;->O0:Ls6/i;

    .line 929
    .line 930
    const-string v1, "TLS_ECDH_anon_WITH_3DES_EDE_CBC_SHA"

    .line 931
    .line 932
    const v2, 0xc017

    .line 933
    .line 934
    .line 935
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 936
    .line 937
    .line 938
    move-result-object v1

    .line 939
    sput-object v1, Ls6/i;->P0:Ls6/i;

    .line 940
    .line 941
    const-string v1, "TLS_ECDH_anon_WITH_AES_128_CBC_SHA"

    .line 942
    .line 943
    const v2, 0xc018

    .line 944
    .line 945
    .line 946
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 947
    .line 948
    .line 949
    move-result-object v1

    .line 950
    sput-object v1, Ls6/i;->Q0:Ls6/i;

    .line 951
    .line 952
    const-string v1, "TLS_ECDH_anon_WITH_AES_256_CBC_SHA"

    .line 953
    .line 954
    const v2, 0xc019

    .line 955
    .line 956
    .line 957
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 958
    .line 959
    .line 960
    move-result-object v1

    .line 961
    sput-object v1, Ls6/i;->R0:Ls6/i;

    .line 962
    .line 963
    const-string v1, "TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA256"

    .line 964
    .line 965
    const v2, 0xc023

    .line 966
    .line 967
    .line 968
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 969
    .line 970
    .line 971
    move-result-object v1

    .line 972
    sput-object v1, Ls6/i;->S0:Ls6/i;

    .line 973
    .line 974
    const-string v1, "TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA384"

    .line 975
    .line 976
    const v2, 0xc024

    .line 977
    .line 978
    .line 979
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 980
    .line 981
    .line 982
    move-result-object v1

    .line 983
    sput-object v1, Ls6/i;->T0:Ls6/i;

    .line 984
    .line 985
    const-string v1, "TLS_ECDH_ECDSA_WITH_AES_128_CBC_SHA256"

    .line 986
    .line 987
    const v2, 0xc025

    .line 988
    .line 989
    .line 990
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 991
    .line 992
    .line 993
    move-result-object v1

    .line 994
    sput-object v1, Ls6/i;->U0:Ls6/i;

    .line 995
    .line 996
    const-string v1, "TLS_ECDH_ECDSA_WITH_AES_256_CBC_SHA384"

    .line 997
    .line 998
    const v2, 0xc026

    .line 999
    .line 1000
    .line 1001
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 1002
    .line 1003
    .line 1004
    move-result-object v1

    .line 1005
    sput-object v1, Ls6/i;->V0:Ls6/i;

    .line 1006
    .line 1007
    const-string v1, "TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256"

    .line 1008
    .line 1009
    const v2, 0xc027

    .line 1010
    .line 1011
    .line 1012
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 1013
    .line 1014
    .line 1015
    move-result-object v1

    .line 1016
    sput-object v1, Ls6/i;->W0:Ls6/i;

    .line 1017
    .line 1018
    const-string v1, "TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA384"

    .line 1019
    .line 1020
    const v2, 0xc028

    .line 1021
    .line 1022
    .line 1023
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 1024
    .line 1025
    .line 1026
    move-result-object v1

    .line 1027
    sput-object v1, Ls6/i;->X0:Ls6/i;

    .line 1028
    .line 1029
    const-string v1, "TLS_ECDH_RSA_WITH_AES_128_CBC_SHA256"

    .line 1030
    .line 1031
    const v2, 0xc029

    .line 1032
    .line 1033
    .line 1034
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 1035
    .line 1036
    .line 1037
    move-result-object v1

    .line 1038
    sput-object v1, Ls6/i;->Y0:Ls6/i;

    .line 1039
    .line 1040
    const-string v1, "TLS_ECDH_RSA_WITH_AES_256_CBC_SHA384"

    .line 1041
    .line 1042
    const v2, 0xc02a

    .line 1043
    .line 1044
    .line 1045
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 1046
    .line 1047
    .line 1048
    move-result-object v1

    .line 1049
    sput-object v1, Ls6/i;->Z0:Ls6/i;

    .line 1050
    .line 1051
    const-string v1, "TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256"

    .line 1052
    .line 1053
    const v2, 0xc02b

    .line 1054
    .line 1055
    .line 1056
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 1057
    .line 1058
    .line 1059
    move-result-object v1

    .line 1060
    sput-object v1, Ls6/i;->a1:Ls6/i;

    .line 1061
    .line 1062
    const-string v1, "TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384"

    .line 1063
    .line 1064
    const v2, 0xc02c

    .line 1065
    .line 1066
    .line 1067
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 1068
    .line 1069
    .line 1070
    move-result-object v1

    .line 1071
    sput-object v1, Ls6/i;->b1:Ls6/i;

    .line 1072
    .line 1073
    const-string v1, "TLS_ECDH_ECDSA_WITH_AES_128_GCM_SHA256"

    .line 1074
    .line 1075
    const v2, 0xc02d

    .line 1076
    .line 1077
    .line 1078
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 1079
    .line 1080
    .line 1081
    move-result-object v1

    .line 1082
    sput-object v1, Ls6/i;->c1:Ls6/i;

    .line 1083
    .line 1084
    const-string v1, "TLS_ECDH_ECDSA_WITH_AES_256_GCM_SHA384"

    .line 1085
    .line 1086
    const v2, 0xc02e

    .line 1087
    .line 1088
    .line 1089
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 1090
    .line 1091
    .line 1092
    move-result-object v1

    .line 1093
    sput-object v1, Ls6/i;->d1:Ls6/i;

    .line 1094
    .line 1095
    const-string v1, "TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256"

    .line 1096
    .line 1097
    const v2, 0xc02f

    .line 1098
    .line 1099
    .line 1100
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 1101
    .line 1102
    .line 1103
    move-result-object v1

    .line 1104
    sput-object v1, Ls6/i;->e1:Ls6/i;

    .line 1105
    .line 1106
    const-string v1, "TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384"

    .line 1107
    .line 1108
    const v2, 0xc030

    .line 1109
    .line 1110
    .line 1111
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 1112
    .line 1113
    .line 1114
    move-result-object v1

    .line 1115
    sput-object v1, Ls6/i;->f1:Ls6/i;

    .line 1116
    .line 1117
    const-string v1, "TLS_ECDH_RSA_WITH_AES_128_GCM_SHA256"

    .line 1118
    .line 1119
    const v2, 0xc031

    .line 1120
    .line 1121
    .line 1122
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 1123
    .line 1124
    .line 1125
    move-result-object v1

    .line 1126
    sput-object v1, Ls6/i;->g1:Ls6/i;

    .line 1127
    .line 1128
    const-string v1, "TLS_ECDH_RSA_WITH_AES_256_GCM_SHA384"

    .line 1129
    .line 1130
    const v2, 0xc032

    .line 1131
    .line 1132
    .line 1133
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 1134
    .line 1135
    .line 1136
    move-result-object v1

    .line 1137
    sput-object v1, Ls6/i;->h1:Ls6/i;

    .line 1138
    .line 1139
    const-string v1, "TLS_ECDHE_PSK_WITH_AES_128_CBC_SHA"

    .line 1140
    .line 1141
    const v2, 0xc035

    .line 1142
    .line 1143
    .line 1144
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 1145
    .line 1146
    .line 1147
    move-result-object v1

    .line 1148
    sput-object v1, Ls6/i;->i1:Ls6/i;

    .line 1149
    .line 1150
    const-string v1, "TLS_ECDHE_PSK_WITH_AES_256_CBC_SHA"

    .line 1151
    .line 1152
    const v2, 0xc036

    .line 1153
    .line 1154
    .line 1155
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 1156
    .line 1157
    .line 1158
    move-result-object v1

    .line 1159
    sput-object v1, Ls6/i;->j1:Ls6/i;

    .line 1160
    .line 1161
    const-string v1, "TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256"

    .line 1162
    .line 1163
    const v2, 0xcca8

    .line 1164
    .line 1165
    .line 1166
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 1167
    .line 1168
    .line 1169
    move-result-object v1

    .line 1170
    sput-object v1, Ls6/i;->k1:Ls6/i;

    .line 1171
    .line 1172
    const-string v1, "TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256"

    .line 1173
    .line 1174
    const v2, 0xcca9

    .line 1175
    .line 1176
    .line 1177
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 1178
    .line 1179
    .line 1180
    move-result-object v1

    .line 1181
    sput-object v1, Ls6/i;->l1:Ls6/i;

    .line 1182
    .line 1183
    const-string v1, "TLS_DHE_RSA_WITH_CHACHA20_POLY1305_SHA256"

    .line 1184
    .line 1185
    const v2, 0xccaa

    .line 1186
    .line 1187
    .line 1188
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 1189
    .line 1190
    .line 1191
    move-result-object v1

    .line 1192
    sput-object v1, Ls6/i;->m1:Ls6/i;

    .line 1193
    .line 1194
    const-string v1, "TLS_ECDHE_PSK_WITH_CHACHA20_POLY1305_SHA256"

    .line 1195
    .line 1196
    const v2, 0xccac

    .line 1197
    .line 1198
    .line 1199
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 1200
    .line 1201
    .line 1202
    move-result-object v1

    .line 1203
    sput-object v1, Ls6/i;->n1:Ls6/i;

    .line 1204
    .line 1205
    const-string v1, "TLS_AES_128_GCM_SHA256"

    .line 1206
    .line 1207
    const/16 v2, 0x1301

    .line 1208
    .line 1209
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 1210
    .line 1211
    .line 1212
    move-result-object v1

    .line 1213
    sput-object v1, Ls6/i;->o1:Ls6/i;

    .line 1214
    .line 1215
    const-string v1, "TLS_AES_256_GCM_SHA384"

    .line 1216
    .line 1217
    const/16 v2, 0x1302

    .line 1218
    .line 1219
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 1220
    .line 1221
    .line 1222
    move-result-object v1

    .line 1223
    sput-object v1, Ls6/i;->p1:Ls6/i;

    .line 1224
    .line 1225
    const-string v1, "TLS_CHACHA20_POLY1305_SHA256"

    .line 1226
    .line 1227
    const/16 v2, 0x1303

    .line 1228
    .line 1229
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 1230
    .line 1231
    .line 1232
    move-result-object v1

    .line 1233
    sput-object v1, Ls6/i;->q1:Ls6/i;

    .line 1234
    .line 1235
    const-string v1, "TLS_AES_128_CCM_SHA256"

    .line 1236
    .line 1237
    const/16 v2, 0x1304

    .line 1238
    .line 1239
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 1240
    .line 1241
    .line 1242
    move-result-object v1

    .line 1243
    sput-object v1, Ls6/i;->r1:Ls6/i;

    .line 1244
    .line 1245
    const-string v1, "TLS_AES_128_CCM_8_SHA256"

    .line 1246
    .line 1247
    const/16 v2, 0x1305

    .line 1248
    .line 1249
    invoke-static {v0, v1, v2}, Ls6/i$b;->a(Ls6/i$b;Ljava/lang/String;I)Ls6/i;

    .line 1250
    .line 1251
    .line 1252
    move-result-object v0

    .line 1253
    sput-object v0, Ls6/i;->s1:Ls6/i;

    .line 1254
    .line 1255
    return-void
.end method

.method public constructor <init>(Ljava/lang/String;)V
    .locals 0

    .line 2
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 3
    iput-object p1, p0, Ls6/i;->a:Ljava/lang/String;

    return-void
.end method

.method public synthetic constructor <init>(Ljava/lang/String;Lkotlin/jvm/internal/j;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Ls6/i;-><init>(Ljava/lang/String;)V

    return-void
.end method

.method public static final synthetic a()Ljava/util/Map;
    .locals 1

    .line 1
    sget-object v0, Ls6/i;->d:Ljava/util/Map;

    .line 2
    .line 3
    return-object v0
.end method

.method public static final synthetic b()Ljava/util/Comparator;
    .locals 1

    .line 1
    sget-object v0, Ls6/i;->c:Ljava/util/Comparator;

    .line 2
    .line 3
    return-object v0
.end method


# virtual methods
.method public final c()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Ls6/i;->a:Ljava/lang/String;

    .line 2
    .line 3
    return-object v0
.end method

.method public toString()Ljava/lang/String;
    .locals 1

    .line 1
    iget-object v0, p0, Ls6/i;->a:Ljava/lang/String;

    .line 2
    .line 3
    return-object v0
.end method
