.class public abstract Ly5/x;
.super Ljava/lang/Object;
.source "SourceFile"


# direct methods
.method public static final a(L<PERSON>va/lang/Object;Ljava/lang/Object;)Ly5/r;
    .locals 1

    .line 1
    new-instance v0, Ly5/r;

    .line 2
    .line 3
    invoke-direct {v0, p0, p1}, Ly5/r;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V

    .line 4
    .line 5
    .line 6
    return-object v0
.end method
