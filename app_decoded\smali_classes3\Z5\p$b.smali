.class public final LZ5/p$b;
.super Lkotlin/jvm/internal/s;
.source "SourceFile"

# interfaces
.implements LM5/k;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = LZ5/p;->a(LZ5/r;Lkotlin/jvm/functions/Function0;LD5/d;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation


# instance fields
.field public final synthetic a:LX5/m;


# direct methods
.method public constructor <init>(LX5/m;)V
    .locals 0

    .line 1
    iput-object p1, p0, LZ5/p$b;->a:LX5/m;

    .line 2
    .line 3
    const/4 p1, 0x1

    .line 4
    invoke-direct {p0, p1}, Lkotlin/jvm/internal/s;-><init>(I)V

    .line 5
    .line 6
    .line 7
    return-void
.end method


# virtual methods
.method public final a(Ljava/lang/Throwable;)V
    .locals 1

    .line 1
    iget-object p1, p0, LZ5/p$b;->a:LX5/m;

    .line 2
    .line 3
    sget-object v0, Ly5/s;->b:Ly5/s$a;

    .line 4
    .line 5
    sget-object v0, Ly5/I;->a:Ly5/I;

    .line 6
    .line 7
    invoke-static {v0}, Ly5/s;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-interface {p1, v0}, LD5/d;->resumeWith(Ljava/lang/Object;)V

    .line 12
    .line 13
    .line 14
    return-void
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Ljava/lang/Throwable;

    .line 2
    .line 3
    invoke-virtual {p0, p1}, LZ5/p$b;->a(Ljava/lang/Throwable;)V

    .line 4
    .line 5
    .line 6
    sget-object p1, Ly5/I;->a:Ly5/I;

    .line 7
    .line 8
    return-object p1
.end method
