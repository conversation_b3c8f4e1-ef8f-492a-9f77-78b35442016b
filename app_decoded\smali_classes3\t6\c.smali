.class public final synthetic Lt6/c;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ljava/util/concurrent/ThreadFactory;


# instance fields
.field public final synthetic a:Ljava/lang/String;

.field public final synthetic b:Z


# direct methods
.method public synthetic constructor <init>(<PERSON><PERSON><PERSON>/lang/String;Z)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lt6/c;->a:Ljava/lang/String;

    .line 5
    .line 6
    iput-boolean p2, p0, Lt6/c;->b:Z

    .line 7
    .line 8
    return-void
.end method


# virtual methods
.method public final newThread(Ljava/lang/Runnable;)Ljava/lang/Thread;
    .locals 2

    .line 1
    iget-object v0, p0, Lt6/c;->a:Ljava/lang/String;

    .line 2
    .line 3
    iget-boolean v1, p0, Lt6/c;->b:Z

    .line 4
    .line 5
    invoke-static {v0, v1, p1}, Lt6/d;->a(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON><PERSON>/lang/Runnable;)<PERSON><PERSON><PERSON>/lang/Thread;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    return-object p1
.end method
