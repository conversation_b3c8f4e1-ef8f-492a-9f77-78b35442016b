.class public abstract Lz5/K;
.super Lz5/P;
.source "SourceFile"


# direct methods
.method public static bridge synthetic b(Ljava/util/Map;)Ljava/util/Map;
    .locals 0

    .line 1
    invoke-static {p0}, Lz5/M;->b(Ljava/util/Map;)Ljava/util/Map;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static bridge synthetic c()Ljava/util/Map;
    .locals 1

    .line 1
    invoke-static {}, Lz5/M;->c()Ljava/util/Map;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method

.method public static bridge synthetic d(I)I
    .locals 0

    .line 1
    invoke-static {p0}, Lz5/M;->d(I)I

    .line 2
    .line 3
    .line 4
    move-result p0

    .line 5
    return p0
.end method

.method public static bridge synthetic e(Ly5/r;)Ljava/util/Map;
    .locals 0

    .line 1
    invoke-static {p0}, Lz5/M;->e(Ly5/r;)Ljava/util/Map;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static bridge synthetic g()Ljava/util/Map;
    .locals 1

    .line 1
    invoke-static {}, Lz5/N;->g()Ljava/util/Map;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method

.method public static bridge synthetic h(Ljava/util/Map;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lz5/N;->h(Ljava/util/Map;Ljava/lang/Object;)Ljava/lang/Object;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static bridge varargs synthetic i([Ly5/r;)Ljava/util/Map;
    .locals 0

    .line 1
    invoke-static {p0}, Lz5/N;->i([Ly5/r;)Ljava/util/Map;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static bridge synthetic j(Ljava/util/Map;Ljava/lang/Iterable;)Ljava/util/Map;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lz5/N;->j(Ljava/util/Map;Ljava/lang/Iterable;)Ljava/util/Map;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static bridge varargs synthetic k([Ly5/r;)Ljava/util/Map;
    .locals 0

    .line 1
    invoke-static {p0}, Lz5/N;->k([Ly5/r;)Ljava/util/Map;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static bridge synthetic m(Ljava/util/Map;Ljava/util/Map;)Ljava/util/Map;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lz5/N;->m(Ljava/util/Map;Ljava/util/Map;)Ljava/util/Map;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static bridge synthetic n(Ljava/util/Map;Ly5/r;)Ljava/util/Map;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lz5/N;->n(Ljava/util/Map;Ly5/r;)Ljava/util/Map;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static bridge synthetic p(Ljava/util/Map;Ljava/lang/Iterable;)V
    .locals 0

    .line 1
    invoke-static {p0, p1}, Lz5/N;->p(Ljava/util/Map;Ljava/lang/Iterable;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public static bridge synthetic r(LU5/f;)Ljava/util/Map;
    .locals 0

    .line 1
    invoke-static {p0}, Lz5/N;->r(LU5/f;)Ljava/util/Map;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static bridge synthetic t(Ljava/lang/Iterable;)Ljava/util/Map;
    .locals 0

    .line 1
    invoke-static {p0}, Lz5/N;->t(Ljava/lang/Iterable;)Ljava/util/Map;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static bridge synthetic v(Ljava/util/Map;)Ljava/util/Map;
    .locals 0

    .line 1
    invoke-static {p0}, Lz5/N;->v(Ljava/util/Map;)Ljava/util/Map;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static bridge synthetic x(Ljava/util/Map;)Ljava/util/Map;
    .locals 0

    .line 1
    invoke-static {p0}, Lz5/N;->x(Ljava/util/Map;)Ljava/util/Map;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static bridge synthetic y(Ljava/util/Map;)LU5/f;
    .locals 0

    .line 1
    invoke-static {p0}, Lz5/P;->y(Ljava/util/Map;)LU5/f;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method
