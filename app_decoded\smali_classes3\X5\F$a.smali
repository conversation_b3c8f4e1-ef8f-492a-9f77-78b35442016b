.class public final LX5/F$a;
.super Lkotlin/jvm/internal/s;
.source "SourceFile"

# interfaces
.implements LM5/o;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = LX5/F;->a(LD5/g;LD5/g;Z)LD5/g;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation


# static fields
.field public static final a:LX5/F$a;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, LX5/F$a;

    .line 2
    .line 3
    invoke-direct {v0}, LX5/F$a;-><init>()V

    .line 4
    .line 5
    .line 6
    sput-object v0, LX5/F$a;->a:LX5/F$a;

    .line 7
    .line 8
    return-void
.end method

.method public constructor <init>()V
    .locals 1

    .line 1
    const/4 v0, 0x2

    .line 2
    invoke-direct {p0, v0}, Lkotlin/jvm/internal/s;-><init>(I)V

    .line 3
    .line 4
    .line 5
    return-void
.end method


# virtual methods
.method public final a(LD5/g;LD5/g$b;)LD5/g;
    .locals 0

    .line 1
    invoke-interface {p1, p2}, LD5/g;->D0(LD5/g;)LD5/g;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, LD5/g;

    .line 2
    .line 3
    check-cast p2, LD5/g$b;

    .line 4
    .line 5
    invoke-virtual {p0, p1, p2}, LX5/F$a;->a(LD5/g;LD5/g$b;)LD5/g;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    return-object p1
.end method
