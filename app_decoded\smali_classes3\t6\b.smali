.class public final synthetic Lt6/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Ls6/r$c;


# instance fields
.field public final synthetic a:Ls6/r;


# direct methods
.method public synthetic constructor <init>(Ls6/r;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lt6/b;->a:Ls6/r;

    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final a(Ls6/e;)Ls6/r;
    .locals 1

    .line 1
    iget-object v0, p0, Lt6/b;->a:Ls6/r;

    .line 2
    .line 3
    invoke-static {v0, p1}, Lt6/d;->b(Ls6/r;Ls6/e;)Ls6/r;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    return-object p1
.end method
