.class public final LU5/d;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LU5/f;
.implements LU5/c;


# static fields
.field public static final a:LU5/d;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    new-instance v0, LU5/d;

    .line 2
    .line 3
    invoke-direct {v0}, LU5/d;-><init>()V

    .line 4
    .line 5
    .line 6
    sput-object v0, LU5/d;->a:LU5/d;

    .line 7
    .line 8
    return-void
.end method

.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public bridge synthetic a(I)LU5/f;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LU5/d;->d(I)LU5/d;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    return-object p1
.end method

.method public bridge synthetic b(I)LU5/f;
    .locals 0

    .line 1
    invoke-virtual {p0, p1}, LU5/d;->c(I)LU5/d;

    .line 2
    .line 3
    .line 4
    move-result-object p1

    .line 5
    return-object p1
.end method

.method public c(I)LU5/d;
    .locals 0

    .line 1
    sget-object p1, LU5/d;->a:LU5/d;

    .line 2
    .line 3
    return-object p1
.end method

.method public d(I)LU5/d;
    .locals 0

    .line 1
    sget-object p1, LU5/d;->a:LU5/d;

    .line 2
    .line 3
    return-object p1
.end method

.method public iterator()Ljava/util/Iterator;
    .locals 1

    .line 1
    sget-object v0, Lz5/z;->a:Lz5/z;

    .line 2
    .line 3
    return-object v0
.end method
