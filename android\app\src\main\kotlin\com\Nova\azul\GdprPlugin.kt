package com.Nova.Yemen


import android.app.Activity
import com.google.android.ump.ConsentDebugSettings
import com.google.android.ump.ConsentRequestParameters
import com.google.android.ump.ConsentInformation
import com.google.android.ump.UserMessagingPlatform
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.MethodChannel.MethodCallHandler
import io.flutter.plugin.common.MethodChannel.Result
import io.flutter.embedding.engine.plugins.FlutterPlugin
import io.flutter.embedding.engine.plugins.activity.ActivityAware
import io.flutter.embedding.engine.plugins.activity.ActivityPluginBinding

class GdprPlugin : FlutterPlugin, MethodCallHandler, ActivityAware {
    private lateinit var channel: MethodChannel
    private var activity: Activity? = null

    private val TAG = "GdprPlugin"
    private lateinit var consentInformation: ConsentInformation

    override fun onAttachedToEngine(flutterPluginBinding: FlutterPlugin.FlutterPluginBinding) {
        channel = MethodChannel(flutterPluginBinding.binaryMessenger, "gdpr_plugin")
        channel.setMethodCallHandler(this)
    }

    override fun onDetachedFromEngine(binding: FlutterPlugin.FlutterPluginBinding) {
        channel.setMethodCallHandler(null)
    }

    override fun onAttachedToActivity(binding: ActivityPluginBinding) {
        activity = binding.activity
        initConsentInformation()
    }

    override fun onDetachedFromActivityForConfigChanges() {
        activity = null
    }

    override fun onReattachedToActivityForConfigChanges(binding: ActivityPluginBinding) {
        activity = binding.activity
    }

    override fun onDetachedFromActivity() {
        activity = null
    }

    private fun initConsentInformation() {
        activity?.let { currentActivity ->
            val debugSettings = ConsentDebugSettings.Builder(currentActivity)
                .addTestDeviceHashedId("TEST-DEVICE-HASHED-ID")
                .build()

            val params = ConsentRequestParameters.Builder()
                .setConsentDebugSettings(debugSettings)
                .build()

            consentInformation = UserMessagingPlatform.getConsentInformation(currentActivity)
            consentInformation.requestConsentInfoUpdate(
                currentActivity,
                params,
                {
                    UserMessagingPlatform.loadAndShowConsentFormIfRequired(
                        currentActivity,
                        {
                            // Handle consent form dismissal
                        }
                    )
                },
                {
                        requestConsentError ->
                    // Handle request consent error
                }
            )
        }
    }

    override fun onMethodCall(call: MethodCall, result: Result) {
        when (call.method) {
            "showConsentForm" -> {
                activity?.let { currentActivity ->
                    UserMessagingPlatform.loadAndShowConsentFormIfRequired(
                        currentActivity,
                        {
                            // Handle consent form dismissal
                            result.success(null)
                        }
                    )
                } ?: result.error("NO_ACTIVITY", "Activity not available", null)
            }
            else -> result.notImplemented()
        }
    }
}
