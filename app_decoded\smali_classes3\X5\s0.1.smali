.class public interface abstract LX5/s0;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LD5/g$b;


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        LX5/s0$a;,
        LX5/s0$b;
    }
.end annotation


# static fields
.field public static final Q7:LX5/s0$b;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 1
    sget-object v0, LX5/s0$b;->a:LX5/s0$b;

    .line 2
    .line 3
    sput-object v0, LX5/s0;->Q7:LX5/s0$b;

    .line 4
    .line 5
    return-void
.end method


# virtual methods
.method public abstract B(LM5/k;)LX5/Z;
.end method

.method public abstract E0()Z
.end method

.method public abstract I0(ZZLM5/k;)LX5/Z;
.end method

.method public abstract K()Ljava/util/concurrent/CancellationException;
.end method

.method public abstract Q(LX5/u;)LX5/s;
.end method

.method public abstract a()Z
.end method

.method public abstract c(Ljava/util/concurrent/CancellationException;)V
.end method

.method public abstract getParent()LX5/s0;
.end method

.method public abstract isCancelled()Z
.end method

.method public abstract start()Z
.end method

.method public abstract t(LD5/d;)Ljava/lang/Object;
.end method
