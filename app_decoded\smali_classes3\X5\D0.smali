.class public final LX5/D0;
.super LX5/N0;
.source "SourceFile"


# instance fields
.field public final d:LD5/d;


# direct methods
.method public constructor <init>(LD5/g;LM5/o;)V
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    invoke-direct {p0, p1, v0}, LX5/N0;-><init>(LD5/g;Z)V

    .line 3
    .line 4
    .line 5
    invoke-static {p2, p0, p0}, LE5/b;->a(LM5/o;Ljava/lang/Object;LD5/d;)LD5/d;

    .line 6
    .line 7
    .line 8
    move-result-object p1

    .line 9
    iput-object p1, p0, LX5/D0;->d:LD5/d;

    .line 10
    .line 11
    return-void
.end method


# virtual methods
.method public B0()V
    .locals 1

    .line 1
    iget-object v0, p0, LX5/D0;->d:LD5/d;

    .line 2
    .line 3
    invoke-static {v0, p0}, Ld6/a;->b(LD5/d;LD5/d;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method
