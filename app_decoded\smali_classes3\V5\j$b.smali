.class public final LV5/j$b;
.super Lkotlin/jvm/internal/s;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = LV5/j;->c(Ljava/lang/CharSequence;I)LU5/f;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation


# instance fields
.field public final synthetic a:LV5/j;

.field public final synthetic b:Ljava/lang/CharSequence;

.field public final synthetic c:I


# direct methods
.method public constructor <init>(LV5/j;Ljava/lang/CharSequence;I)V
    .locals 0

    .line 1
    iput-object p1, p0, LV5/j$b;->a:LV5/j;

    .line 2
    .line 3
    iput-object p2, p0, LV5/j$b;->b:Ljava/lang/CharSequence;

    .line 4
    .line 5
    iput p3, p0, LV5/j$b;->c:I

    .line 6
    .line 7
    const/4 p1, 0x0

    .line 8
    invoke-direct {p0, p1}, Lkotlin/jvm/internal/s;-><init>(I)V

    .line 9
    .line 10
    .line 11
    return-void
.end method


# virtual methods
.method public final a()LV5/h;
    .locals 3

    .line 1
    iget-object v0, p0, LV5/j$b;->a:LV5/j;

    .line 2
    .line 3
    iget-object v1, p0, LV5/j$b;->b:Ljava/lang/CharSequence;

    .line 4
    .line 5
    iget v2, p0, LV5/j$b;->c:I

    .line 6
    .line 7
    invoke-virtual {v0, v1, v2}, LV5/j;->a(Ljava/lang/CharSequence;I)LV5/h;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    return-object v0
.end method

.method public bridge synthetic invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, LV5/j$b;->a()LV5/h;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method
