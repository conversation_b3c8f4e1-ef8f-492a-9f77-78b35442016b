.class public final LX5/L0;
.super LX5/z0;
.source "SourceFile"


# instance fields
.field public final e:LD5/d;


# direct methods
.method public constructor <init>(LD5/d;)V
    .locals 0

    .line 1
    invoke-direct {p0}, LX5/z0;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LX5/L0;->e:LD5/d;

    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public bridge synthetic invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Ljava/lang/Throwable;

    .line 2
    .line 3
    invoke-virtual {p0, p1}, LX5/L0;->v(Ljava/lang/Throwable;)V

    .line 4
    .line 5
    .line 6
    sget-object p1, Ly5/I;->a:Ly5/I;

    .line 7
    .line 8
    return-object p1
.end method

.method public v(Ljava/lang/Throwable;)V
    .locals 1

    .line 1
    iget-object p1, p0, LX5/L0;->e:LD5/d;

    .line 2
    .line 3
    sget-object v0, Ly5/s;->b:Ly5/s$a;

    .line 4
    .line 5
    sget-object v0, Ly5/I;->a:Ly5/I;

    .line 6
    .line 7
    invoke-static {v0}, Ly5/s;->b(Ljava/lang/Object;)Ljava/lang/Object;

    .line 8
    .line 9
    .line 10
    move-result-object v0

    .line 11
    invoke-interface {p1, v0}, LD5/d;->resumeWith(Ljava/lang/Object;)V

    .line 12
    .line 13
    .line 14
    return-void
.end method
