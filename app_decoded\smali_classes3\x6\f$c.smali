.class public final Lx6/f$c;
.super Lkotlin/jvm/internal/s;
.source "SourceFile"

# interfaces
.implements Lkotlin/jvm/functions/Function0;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lx6/f;->i(Lx6/b;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation


# instance fields
.field public final synthetic a:Ls6/g;

.field public final synthetic b:Ls6/s;

.field public final synthetic c:Ls6/a;


# direct methods
.method public constructor <init>(Ls6/g;Ls6/s;Ls6/a;)V
    .locals 0

    .line 1
    iput-object p1, p0, Lx6/f$c;->a:Ls6/g;

    .line 2
    .line 3
    iput-object p2, p0, Lx6/f$c;->b:Ls6/s;

    .line 4
    .line 5
    iput-object p3, p0, Lx6/f$c;->c:Ls6/a;

    .line 6
    .line 7
    const/4 p1, 0x0

    .line 8
    invoke-direct {p0, p1}, Lkotlin/jvm/internal/s;-><init>(I)V

    .line 9
    .line 10
    .line 11
    return-void
.end method


# virtual methods
.method public bridge synthetic invoke()Ljava/lang/Object;
    .locals 1

    .line 1
    invoke-virtual {p0}, Lx6/f$c;->invoke()Ljava/util/List;

    move-result-object v0

    return-object v0
.end method

.method public final invoke()Ljava/util/List;
    .locals 3

    .line 2
    iget-object v0, p0, Lx6/f$c;->a:Ls6/g;

    invoke-virtual {v0}, Ls6/g;->d()LF6/c;

    move-result-object v0

    invoke-static {v0}, Lkotlin/jvm/internal/r;->c(Ljava/lang/Object;)V

    iget-object v1, p0, Lx6/f$c;->b:Ls6/s;

    invoke-virtual {v1}, Ls6/s;->d()Ljava/util/List;

    move-result-object v1

    .line 3
    iget-object v2, p0, Lx6/f$c;->c:Ls6/a;

    invoke-virtual {v2}, Ls6/a;->l()Ls6/u;

    move-result-object v2

    invoke-virtual {v2}, Ls6/u;->h()Ljava/lang/String;

    move-result-object v2

    .line 4
    invoke-virtual {v0, v1, v2}, LF6/c;->a(Ljava/util/List;Ljava/lang/String;)Ljava/util/List;

    move-result-object v0

    return-object v0
.end method
