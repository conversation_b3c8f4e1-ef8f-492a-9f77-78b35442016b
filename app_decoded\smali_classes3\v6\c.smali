.class public final Lv6/c;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/MemberClasses;
    value = {
        Lv6/c$b;,
        Lv6/c$a;
    }
.end annotation


# static fields
.field public static final c:Lv6/c$a;


# instance fields
.field public final a:Ls6/z;

.field public final b:Ls6/B;


# direct methods
.method static constructor <clinit>()V
    .locals 2

    .line 1
    new-instance v0, Lv6/c$a;

    .line 2
    .line 3
    const/4 v1, 0x0

    .line 4
    invoke-direct {v0, v1}, Lv6/c$a;-><init>(Lkotlin/jvm/internal/j;)V

    .line 5
    .line 6
    .line 7
    sput-object v0, Lv6/c;->c:Lv6/c$a;

    .line 8
    .line 9
    return-void
.end method

.method public constructor <init>(Ls6/z;Ls6/B;)V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, Lv6/c;->a:Ls6/z;

    .line 5
    .line 6
    iput-object p2, p0, Lv6/c;->b:Ls6/B;

    .line 7
    .line 8
    return-void
.end method


# virtual methods
.method public final a()Ls6/B;
    .locals 1

    .line 1
    iget-object v0, p0, Lv6/c;->b:Ls6/B;

    .line 2
    .line 3
    return-object v0
.end method

.method public final b()Ls6/z;
    .locals 1

    .line 1
    iget-object v0, p0, Lv6/c;->a:Ls6/z;

    .line 2
    .line 3
    return-object v0
.end method
