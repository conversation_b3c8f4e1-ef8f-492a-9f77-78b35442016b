.class public Lio/flutter/plugins/webviewflutter/WebStorageProxyApi;
.super Lio/flutter/plugins/webviewflutter/PigeonApiWebStorage;
.source "SourceFile"


# direct methods
.method public constructor <init>(Lio/flutter/plugins/webviewflutter/ProxyApiRegistrar;)V
    .locals 0

    .line 1
    invoke-direct {p0, p1}, Lio/flutter/plugins/webviewflutter/PigeonApiWebStorage;-><init>(Lio/flutter/plugins/webviewflutter/AndroidWebkitLibraryPigeonProxyApiRegistrar;)V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public deleteAllData(Landroid/webkit/WebStorage;)V
    .locals 0

    .line 1
    invoke-virtual {p1}, Landroid/webkit/WebStorage;->deleteAllData()V

    .line 2
    .line 3
    .line 4
    return-void
.end method

.method public instance()Landroid/webkit/WebStorage;
    .locals 1

    .line 1
    invoke-static {}, Landroid/webkit/WebStorage;->getInstance()Landroid/webkit/WebStorage;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method
