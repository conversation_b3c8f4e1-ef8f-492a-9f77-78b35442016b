.class public final LX5/t;
.super LX5/u0;
.source "SourceFile"

# interfaces
.implements LX5/s;


# instance fields
.field public final e:LX5/u;


# direct methods
.method public constructor <init>(LX5/u;)V
    .locals 0

    .line 1
    invoke-direct {p0}, LX5/u0;-><init>()V

    .line 2
    .line 3
    .line 4
    iput-object p1, p0, LX5/t;->e:LX5/u;

    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public c(Ljava/lang/Throwable;)Z
    .locals 1

    .line 1
    invoke-virtual {p0}, LX5/z0;->w()LX5/A0;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    invoke-virtual {v0, p1}, LX5/A0;->R(Ljava/lang/Throwable;)Z

    .line 6
    .line 7
    .line 8
    move-result p1

    .line 9
    return p1
.end method

.method public getParent()LX5/s0;
    .locals 1

    .line 1
    invoke-virtual {p0}, LX5/z0;->w()LX5/A0;

    .line 2
    .line 3
    .line 4
    move-result-object v0

    .line 5
    return-object v0
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Ljava/lang/Throwable;

    .line 2
    .line 3
    invoke-virtual {p0, p1}, LX5/t;->v(Ljava/lang/Throwable;)V

    .line 4
    .line 5
    .line 6
    sget-object p1, Ly5/I;->a:Ly5/I;

    .line 7
    .line 8
    return-object p1
.end method

.method public v(Ljava/lang/Throwable;)V
    .locals 1

    .line 1
    iget-object p1, p0, LX5/t;->e:LX5/u;

    .line 2
    .line 3
    invoke-virtual {p0}, LX5/z0;->w()LX5/A0;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-interface {p1, v0}, LX5/u;->h0(LX5/J0;)V

    .line 8
    .line 9
    .line 10
    return-void
.end method
