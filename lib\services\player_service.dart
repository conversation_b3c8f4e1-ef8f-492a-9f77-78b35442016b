import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import '../presentation/screens/player/enhanced_video_player.dart';
import '../presentation/screens/player/smarters_video_player.dart';

class PlayerService {
  static const String _playerKey = 'selected_player';

  /// Get the selected player preference
  static String getSelectedPlayer() {
    final storage = GetStorage();
    return storage.read(_playerKey) ?? 'player1';
  }

  /// Set the selected player preference
  static Future<void> setSelectedPlayer(String player) async {
    final storage = GetStorage();
    await storage.write(_playerKey, player);
  }

  /// Navigate to the appropriate player based on user preference
  static void openPlayer({
    required String videoUrl,
    required String title,
    bool isLive = false,
    List<String>? qualities,
    List<String>? audioTracks,
    List<String>? subtitleTracks,
  }) {
    final selectedPlayer = getSelectedPlayer();

    if (selectedPlayer == 'player2') {
      // Open Advanced Player (Smarters)
      Get.to(() => SmartersVideoPlayer(
            videoUrl: videoUrl,
            title: title,
            isLive: isLive,
            qualities: qualities,
            audioTracks: audioTracks,
            subtitleTracks: subtitleTracks,
          ));
    } else {
      // Open Enhanced Player (Default)
      Get.to(() => EnhancedVideoPlayer(
            videoUrl: videoUrl,
            title: title,
            isLive: isLive,
          ));
    }
  }

  /// Get player name for display
  static String getPlayerName(String playerKey) {
    switch (playerKey) {
      case 'player1':
        return 'مشغل 1';
      case 'player2':
        return 'مشغل 2';
      default:
        return 'مشغل 1';
    }
  }

  /// Get player description
  static String getPlayerDescription(String playerKey) {
    switch (playerKey) {
      case 'player1':
        return 'المشغل الأساسي مع الميزات الأساسية';
      case 'player2':
        return 'المشغل المتقدم مع جميع الميزات';
      default:
        return 'المشغل الأساسي مع الميزات الأساسية';
    }
  }
}
