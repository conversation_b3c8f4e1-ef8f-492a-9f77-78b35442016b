.class public final Ls6/A$a$c;
.super Ls6/A;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Ls6/A$a;->m([BLs6/w;II)Ls6/A;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation


# instance fields
.field public final synthetic a:Ls6/w;

.field public final synthetic b:I

.field public final synthetic c:[B

.field public final synthetic d:I


# direct methods
.method public constructor <init>(Ls6/w;I[BI)V
    .locals 0

    .line 1
    iput-object p1, p0, Ls6/A$a$c;->a:Ls6/w;

    .line 2
    .line 3
    iput p2, p0, Ls6/A$a$c;->b:I

    .line 4
    .line 5
    iput-object p3, p0, Ls6/A$a$c;->c:[B

    .line 6
    .line 7
    iput p4, p0, Ls6/A$a$c;->d:I

    .line 8
    .line 9
    invoke-direct {p0}, Ls6/A;-><init>()V

    .line 10
    .line 11
    .line 12
    return-void
.end method


# virtual methods
.method public contentLength()J
    .locals 2

    .line 1
    iget v0, p0, Ls6/A$a$c;->b:I

    .line 2
    .line 3
    int-to-long v0, v0

    .line 4
    return-wide v0
.end method

.method public contentType()Ls6/w;
    .locals 1

    .line 1
    iget-object v0, p0, Ls6/A$a$c;->a:Ls6/w;

    .line 2
    .line 3
    return-object v0
.end method

.method public writeTo(LG6/f;)V
    .locals 3

    .line 1
    const-string v0, "sink"

    .line 2
    .line 3
    invoke-static {p1, v0}, Lkotlin/jvm/internal/r;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    iget-object v0, p0, Ls6/A$a$c;->c:[B

    .line 7
    .line 8
    iget v1, p0, Ls6/A$a$c;->d:I

    .line 9
    .line 10
    iget v2, p0, Ls6/A$a$c;->b:I

    .line 11
    .line 12
    invoke-interface {p1, v0, v1, v2}, LG6/f;->c0([BII)LG6/f;

    .line 13
    .line 14
    .line 15
    return-void
.end method
