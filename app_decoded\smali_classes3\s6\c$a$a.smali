.class public final Ls6/c$a$a;
.super LG6/n;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Ls6/c$a;-><init>(Lv6/d$d;Ljava/lang/String;Ljava/lang/String;)V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation


# instance fields
.field public final synthetic a:LG6/Z;

.field public final synthetic b:Ls6/c$a;


# direct methods
.method public constructor <init>(LG6/Z;Ls6/c$a;)V
    .locals 0

    .line 1
    iput-object p1, p0, Ls6/c$a$a;->a:LG6/Z;

    .line 2
    .line 3
    iput-object p2, p0, Ls6/c$a$a;->b:Ls6/c$a;

    .line 4
    .line 5
    invoke-direct {p0, p1}, LG6/n;-><init>(LG6/Z;)V

    .line 6
    .line 7
    .line 8
    return-void
.end method


# virtual methods
.method public close()V
    .locals 1

    .line 1
    iget-object v0, p0, Ls6/c$a$a;->b:Ls6/c$a;

    .line 2
    .line 3
    invoke-virtual {v0}, Ls6/c$a;->b()Lv6/d$d;

    .line 4
    .line 5
    .line 6
    move-result-object v0

    .line 7
    invoke-virtual {v0}, Lv6/d$d;->close()V

    .line 8
    .line 9
    .line 10
    invoke-super {p0}, LG6/n;->close()V

    .line 11
    .line 12
    .line 13
    return-void
.end method
