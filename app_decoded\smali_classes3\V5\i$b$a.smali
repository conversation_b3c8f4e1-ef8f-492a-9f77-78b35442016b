.class public final LV5/i$b$a;
.super Lkotlin/jvm/internal/s;
.source "SourceFile"

# interfaces
.implements LM5/k;


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = LV5/i$b;->iterator()Ljava/util/Iterator;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation


# instance fields
.field public final synthetic a:LV5/i$b;


# direct methods
.method public constructor <init>(LV5/i$b;)V
    .locals 0

    .line 1
    iput-object p1, p0, LV5/i$b$a;->a:LV5/i$b;

    .line 2
    .line 3
    const/4 p1, 0x1

    .line 4
    invoke-direct {p0, p1}, Lkotlin/jvm/internal/s;-><init>(I)V

    .line 5
    .line 6
    .line 7
    return-void
.end method


# virtual methods
.method public final a(I)LV5/f;
    .locals 1

    .line 1
    iget-object v0, p0, LV5/i$b$a;->a:LV5/i$b;

    .line 2
    .line 3
    invoke-virtual {v0, p1}, LV5/i$b;->get(I)LV5/f;

    .line 4
    .line 5
    .line 6
    move-result-object p1

    .line 7
    return-object p1
.end method

.method public bridge synthetic invoke(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 0

    .line 1
    check-cast p1, Ljava/lang/Number;

    .line 2
    .line 3
    invoke-virtual {p1}, Ljava/lang/Number;->intValue()I

    .line 4
    .line 5
    .line 6
    move-result p1

    .line 7
    invoke-virtual {p0, p1}, LV5/i$b$a;->a(I)LV5/f;

    .line 8
    .line 9
    .line 10
    move-result-object p1

    .line 11
    return-object p1
.end method
