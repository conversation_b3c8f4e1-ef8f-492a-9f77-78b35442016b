.class public final LT5/n$a;
.super Ljava/lang/Object;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = LT5/n;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "a"
.end annotation


# direct methods
.method public constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public synthetic constructor <init>(Lkotlin/jvm/internal/j;)V
    .locals 0

    .line 1
    invoke-direct {p0}, LT5/n$a;-><init>()V

    return-void
.end method


# virtual methods
.method public final a(LT5/m;)LT5/n;
    .locals 2

    .line 1
    const-string v0, "type"

    .line 2
    .line 3
    invoke-static {p1, v0}, Lkotlin/jvm/internal/r;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    new-instance v0, LT5/n;

    .line 7
    .line 8
    sget-object v1, LT5/o;->a:LT5/o;

    .line 9
    .line 10
    invoke-direct {v0, v1, p1}, LT5/n;-><init>(LT5/o;LT5/m;)V

    .line 11
    .line 12
    .line 13
    return-object v0
.end method
