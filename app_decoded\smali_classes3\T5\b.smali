.class public interface abstract LT5/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements LT5/a;


# virtual methods
.method public varargs abstract call([Ljava/lang/Object;)Ljava/lang/Object;
.end method

.method public abstract callBy(Ljava/util/Map;)Ljava/lang/Object;
.end method

.method public abstract getName()Ljava/lang/String;
.end method

.method public abstract getParameters()Ljava/util/List;
.end method

.method public abstract getReturnType()LT5/m;
.end method

.method public abstract getTypeParameters()Ljava/util/List;
.end method

.method public abstract getVisibility()LT5/p;
.end method

.method public abstract isAbstract()Z
.end method

.method public abstract isFinal()Z
.end method

.method public abstract isOpen()Z
.end method
