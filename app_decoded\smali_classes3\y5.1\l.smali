.class public abstract Ly5/l;
.super Ly5/n;
.source "SourceFile"


# direct methods
.method public static bridge synthetic a(Lkotlin/jvm/functions/Function0;)Ly5/k;
    .locals 0

    .line 1
    invoke-static {p0}, Ly5/m;->a(Lkotlin/jvm/functions/Function0;)Ly5/k;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method

.method public static bridge synthetic b(Ly5/o;Lkotlin/jvm/functions/Function0;)Ly5/k;
    .locals 0

    .line 1
    invoke-static {p0, p1}, Ly5/m;->b(Ly5/o;Lkotlin/jvm/functions/Function0;)Ly5/k;

    .line 2
    .line 3
    .line 4
    move-result-object p0

    .line 5
    return-object p0
.end method
