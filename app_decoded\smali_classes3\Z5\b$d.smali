.class public final LZ5/b$d;
.super LF5/d;
.source "SourceFile"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = LZ5/b;->u0(LZ5/j;IJLD5/d;)Ljava/lang/Object;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = null
.end annotation


# instance fields
.field public a:Ljava/lang/Object;

.field public b:Ljava/lang/Object;

.field public c:I

.field public d:J

.field public synthetic e:Ljava/lang/Object;

.field public final synthetic f:LZ5/b;

.field public g:I


# direct methods
.method public constructor <init>(LZ5/b;LD5/d;)V
    .locals 0

    .line 1
    iput-object p1, p0, LZ5/b$d;->f:LZ5/b;

    .line 2
    .line 3
    invoke-direct {p0, p2}, LF5/d;-><init>(LD5/d;)V

    .line 4
    .line 5
    .line 6
    return-void
.end method


# virtual methods
.method public final invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
    .locals 6

    .line 1
    iput-object p1, p0, LZ5/b$d;->e:Ljava/lang/Object;

    .line 2
    .line 3
    iget p1, p0, LZ5/b$d;->g:I

    .line 4
    .line 5
    const/high16 v0, -0x80000000

    .line 6
    .line 7
    or-int/2addr p1, v0

    .line 8
    iput p1, p0, LZ5/b$d;->g:I

    .line 9
    .line 10
    iget-object v0, p0, LZ5/b$d;->f:LZ5/b;

    .line 11
    .line 12
    const/4 v2, 0x0

    .line 13
    const-wide/16 v3, 0x0

    .line 14
    .line 15
    const/4 v1, 0x0

    .line 16
    move-object v5, p0

    .line 17
    invoke-static/range {v0 .. v5}, LZ5/b;->v(LZ5/b;LZ5/j;IJLD5/d;)Ljava/lang/Object;

    .line 18
    .line 19
    .line 20
    move-result-object p1

    .line 21
    invoke-static {}, LE5/b;->e()Ljava/lang/Object;

    .line 22
    .line 23
    .line 24
    move-result-object v0

    .line 25
    if-ne p1, v0, :cond_0

    .line 26
    .line 27
    return-object p1

    .line 28
    :cond_0
    invoke-static {p1}, LZ5/h;->b(Ljava/lang/Object;)LZ5/h;

    .line 29
    .line 30
    .line 31
    move-result-object p1

    .line 32
    return-object p1
.end method
