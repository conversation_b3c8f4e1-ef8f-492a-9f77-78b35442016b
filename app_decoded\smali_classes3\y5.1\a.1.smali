.class public final Ly5/a;
.super Ljava/lang/Object;
.source "SourceFile"


# instance fields
.field public final a:LM5/p;


# direct methods
.method public constructor <init>(LM5/p;)V
    .locals 1

    .line 1
    const-string v0, "block"

    .line 2
    .line 3
    invoke-static {p1, v0}, <PERSON><PERSON><PERSON>/jvm/internal/r;->f(Ljava/lang/Object;Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    invoke-direct {p0}, L<PERSON><PERSON>/lang/Object;-><init>()V

    .line 7
    .line 8
    .line 9
    iput-object p1, p0, Ly5/a;->a:LM5/p;

    .line 10
    .line 11
    return-void
.end method


# virtual methods
.method public final a()LM5/p;
    .locals 1

    .line 1
    iget-object v0, p0, Ly5/a;->a:LM5/p;

    .line 2
    .line 3
    return-object v0
.end method
